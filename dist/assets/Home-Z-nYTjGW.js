import{j as e}from"./index-mQoYb2KJ.js";function t(){return e.jsx("p",{children:"©2025 by <PERSON><PERSON>"})}function r(){return e.jsx(e.Fragment,{children:e.jsxs("div",{className:"relative flex flex-col flex-1 lg:bg-[url('/img/background/Homepage_illustration_desktop.jpg')] md:bg-[url('/img/background/Homepage_illustration_tablet.png')] bg-[url('/img/background/Homepage_illustration_phone.png')] bg-cover bg-center",children:[e.jsxs("div",{className:"absolute sm:top-[110px] top-[130px] sm:left-[60px] left-10 sm:w-[371px] w-[219px] text-base",children:[e.jsx("p",{children:"I draw, design, write, make ceramics and I love creating new things wherever I go — all with a joyful heart."}),e.jsx("p",{children:"Welcome to my little world!"})]}),e.jsx("div",{className:"mt-auto text-center mb-4",children:e.jsx(t,{})})]})})}export{r as default};
