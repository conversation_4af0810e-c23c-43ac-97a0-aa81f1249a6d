import{r,j as e}from"./index-mQoYb2KJ.js";import{M as h}from"./ModalSlider-B_GfrwVy.js";import{F as u}from"./Footer-CuXHR_dz.js";const l=["1_Trina_with_Cow.jpg","3_JuliewithCow_BG3.jpg","4_An_birthday.jpg","5_Bi_Flower_FA.jpg","6_Truong_illustration.jpg","7_Chiangmai.jpg","8_MuiNe.jpg","9_PhuYen.jpg","10_Stella.jpg","11_ES_green.jpg","12_Uyen_Julie_Trina.jpg","13_Uyen_julie.jpg","14_duongchantroicoffee.jpg","15_Trung_wedding.jpg"],x=3,p=30;function j({openModal:c}){const[s,d]=r.useState(0),[i,n]=r.useState(326);r.useEffect(()=>{const m=(document.getElementById("LifeSlider").offsetWidth-p*(x-1))/x;n(m)},[]);const t=()=>{d(s+1!==l.length-2?s+1:0)},a=()=>{d(s-1<0?l.length-3:s-1)};return e.jsxs("div",{id:"LifeSlider",className:"relative w-full flex justify-center items-center overflow-hidden",children:[e.jsx("div",{className:"overflow-hidden",style:{width:`${x*i+(x-1)*p}px`},children:e.jsx("div",{className:"flex transition-transform ease-out duration-500",style:{gap:`${p}px`,width:`${l.length*i+(l.length-1)*p}px`,transform:`translateX(-${s*(i+p)}px)`},children:l.map((o,m)=>e.jsx("div",{className:"bg-cover bg-center aspect-square cursor-pointer",style:{width:`${i}px`,backgroundImage:`url(/img/life/square/${o})`},onClick:()=>c(m)},m))})}),e.jsx("button",{className:"xl:flex hidden absolute left-0 top-1/2 -translate-y-1/2 w-[50px] aspect-square bg-black items-center justify-center",onClick:a,children:e.jsx("img",{src:"/img/icons/white_slide_left.svg",alt:"white_slide_left",className:"w-[17px]"})}),e.jsx("button",{className:"xl:flex hidden absolute right-0 top-1/2 -translate-y-1/2 w-[50px] aspect-square bg-black items-center justify-center",onClick:t,children:e.jsx("img",{src:"/img/icons/white_slide_right.svg",alt:"white_slide_right",className:"w-[17px]"})})]})}const f=30,g=2;function _({imgs:c,openModal:s}){const[d,i]=r.useState(288);return r.useEffect(()=>{const t=(document.getElementById("LifeGrid").offsetWidth-f*(g-1))/g;i(t)},[]),e.jsx("div",{id:"LifeGrid",className:"lg:hidden sm:grid sm:grid-cols-2 hidden gap-5",style:{gap:`${f}px`},children:c.map((n,t)=>e.jsx("img",{src:`/img/life/square/${n}`,alt:t,onClick:()=>s(t),className:"aspect-square object-cover",style:{width:`${d}px`}},t))})}function y(){const[c,s]=r.useState(!1),[d,i]=r.useState(0),n=a=>{i(a),s(!0)},t=()=>{s(!1)};return e.jsx(e.Fragment,{children:e.jsxs("div",{className:"flex flex-1 flex-col items-center",children:[e.jsxs("div",{className:"flex flex-col items-start justify-center sm:gap-[30px] gap-5 lg:px-[60px] sm:px-[40px] sm:pb-[50px] sm:pt-[60px] py-[30px] px-10 w-full",children:[e.jsx("h3",{className:"sm:text-[32px] text-[21px] font-medium",children:"Capturing life"}),e.jsx("p",{className:"lg:w-1/2 sm:w-2/3 w-full",children:"To me, life is beautiful - even in ordinary moments. My heart fills with happiness through lines of poetry, and photos I capture while traveling and exploring new places"})]}),e.jsx("div",{className:"lg:block hidden w-full px-[60px]",children:e.jsx(j,{openModal:n})}),e.jsx("div",{className:"w-full px-[40px]",children:e.jsx(_,{imgs:l,openModal:n})}),e.jsx("div",{className:"sm:hidden flex flex-col gap-5",children:l.map((a,o)=>e.jsx("img",{className:"",src:`/img/life/full/${a}`,alt:o},o))}),c&&e.jsx(h,{index:d,images:l.map(a=>`/img/life/full/${a}`),onClose:t}),e.jsx("div",{className:"flex-1 mt-[60px] flex flex-col justify-end",children:e.jsx(u,{})})]})})}export{y as default};
