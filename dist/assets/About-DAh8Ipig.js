import{j as e,a as s,r}from"./index-mQoYb2KJ.js";import{G as l}from"./iconBase-CjHZMrX_.js";import{F as x}from"./Footer-CuXHR_dz.js";function o(t){return l({attr:{viewBox:"0 0 24 24"},child:[{tag:"path",attr:{d:"M4.97 13.22a.75.75 0 0 1 1.06 0L11 18.19V3.75a.75.75 0 0 1 1.5 0v14.44l4.97-4.97a.749.749 0 0 1 1.275.326.749.749 0 0 1-.215.734l-6.25 6.25a.75.75 0 0 1-1.06 0l-6.25-6.25a.75.75 0 0 1 0-1.06Z"},child:[]}]})(t)}function d(t){return l({attr:{viewBox:"0 0 24 24"},child:[{tag:"path",attr:{d:"M5.72 5.72a.75.75 0 0 1 1.06 0L12 10.94l5.22-5.22a.749.749 0 0 1 1.275.326.749.749 0 0 1-.215.734L13.06 12l5.22 5.22a.749.749 0 0 1-.326 1.275.749.749 0 0 1-.734-.215L12 13.06l-5.22 5.22a.751.751 0 0 1-1.042-.018.751.751 0 0 1-.018-1.042L10.94 12 5.72 6.78a.75.75 0 0 1 0-1.06Z"},child:[]}]})(t)}function a(){return e.jsxs("div",{className:"flex flex-col gap-5",children:[e.jsx("h1",{className:"text-[28px]",children:"Hi! Let's work together."}),e.jsxs("div",{className:"flex flex-col gap-[5px] sm:text-[17px] text-[16px] leading-[21px]",children:[e.jsx("p",{children:"I'm excited to work on:"}),e.jsxs("ul",{className:"list-disc ml-[30px]",children:[e.jsx("li",{children:"Children's Book Illustration"}),e.jsx("li",{children:"Picture Book Illustration"}),e.jsx("li",{children:"Editorial Illustration"}),e.jsx("li",{children:"Motion Graphics"}),e.jsx("li",{children:"Ceramic Arts"})]}),e.jsx("p",{children:"For commissions and project enquiries, please contact:"}),e.jsx("p",{className:"font-medium text-[21px] underline text-[#FF4B6F] overflow-hidden",children:"<EMAIL>"})]}),e.jsx("div",{className:"w-[52px]",children:e.jsx(s,{})}),e.jsx("p",{className:"sm:text-[17px] text-[16px] leading-[21px] italic font-inter",children:"I am happy to hear from you :)"})]})}function m(){const[t,i]=r.useState(!1),n=()=>{i(!1)},c=()=>{i(!0)};return e.jsxs(e.Fragment,{children:[e.jsx("button",{className:"w-20 aspect-square flex items-center justify-center bg-black rounded-full shadow-[6px_6px_6px_rgba(0,0,0,0.3)]",onClick:c,children:e.jsx("span",{className:"font-inter text-[16px] leading-tight text-white",children:"Contact me"})}),t&&e.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-80 flex flex-col items-center justify-center z-50 p-5",children:e.jsxs("div",{className:"relative bg-white h-fit",children:[e.jsx("button",{className:"absolute top-0 right-0 m-3",onClick:n,children:e.jsx(d,{className:"w-[60px] h-[60px]"})}),e.jsx("img",{src:"/img/contact/Contact_artist_left.jpg",alt:"Contact_artist_left"}),e.jsx("div",{className:"px-5 py-[30px]",children:e.jsx(a,{})})]})})]})}function u(){return e.jsxs("div",{className:"w-full flex flex-col justify-center items-center",children:[e.jsx("div",{className:"lg:bg-[url('/img/background/circle.jpg')] bg-contain bg-center sm:pt-[60px] pt-[30px] lg:px-[60px] px-[40px]",children:e.jsxs("div",{className:"sm:grid flex flex-col lg:grid-cols-3 grid-cols-2 gap-x-[30px] lg:gap-y-[30px] gap-y-[20px]",children:[e.jsxs("div",{className:"w-full sm:flex hidden flex-col lg:gap-[30px] gap-5",children:[e.jsx("img",{className:"object-cover w-full aspect-square",src:"/img/aboutme.png",alt:"me"}),e.jsx("span",{className:"italic text-[17px] leading-[21px]",children:`When I'm not drawing, I'll be in my studio making ceramics and crafts, playing the piano, and chasing after my two cute-but-naughty dogs, Bom and Bun."`})]}),e.jsx("img",{className:"sm:hidden block object-cover w-[100px] aspect-square",src:"/img/aboutme.png",alt:"me"}),e.jsxs("div",{className:"flex flex-col lg:gap-[10px] sm:gap-5 gap-[10px] lg:px-[30px] sm:text-[17px] text-[16px] sm:leading-[21px] leading-tight",children:[e.jsx("h1",{className:"lg:text-[28px] sm:text-[24px] text-[16px] font-normal",children:"Hello!"}),e.jsx("p",{children:"I'm Ngoc Vo, a Graphic Designer, Illustrator, and now, a rising ceramic artist too!"}),e.jsx("p",{children:"From a young age, I explored different art mediums like watercolor and gouache, participating in exciting art competitions (and even winning some little awards—what a thrill for a kid!)."}),e.jsx("p",{children:"I earned my Bachelor's degree in Architecture Design in 2010, but my heart led me to Graphic Design, where I worked full-time until late 2024. Now, I embrace the creative freedom of being an independent artist, focusing on Illustration and Graphic Design."}),e.jsx("p",{children:"My work blends both traditional and digital mediums, with storytelling at its core. I believe stories bring art to life, creating deeper connections with the audience."})]}),e.jsx("div",{className:"w-full h-full lg:hidden sm:flex hidden items-center",children:e.jsx("img",{src:"/img/contact/Contact_Bom_Bun.jpg",alt:"Contact_Bom_Bun"})}),e.jsxs("div",{className:"relative flex flex-col sm:gap-[30px] gap-5 h-fit sm:ring-[1px] sm:ring-black sm:mt-0 mt-[10px] sm:p-[30px] sm:pl-[60px] text-[17px] leading-[21px] lg:self-start self-center",children:[e.jsx("p",{className:"font-medium",children:"Recognition"}),e.jsxs("ul",{className:"list-disc",children:[e.jsx("li",{children:"2012 ASEAN-Korea Multimedia Competition"}),e.jsx("li",{children:'2014 Summer program "K-Pop Wave and ASEAN"'}),e.jsx("li",{children:"2014 Korea-ASEAN Exhibition in Jakarta"}),e.jsx("li",{children:"2024 2024 Media X Space: Across Asia Exhibition"})]}),e.jsx("div",{className:"absolute -right-[28px] -top-[26px] sm:hidden block",children:e.jsx(m,{})})]})]})}),e.jsxs("div",{className:"w-[400px] sm:flex hidden flex-col items-center justify-between lg:mt-[60px] mt-5 gap-[15px]",children:[e.jsx(s,{}),e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"flex items-center justify-center w-[58px] aspect-square",children:e.jsx(o,{className:"w-[45px] h-[45px]"})}),e.jsx("h1",{className:"text-[28px]",children:"Get in touch"})]}),e.jsx(s,{})]}),e.jsxs("div",{className:"lg:mt-10 mt-5 px-[60px] sm:grid hidden lg:grid-cols-3 grid-cols-2 gap-[30px]",children:[e.jsx("div",{className:"w-full h-full flex items-center",children:e.jsx("img",{src:"/img/contact/Contact_artist_left.jpg",alt:"Contact_artist_left"})}),e.jsx("div",{className:"w-full h-full flex items-center",children:e.jsx(a,{})}),e.jsx("div",{className:"w-full h-full lg:flex hidden items-center",children:e.jsx("img",{src:"/img/contact/Contact_Bom_Bun.jpg",alt:"Contact_Bom_Bun"})})]}),e.jsx("div",{className:"flex-1 lg:mt-[60px] sm:mt-10 mt-[30px] w-full",children:e.jsx(x,{})})]})}export{u as default};
