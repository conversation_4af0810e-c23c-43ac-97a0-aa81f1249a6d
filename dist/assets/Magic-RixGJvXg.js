import{j as e,L as x,r,A as u,m as g,u as f,a as b}from"./index-mQoYb2KJ.js";import{m as w}from"./magicCollection-DQ2xHN7e.js";import{G as m}from"./iconBase-CjHZMrX_.js";import{F as j}from"./Footer-CuXHR_dz.js";function v(s,n=.5){s=s.replace("#",""),s.length===3&&(s=s.split("").map(t=>t+t).join(""));const i=parseInt(s.substring(0,2),16),a=parseInt(s.substring(2,4),16),o=parseInt(s.substring(4,6),16);return`rgba(${i}, ${a}, ${o}, ${n})`}const y=({imageUrl:s,imageTitle:n,imageId:i,imageMainColor:a="#228B22"})=>e.jsxs(x,{to:`/magicDetail/${i}`,className:"relative group h-full w-full",children:[e.jsx("img",{src:s,alt:i,className:"object-cover h-full w-full",loading:"lazy"}),e.jsx("div",{className:"absolute inset-0 transition-opacity duration-300 opacity-0 group-hover:opacity-90",style:{backgroundColor:v(a)}})]});function k(){return e.jsx("div",{className:"w-full flex items-center justify-center aspect-square rounded-full bg-black shadow-[6px_6px_6px_rgba(0,0,0,0.3)]",children:e.jsx("img",{src:"/img/icons/arrow_down.svg",alt:"arrow_down",className:"w-[45%]"})})}function N(s){return m({attr:{viewBox:"0 0 1024 1024"},child:[{tag:"path",attr:{d:"M752.145 0c8.685 0 17.572 3.434 24.237 10.099 13.33 13.33 13.33 35.143 0 48.473L320.126 515.03l449.591 449.591c13.33 13.33 13.33 35.144 0 48.474-13.33 13.33-35.142 13.33-48.472 0L247.418 539.268c-13.33-13.33-13.33-35.144 0-48.474L727.91 10.1C734.575 3.435 743.46.002 752.146.002z"},child:[]}]})(s)}function I(s){return m({attr:{viewBox:"0 0 1024 1024"},child:[{tag:"path",attr:{d:"M271.653 1023.192c-8.685 0-17.573-3.432-24.238-10.097-13.33-13.33-13.33-35.144 0-48.474L703.67 508.163 254.08 58.573c-13.33-13.331-13.33-35.145 0-48.475 13.33-13.33 35.143-13.33 48.473 0L776.38 483.925c13.33 13.33 13.33 35.143 0 48.473l-480.492 480.694c-6.665 6.665-15.551 10.099-24.236 10.099z"},child:[]}]})(s)}function L(){return e.jsx("div",{className:"w-full flex items-center justify-center aspect-square rounded-full bg-black shadow-[6px_6px_6px_rgba(0,0,0,0.3)]",children:e.jsx("img",{src:"/img/icons/arrow_up.svg",alt:"arrow_up",className:"w-[45%]"})})}const S=({headRef:s})=>{const[n,i]=r.useState(!1),a=()=>{s.current.scrollIntoView({behavior:"smooth"})},o=t=>{t.target.scrollTop>t.target.clientHeight?i(!0):i(!1)};return r.useEffect(()=>{const t=document.querySelector("main");return t.addEventListener("scroll",o),()=>{t.removeEventListener("scroll",o)}},[]),e.jsx(u,{children:n&&e.jsx(g.button,{initial:{opacity:0,scale:.5},animate:{opacity:1,scale:1},exit:{opacity:0,scale:.5},transition:{duration:.3},onClick:a,className:"cursor-pointer w-full","aria-label":"Scroll to top",children:e.jsx(L,{})})})};function M(){const{navBarHeight:s}=f(),[n,i]=r.useState(0),[a,o]=r.useState(!1),t=r.useRef(null),c=r.useRef(null),p=()=>{t.current.scrollIntoView({behavior:"smooth"})},d=()=>{o(!a)};return r.useEffect(()=>{let l;window.innerWidth>1024?l=window.innerHeight-s:window.innerWidth<=1024&&window.innerWidth>834?l=557:l=470,i(l)},[s]),e.jsx(e.Fragment,{children:e.jsxs("div",{className:"relative flex flex-col items-center justify-center",children:[e.jsx("div",{ref:c,className:"absolute -top-[200px]"}),e.jsxs("div",{className:"sm:block hidden relative w-full sm:bg-[url('/img/background/Magic_castles.jpg')] bg-white bg-cover bg-center",style:{height:`${n}px`},children:[e.jsxs("div",{className:"xl:grid hidden grid-cols-3 px-[60px] pt-[85px] gap-[30px]",children:[e.jsxs("div",{className:"relative w-full bg-white bg-opacity-80 h-fit px-5 py-10 flex flex-col gap-[30px]",children:[e.jsx("h3",{className:"text-[32px] font-medium",children:"Magic World"}),e.jsx("div",{className:"w-[90px]",children:e.jsx(b,{})}),e.jsx("p",{className:"3xl:w-[426px] w-full text-[17px] leading-[21px]",children:"Fairy tales have sparked my imagination and inspired the themes of my artworks. I hope you can find hope and happiness through my illustrations and drawings."}),e.jsx("div",{onClick:d,className:`${a?"hidden":"block"} absolute top-5 right-5 aspect-square w-[50px] rounded-full border border-black flex items-center justify-center hover:bg-black hover:text-white hover:cursor-pointer transform duration-300`,children:e.jsx(I,{size:30})})]}),e.jsxs("div",{className:`${a?"flex":"hidden"} flex-col gap-[10px] relative w-full bg-white bg-opacity-80 text-base px-5 pb-10 pt-[90px]`,children:[e.jsx("p",{children:"Old folk stories always take me back to my childhood, making me feel like a time traveler."}),e.jsx("p",{children:"There, I can meet various characters like the kings and queens, princes and princesses, etc. living in majestic castles. I must admit every page of a fairy tale gives me a sense of wonder and magic. Despite not being a child anymore, I still find it enjoyable to be immersed in the fantasy world."}),e.jsx("p",{children:"Reading fairy stories and poems brings me nostalgic feelings and thought-provoking life lessons as well. I understand more about cultural and societal values, the world and the love around us. Fairy tales hold a special place in my heart. They always remind me to be gentle, kind, and strong no matter what adversity I have to face."}),e.jsx("div",{onClick:d,className:"absolute top-5 left-5 aspect-square w-[50px] rounded-full border border-black flex items-center justify-center hover:bg-black hover:text-white hover:cursor-pointer transform duration-300",children:e.jsx(N,{size:30})})]})]}),e.jsxs("div",{className:"xl:hidden sm:block hidden absolute left-[60px] top-10 w-[430px] text-[14px] leading-[21px]",children:[e.jsx("p",{children:"Old folk stories always take me back to my childhood, where I feel like a time traveler."}),e.jsx("p",{children:"There, I meet fictional characters—princes, princesses, and majestic castles that fill me with wonder and pure fantasy."}),e.jsx("p",{className:"mb-[5px]",children:"I see and feel the breathtaking beauty of ancient times, a world both thrilling and enchanting."}),e.jsx("p",{children:"Sometimes, I simply immerse myself in gentle stories and poems, where love shines through simple acts everyday life, bringing me comfort and hope in this beautiful world."}),e.jsx("p",{className:"mb-[5px]",children:"They teach me to be a better person, to do good and avoid evils. Be resilient, persevere, and spread love wherever I go."}),e.jsx("p",{className:"mb-[5px]",children:"Fairy tales set my imagination free!"})]}),e.jsxs("div",{onClick:p,className:"absolute right-[90px] bottom-10 cursor-pointer lg:w-20 w-[60px]",children:[e.jsx("p",{className:"absolute text-[24px] -top-10 right-1/2 transform translate-x-1/2 min-w-max font-inter",children:"see projects"}),e.jsx(k,{})]})]}),e.jsxs("div",{className:"sm:hidden block px-10 py-[30px]",children:[e.jsx("h3",{className:"text-[21px] mb-[10px]",children:"Magic World"}),e.jsx("p",{className:"text-[16px] leading-tight",children:"Old folk stories always take me back to my childhood, where I feel like a time traveler."}),e.jsx("p",{className:"text-[16px] mt-[10px]",children:"Fairy tales set my imagination free!"})]}),e.jsx("div",{className:"sm:my-[60px] sm:px-10 xl:px-[60px] p-0 w-full",ref:t,children:e.jsx("div",{className:"flex justify-center",children:e.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-3 sm:gap-[30px] gap-[10px] w-full",children:w.map((l,h)=>e.jsx("div",{className:"flex items-center justify-center w-full sm:h-auto h-[270px] sm:aspect-square",children:e.jsx(y,{imageUrl:l.priImg,imageTitle:l.title,imageId:l.id,imageMainColor:void 0})},h))})})}),e.jsx("div",{className:"mt-[60px]",children:e.jsx(j,{})}),e.jsx("div",{className:"fixed right-[90px] bottom-10 sm:block hidden lg:w-20 w-[60px]",children:e.jsx(S,{headRef:c})})]})})}export{M as default};
