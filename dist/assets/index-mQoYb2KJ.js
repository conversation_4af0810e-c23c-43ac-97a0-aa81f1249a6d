const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/Magic-RixGJvXg.js","assets/magicCollection-DQ2xHN7e.js","assets/iconBase-CjHZMrX_.js","assets/Footer-CuXHR_dz.js","assets/Life-j4mYaPCE.js","assets/ModalSlider-B_GfrwVy.js","assets/MagicDetail-CzDQd-cn.js","assets/MagicDetail-gSmPRW6h.css","assets/About-DAh8Ipig.js"])))=>i.map(i=>d[i]);
(function(){const i=document.createElement("link").relList;if(i&&i.supports&&i.supports("modulepreload"))return;for(const c of document.querySelectorAll('link[rel="modulepreload"]'))u(c);new MutationObserver(c=>{for(const f of c)if(f.type==="childList")for(const h of f.addedNodes)h.tagName==="LINK"&&h.rel==="modulepreload"&&u(h)}).observe(document,{childList:!0,subtree:!0});function s(c){const f={};return c.integrity&&(f.integrity=c.integrity),c.referrerPolicy&&(f.referrerPolicy=c.referrerPolicy),c.crossOrigin==="use-credentials"?f.credentials="include":c.crossOrigin==="anonymous"?f.credentials="omit":f.credentials="same-origin",f}function u(c){if(c.ep)return;c.ep=!0;const f=s(c);fetch(c.href,f)}})();const aS="modulepreload",iS=function(a){return"/"+a},np={},Ll=function(i,s,u){let c=Promise.resolve();if(s&&s.length>0){document.getElementsByTagName("link");const h=document.querySelector("meta[property=csp-nonce]"),p=(h==null?void 0:h.nonce)||(h==null?void 0:h.getAttribute("nonce"));c=Promise.allSettled(s.map(d=>{if(d=iS(d),d in np)return;np[d]=!0;const m=d.endsWith(".css"),g=m?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${d}"]${g}`))return;const S=document.createElement("link");if(S.rel=m?"stylesheet":aS,m||(S.as="script"),S.crossOrigin="",S.href=d,p&&S.setAttribute("nonce",p),document.head.appendChild(S),m)return new Promise((b,x)=>{S.addEventListener("load",b),S.addEventListener("error",()=>x(new Error(`Unable to preload CSS for ${d}`)))})}))}function f(h){const p=new Event("vite:preloadError",{cancelable:!0});if(p.payload=h,window.dispatchEvent(p),!p.defaultPrevented)throw h}return c.then(h=>{for(const p of h||[])p.status==="rejected"&&f(p.reason);return i().catch(f)})};function lS(a){return a&&a.__esModule&&Object.prototype.hasOwnProperty.call(a,"default")?a.default:a}var Xo={exports:{}},gl={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var ap;function sS(){if(ap)return gl;ap=1;var a=Symbol.for("react.transitional.element"),i=Symbol.for("react.fragment");function s(u,c,f){var h=null;if(f!==void 0&&(h=""+f),c.key!==void 0&&(h=""+c.key),"key"in c){f={};for(var p in c)p!=="key"&&(f[p]=c[p])}else f=c;return c=f.ref,{$$typeof:a,type:u,key:h,ref:c!==void 0?c:null,props:f}}return gl.Fragment=i,gl.jsx=s,gl.jsxs=s,gl}var ip;function uS(){return ip||(ip=1,Xo.exports=sS()),Xo.exports}var Y=uS(),Ko={exports:{}},vl={},Qo={exports:{}},Zo={};/**
 * @license React
 * scheduler.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var lp;function rS(){return lp||(lp=1,function(a){function i(K,$){var W=K.length;K.push($);t:for(;0<W;){var gt=W-1>>>1,E=K[gt];if(0<c(E,$))K[gt]=$,K[W]=E,W=gt;else break t}}function s(K){return K.length===0?null:K[0]}function u(K){if(K.length===0)return null;var $=K[0],W=K.pop();if(W!==$){K[0]=W;t:for(var gt=0,E=K.length,G=E>>>1;gt<G;){var it=2*(gt+1)-1,et=K[it],Q=it+1,dt=K[Q];if(0>c(et,W))Q<E&&0>c(dt,et)?(K[gt]=dt,K[Q]=W,gt=Q):(K[gt]=et,K[it]=W,gt=it);else if(Q<E&&0>c(dt,W))K[gt]=dt,K[Q]=W,gt=Q;else break t}}return $}function c(K,$){var W=K.sortIndex-$.sortIndex;return W!==0?W:K.id-$.id}if(a.unstable_now=void 0,typeof performance=="object"&&typeof performance.now=="function"){var f=performance;a.unstable_now=function(){return f.now()}}else{var h=Date,p=h.now();a.unstable_now=function(){return h.now()-p}}var d=[],m=[],g=1,S=null,b=3,x=!1,M=!1,z=!1,V=typeof setTimeout=="function"?setTimeout:null,O=typeof clearTimeout=="function"?clearTimeout:null,L=typeof setImmediate<"u"?setImmediate:null;function j(K){for(var $=s(m);$!==null;){if($.callback===null)u(m);else if($.startTime<=K)u(m),$.sortIndex=$.expirationTime,i(d,$);else break;$=s(m)}}function P(K){if(z=!1,j(K),!M)if(s(d)!==null)M=!0,be();else{var $=s(m);$!==null&&ne(P,$.startTime-K)}}var H=!1,k=-1,st=5,J=-1;function X(){return!(a.unstable_now()-J<st)}function tt(){if(H){var K=a.unstable_now();J=K;var $=!0;try{t:{M=!1,z&&(z=!1,O(k),k=-1),x=!0;var W=b;try{e:{for(j(K),S=s(d);S!==null&&!(S.expirationTime>K&&X());){var gt=S.callback;if(typeof gt=="function"){S.callback=null,b=S.priorityLevel;var E=gt(S.expirationTime<=K);if(K=a.unstable_now(),typeof E=="function"){S.callback=E,j(K),$=!0;break e}S===s(d)&&u(d),j(K)}else u(d);S=s(d)}if(S!==null)$=!0;else{var G=s(m);G!==null&&ne(P,G.startTime-K),$=!1}}break t}finally{S=null,b=W,x=!1}$=void 0}}finally{$?wt():H=!1}}}var wt;if(typeof L=="function")wt=function(){L(tt)};else if(typeof MessageChannel<"u"){var ve=new MessageChannel,Se=ve.port2;ve.port1.onmessage=tt,wt=function(){Se.postMessage(null)}}else wt=function(){V(tt,0)};function be(){H||(H=!0,wt())}function ne(K,$){k=V(function(){K(a.unstable_now())},$)}a.unstable_IdlePriority=5,a.unstable_ImmediatePriority=1,a.unstable_LowPriority=4,a.unstable_NormalPriority=3,a.unstable_Profiling=null,a.unstable_UserBlockingPriority=2,a.unstable_cancelCallback=function(K){K.callback=null},a.unstable_continueExecution=function(){M||x||(M=!0,be())},a.unstable_forceFrameRate=function(K){0>K||125<K?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):st=0<K?Math.floor(1e3/K):5},a.unstable_getCurrentPriorityLevel=function(){return b},a.unstable_getFirstCallbackNode=function(){return s(d)},a.unstable_next=function(K){switch(b){case 1:case 2:case 3:var $=3;break;default:$=b}var W=b;b=$;try{return K()}finally{b=W}},a.unstable_pauseExecution=function(){},a.unstable_requestPaint=function(){},a.unstable_runWithPriority=function(K,$){switch(K){case 1:case 2:case 3:case 4:case 5:break;default:K=3}var W=b;b=K;try{return $()}finally{b=W}},a.unstable_scheduleCallback=function(K,$,W){var gt=a.unstable_now();switch(typeof W=="object"&&W!==null?(W=W.delay,W=typeof W=="number"&&0<W?gt+W:gt):W=gt,K){case 1:var E=-1;break;case 2:E=250;break;case 5:E=1073741823;break;case 4:E=1e4;break;default:E=5e3}return E=W+E,K={id:g++,callback:$,priorityLevel:K,startTime:W,expirationTime:E,sortIndex:-1},W>gt?(K.sortIndex=W,i(m,K),s(d)===null&&K===s(m)&&(z?(O(k),k=-1):z=!0,ne(P,W-gt))):(K.sortIndex=E,i(d,K),M||x||(M=!0,be())),K},a.unstable_shouldYield=X,a.unstable_wrapCallback=function(K){var $=b;return function(){var W=b;b=$;try{return K.apply(this,arguments)}finally{b=W}}}}(Zo)),Zo}var sp;function oS(){return sp||(sp=1,Qo.exports=rS()),Qo.exports}var ko={exports:{}},ut={};/**
 * @license React
 * react.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var up;function cS(){if(up)return ut;up=1;var a=Symbol.for("react.transitional.element"),i=Symbol.for("react.portal"),s=Symbol.for("react.fragment"),u=Symbol.for("react.strict_mode"),c=Symbol.for("react.profiler"),f=Symbol.for("react.consumer"),h=Symbol.for("react.context"),p=Symbol.for("react.forward_ref"),d=Symbol.for("react.suspense"),m=Symbol.for("react.memo"),g=Symbol.for("react.lazy"),S=Symbol.iterator;function b(E){return E===null||typeof E!="object"?null:(E=S&&E[S]||E["@@iterator"],typeof E=="function"?E:null)}var x={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},M=Object.assign,z={};function V(E,G,it){this.props=E,this.context=G,this.refs=z,this.updater=it||x}V.prototype.isReactComponent={},V.prototype.setState=function(E,G){if(typeof E!="object"&&typeof E!="function"&&E!=null)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,E,G,"setState")},V.prototype.forceUpdate=function(E){this.updater.enqueueForceUpdate(this,E,"forceUpdate")};function O(){}O.prototype=V.prototype;function L(E,G,it){this.props=E,this.context=G,this.refs=z,this.updater=it||x}var j=L.prototype=new O;j.constructor=L,M(j,V.prototype),j.isPureReactComponent=!0;var P=Array.isArray,H={H:null,A:null,T:null,S:null},k=Object.prototype.hasOwnProperty;function st(E,G,it,et,Q,dt){return it=dt.ref,{$$typeof:a,type:E,key:G,ref:it!==void 0?it:null,props:dt}}function J(E,G){return st(E.type,G,void 0,void 0,void 0,E.props)}function X(E){return typeof E=="object"&&E!==null&&E.$$typeof===a}function tt(E){var G={"=":"=0",":":"=2"};return"$"+E.replace(/[=:]/g,function(it){return G[it]})}var wt=/\/+/g;function ve(E,G){return typeof E=="object"&&E!==null&&E.key!=null?tt(""+E.key):G.toString(36)}function Se(){}function be(E){switch(E.status){case"fulfilled":return E.value;case"rejected":throw E.reason;default:switch(typeof E.status=="string"?E.then(Se,Se):(E.status="pending",E.then(function(G){E.status==="pending"&&(E.status="fulfilled",E.value=G)},function(G){E.status==="pending"&&(E.status="rejected",E.reason=G)})),E.status){case"fulfilled":return E.value;case"rejected":throw E.reason}}throw E}function ne(E,G,it,et,Q){var dt=typeof E;(dt==="undefined"||dt==="boolean")&&(E=null);var rt=!1;if(E===null)rt=!0;else switch(dt){case"bigint":case"string":case"number":rt=!0;break;case"object":switch(E.$$typeof){case a:case i:rt=!0;break;case g:return rt=E._init,ne(rt(E._payload),G,it,et,Q)}}if(rt)return Q=Q(E),rt=et===""?"."+ve(E,0):et,P(Q)?(it="",rt!=null&&(it=rt.replace(wt,"$&/")+"/"),ne(Q,G,it,"",function(Lt){return Lt})):Q!=null&&(X(Q)&&(Q=J(Q,it+(Q.key==null||E&&E.key===Q.key?"":(""+Q.key).replace(wt,"$&/")+"/")+rt)),G.push(Q)),1;rt=0;var ae=et===""?".":et+":";if(P(E))for(var vt=0;vt<E.length;vt++)et=E[vt],dt=ae+ve(et,vt),rt+=ne(et,G,it,dt,Q);else if(vt=b(E),typeof vt=="function")for(E=vt.call(E),vt=0;!(et=E.next()).done;)et=et.value,dt=ae+ve(et,vt++),rt+=ne(et,G,it,dt,Q);else if(dt==="object"){if(typeof E.then=="function")return ne(be(E),G,it,et,Q);throw G=String(E),Error("Objects are not valid as a React child (found: "+(G==="[object Object]"?"object with keys {"+Object.keys(E).join(", ")+"}":G)+"). If you meant to render a collection of children, use an array instead.")}return rt}function K(E,G,it){if(E==null)return E;var et=[],Q=0;return ne(E,et,"","",function(dt){return G.call(it,dt,Q++)}),et}function $(E){if(E._status===-1){var G=E._result;G=G(),G.then(function(it){(E._status===0||E._status===-1)&&(E._status=1,E._result=it)},function(it){(E._status===0||E._status===-1)&&(E._status=2,E._result=it)}),E._status===-1&&(E._status=0,E._result=G)}if(E._status===1)return E._result.default;throw E._result}var W=typeof reportError=="function"?reportError:function(E){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var G=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof E=="object"&&E!==null&&typeof E.message=="string"?String(E.message):String(E),error:E});if(!window.dispatchEvent(G))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",E);return}console.error(E)};function gt(){}return ut.Children={map:K,forEach:function(E,G,it){K(E,function(){G.apply(this,arguments)},it)},count:function(E){var G=0;return K(E,function(){G++}),G},toArray:function(E){return K(E,function(G){return G})||[]},only:function(E){if(!X(E))throw Error("React.Children.only expected to receive a single React element child.");return E}},ut.Component=V,ut.Fragment=s,ut.Profiler=c,ut.PureComponent=L,ut.StrictMode=u,ut.Suspense=d,ut.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=H,ut.act=function(){throw Error("act(...) is not supported in production builds of React.")},ut.cache=function(E){return function(){return E.apply(null,arguments)}},ut.cloneElement=function(E,G,it){if(E==null)throw Error("The argument must be a React element, but you passed "+E+".");var et=M({},E.props),Q=E.key,dt=void 0;if(G!=null)for(rt in G.ref!==void 0&&(dt=void 0),G.key!==void 0&&(Q=""+G.key),G)!k.call(G,rt)||rt==="key"||rt==="__self"||rt==="__source"||rt==="ref"&&G.ref===void 0||(et[rt]=G[rt]);var rt=arguments.length-2;if(rt===1)et.children=it;else if(1<rt){for(var ae=Array(rt),vt=0;vt<rt;vt++)ae[vt]=arguments[vt+2];et.children=ae}return st(E.type,Q,void 0,void 0,dt,et)},ut.createContext=function(E){return E={$$typeof:h,_currentValue:E,_currentValue2:E,_threadCount:0,Provider:null,Consumer:null},E.Provider=E,E.Consumer={$$typeof:f,_context:E},E},ut.createElement=function(E,G,it){var et,Q={},dt=null;if(G!=null)for(et in G.key!==void 0&&(dt=""+G.key),G)k.call(G,et)&&et!=="key"&&et!=="__self"&&et!=="__source"&&(Q[et]=G[et]);var rt=arguments.length-2;if(rt===1)Q.children=it;else if(1<rt){for(var ae=Array(rt),vt=0;vt<rt;vt++)ae[vt]=arguments[vt+2];Q.children=ae}if(E&&E.defaultProps)for(et in rt=E.defaultProps,rt)Q[et]===void 0&&(Q[et]=rt[et]);return st(E,dt,void 0,void 0,null,Q)},ut.createRef=function(){return{current:null}},ut.forwardRef=function(E){return{$$typeof:p,render:E}},ut.isValidElement=X,ut.lazy=function(E){return{$$typeof:g,_payload:{_status:-1,_result:E},_init:$}},ut.memo=function(E,G){return{$$typeof:m,type:E,compare:G===void 0?null:G}},ut.startTransition=function(E){var G=H.T,it={};H.T=it;try{var et=E(),Q=H.S;Q!==null&&Q(it,et),typeof et=="object"&&et!==null&&typeof et.then=="function"&&et.then(gt,W)}catch(dt){W(dt)}finally{H.T=G}},ut.unstable_useCacheRefresh=function(){return H.H.useCacheRefresh()},ut.use=function(E){return H.H.use(E)},ut.useActionState=function(E,G,it){return H.H.useActionState(E,G,it)},ut.useCallback=function(E,G){return H.H.useCallback(E,G)},ut.useContext=function(E){return H.H.useContext(E)},ut.useDebugValue=function(){},ut.useDeferredValue=function(E,G){return H.H.useDeferredValue(E,G)},ut.useEffect=function(E,G){return H.H.useEffect(E,G)},ut.useId=function(){return H.H.useId()},ut.useImperativeHandle=function(E,G,it){return H.H.useImperativeHandle(E,G,it)},ut.useInsertionEffect=function(E,G){return H.H.useInsertionEffect(E,G)},ut.useLayoutEffect=function(E,G){return H.H.useLayoutEffect(E,G)},ut.useMemo=function(E,G){return H.H.useMemo(E,G)},ut.useOptimistic=function(E,G){return H.H.useOptimistic(E,G)},ut.useReducer=function(E,G,it){return H.H.useReducer(E,G,it)},ut.useRef=function(E){return H.H.useRef(E)},ut.useState=function(E){return H.H.useState(E)},ut.useSyncExternalStore=function(E,G,it){return H.H.useSyncExternalStore(E,G,it)},ut.useTransition=function(){return H.H.useTransition()},ut.version="19.0.0",ut}var rp;function Oc(){return rp||(rp=1,ko.exports=cS()),ko.exports}var Po={exports:{}},It={};/**
 * @license React
 * react-dom.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var op;function fS(){if(op)return It;op=1;var a=Oc();function i(d){var m="https://react.dev/errors/"+d;if(1<arguments.length){m+="?args[]="+encodeURIComponent(arguments[1]);for(var g=2;g<arguments.length;g++)m+="&args[]="+encodeURIComponent(arguments[g])}return"Minified React error #"+d+"; visit "+m+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function s(){}var u={d:{f:s,r:function(){throw Error(i(522))},D:s,C:s,L:s,m:s,X:s,S:s,M:s},p:0,findDOMNode:null},c=Symbol.for("react.portal");function f(d,m,g){var S=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:c,key:S==null?null:""+S,children:d,containerInfo:m,implementation:g}}var h=a.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function p(d,m){if(d==="font")return"";if(typeof m=="string")return m==="use-credentials"?m:""}return It.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=u,It.createPortal=function(d,m){var g=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!m||m.nodeType!==1&&m.nodeType!==9&&m.nodeType!==11)throw Error(i(299));return f(d,m,null,g)},It.flushSync=function(d){var m=h.T,g=u.p;try{if(h.T=null,u.p=2,d)return d()}finally{h.T=m,u.p=g,u.d.f()}},It.preconnect=function(d,m){typeof d=="string"&&(m?(m=m.crossOrigin,m=typeof m=="string"?m==="use-credentials"?m:"":void 0):m=null,u.d.C(d,m))},It.prefetchDNS=function(d){typeof d=="string"&&u.d.D(d)},It.preinit=function(d,m){if(typeof d=="string"&&m&&typeof m.as=="string"){var g=m.as,S=p(g,m.crossOrigin),b=typeof m.integrity=="string"?m.integrity:void 0,x=typeof m.fetchPriority=="string"?m.fetchPriority:void 0;g==="style"?u.d.S(d,typeof m.precedence=="string"?m.precedence:void 0,{crossOrigin:S,integrity:b,fetchPriority:x}):g==="script"&&u.d.X(d,{crossOrigin:S,integrity:b,fetchPriority:x,nonce:typeof m.nonce=="string"?m.nonce:void 0})}},It.preinitModule=function(d,m){if(typeof d=="string")if(typeof m=="object"&&m!==null){if(m.as==null||m.as==="script"){var g=p(m.as,m.crossOrigin);u.d.M(d,{crossOrigin:g,integrity:typeof m.integrity=="string"?m.integrity:void 0,nonce:typeof m.nonce=="string"?m.nonce:void 0})}}else m==null&&u.d.M(d)},It.preload=function(d,m){if(typeof d=="string"&&typeof m=="object"&&m!==null&&typeof m.as=="string"){var g=m.as,S=p(g,m.crossOrigin);u.d.L(d,g,{crossOrigin:S,integrity:typeof m.integrity=="string"?m.integrity:void 0,nonce:typeof m.nonce=="string"?m.nonce:void 0,type:typeof m.type=="string"?m.type:void 0,fetchPriority:typeof m.fetchPriority=="string"?m.fetchPriority:void 0,referrerPolicy:typeof m.referrerPolicy=="string"?m.referrerPolicy:void 0,imageSrcSet:typeof m.imageSrcSet=="string"?m.imageSrcSet:void 0,imageSizes:typeof m.imageSizes=="string"?m.imageSizes:void 0,media:typeof m.media=="string"?m.media:void 0})}},It.preloadModule=function(d,m){if(typeof d=="string")if(m){var g=p(m.as,m.crossOrigin);u.d.m(d,{as:typeof m.as=="string"&&m.as!=="script"?m.as:void 0,crossOrigin:g,integrity:typeof m.integrity=="string"?m.integrity:void 0})}else u.d.m(d)},It.requestFormReset=function(d){u.d.r(d)},It.unstable_batchedUpdates=function(d,m){return d(m)},It.useFormState=function(d,m,g){return h.H.useFormState(d,m,g)},It.useFormStatus=function(){return h.H.useHostTransitionStatus()},It.version="19.0.0",It}var cp;function hS(){if(cp)return Po.exports;cp=1;function a(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(a)}catch(i){console.error(i)}}return a(),Po.exports=fS(),Po.exports}/**
 * @license React
 * react-dom-client.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var fp;function dS(){if(fp)return vl;fp=1;var a=oS(),i=Oc(),s=hS();function u(t){var e="https://react.dev/errors/"+t;if(1<arguments.length){e+="?args[]="+encodeURIComponent(arguments[1]);for(var n=2;n<arguments.length;n++)e+="&args[]="+encodeURIComponent(arguments[n])}return"Minified React error #"+t+"; visit "+e+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function c(t){return!(!t||t.nodeType!==1&&t.nodeType!==9&&t.nodeType!==11)}var f=Symbol.for("react.element"),h=Symbol.for("react.transitional.element"),p=Symbol.for("react.portal"),d=Symbol.for("react.fragment"),m=Symbol.for("react.strict_mode"),g=Symbol.for("react.profiler"),S=Symbol.for("react.provider"),b=Symbol.for("react.consumer"),x=Symbol.for("react.context"),M=Symbol.for("react.forward_ref"),z=Symbol.for("react.suspense"),V=Symbol.for("react.suspense_list"),O=Symbol.for("react.memo"),L=Symbol.for("react.lazy"),j=Symbol.for("react.offscreen"),P=Symbol.for("react.memo_cache_sentinel"),H=Symbol.iterator;function k(t){return t===null||typeof t!="object"?null:(t=H&&t[H]||t["@@iterator"],typeof t=="function"?t:null)}var st=Symbol.for("react.client.reference");function J(t){if(t==null)return null;if(typeof t=="function")return t.$$typeof===st?null:t.displayName||t.name||null;if(typeof t=="string")return t;switch(t){case d:return"Fragment";case p:return"Portal";case g:return"Profiler";case m:return"StrictMode";case z:return"Suspense";case V:return"SuspenseList"}if(typeof t=="object")switch(t.$$typeof){case x:return(t.displayName||"Context")+".Provider";case b:return(t._context.displayName||"Context")+".Consumer";case M:var e=t.render;return t=t.displayName,t||(t=e.displayName||e.name||"",t=t!==""?"ForwardRef("+t+")":"ForwardRef"),t;case O:return e=t.displayName||null,e!==null?e:J(t.type)||"Memo";case L:e=t._payload,t=t._init;try{return J(t(e))}catch{}}return null}var X=i.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,tt=Object.assign,wt,ve;function Se(t){if(wt===void 0)try{throw Error()}catch(n){var e=n.stack.trim().match(/\n( *(at )?)/);wt=e&&e[1]||"",ve=-1<n.stack.indexOf(`
    at`)?" (<anonymous>)":-1<n.stack.indexOf("@")?"@unknown:0:0":""}return`
`+wt+t+ve}var be=!1;function ne(t,e){if(!t||be)return"";be=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var l={DetermineComponentFrameRoot:function(){try{if(e){var q=function(){throw Error()};if(Object.defineProperty(q.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(q,[])}catch(U){var _=U}Reflect.construct(t,[],q)}else{try{q.call()}catch(U){_=U}t.call(q.prototype)}}else{try{throw Error()}catch(U){_=U}(q=t())&&typeof q.catch=="function"&&q.catch(function(){})}}catch(U){if(U&&_&&typeof U.stack=="string")return[U.stack,_.stack]}return[null,null]}};l.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var r=Object.getOwnPropertyDescriptor(l.DetermineComponentFrameRoot,"name");r&&r.configurable&&Object.defineProperty(l.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var o=l.DetermineComponentFrameRoot(),y=o[0],v=o[1];if(y&&v){var T=y.split(`
`),D=v.split(`
`);for(r=l=0;l<T.length&&!T[l].includes("DetermineComponentFrameRoot");)l++;for(;r<D.length&&!D[r].includes("DetermineComponentFrameRoot");)r++;if(l===T.length||r===D.length)for(l=T.length-1,r=D.length-1;1<=l&&0<=r&&T[l]!==D[r];)r--;for(;1<=l&&0<=r;l--,r--)if(T[l]!==D[r]){if(l!==1||r!==1)do if(l--,r--,0>r||T[l]!==D[r]){var B=`
`+T[l].replace(" at new "," at ");return t.displayName&&B.includes("<anonymous>")&&(B=B.replace("<anonymous>",t.displayName)),B}while(1<=l&&0<=r);break}}}finally{be=!1,Error.prepareStackTrace=n}return(n=t?t.displayName||t.name:"")?Se(n):""}function K(t){switch(t.tag){case 26:case 27:case 5:return Se(t.type);case 16:return Se("Lazy");case 13:return Se("Suspense");case 19:return Se("SuspenseList");case 0:case 15:return t=ne(t.type,!1),t;case 11:return t=ne(t.type.render,!1),t;case 1:return t=ne(t.type,!0),t;default:return""}}function $(t){try{var e="";do e+=K(t),t=t.return;while(t);return e}catch(n){return`
Error generating stack: `+n.message+`
`+n.stack}}function W(t){var e=t,n=t;if(t.alternate)for(;e.return;)e=e.return;else{t=e;do e=t,(e.flags&4098)!==0&&(n=e.return),t=e.return;while(t)}return e.tag===3?n:null}function gt(t){if(t.tag===13){var e=t.memoizedState;if(e===null&&(t=t.alternate,t!==null&&(e=t.memoizedState)),e!==null)return e.dehydrated}return null}function E(t){if(W(t)!==t)throw Error(u(188))}function G(t){var e=t.alternate;if(!e){if(e=W(t),e===null)throw Error(u(188));return e!==t?null:t}for(var n=t,l=e;;){var r=n.return;if(r===null)break;var o=r.alternate;if(o===null){if(l=r.return,l!==null){n=l;continue}break}if(r.child===o.child){for(o=r.child;o;){if(o===n)return E(r),t;if(o===l)return E(r),e;o=o.sibling}throw Error(u(188))}if(n.return!==l.return)n=r,l=o;else{for(var y=!1,v=r.child;v;){if(v===n){y=!0,n=r,l=o;break}if(v===l){y=!0,l=r,n=o;break}v=v.sibling}if(!y){for(v=o.child;v;){if(v===n){y=!0,n=o,l=r;break}if(v===l){y=!0,l=o,n=r;break}v=v.sibling}if(!y)throw Error(u(189))}}if(n.alternate!==l)throw Error(u(190))}if(n.tag!==3)throw Error(u(188));return n.stateNode.current===n?t:e}function it(t){var e=t.tag;if(e===5||e===26||e===27||e===6)return t;for(t=t.child;t!==null;){if(e=it(t),e!==null)return e;t=t.sibling}return null}var et=Array.isArray,Q=s.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,dt={pending:!1,data:null,method:null,action:null},rt=[],ae=-1;function vt(t){return{current:t}}function Lt(t){0>ae||(t.current=rt[ae],rt[ae]=null,ae--)}function Rt(t,e){ae++,rt[ae]=t.current,t.current=e}var Ye=vt(null),bi=vt(null),bn=vt(null),Ql=vt(null);function Zl(t,e){switch(Rt(bn,e),Rt(bi,t),Rt(Ye,null),t=e.nodeType,t){case 9:case 11:e=(e=e.documentElement)&&(e=e.namespaceURI)?_m(e):0;break;default:if(t=t===8?e.parentNode:e,e=t.tagName,t=t.namespaceURI)t=_m(t),e=Vm(t,e);else switch(e){case"svg":e=1;break;case"math":e=2;break;default:e=0}}Lt(Ye),Rt(Ye,e)}function Aa(){Lt(Ye),Lt(bi),Lt(bn)}function zu(t){t.memoizedState!==null&&Rt(Ql,t);var e=Ye.current,n=Vm(e,t.type);e!==n&&(Rt(bi,t),Rt(Ye,n))}function kl(t){bi.current===t&&(Lt(Ye),Lt(bi)),Ql.current===t&&(Lt(Ql),hl._currentValue=dt)}var Uu=Object.prototype.hasOwnProperty,Bu=a.unstable_scheduleCallback,ju=a.unstable_cancelCallback,Bv=a.unstable_shouldYield,jv=a.unstable_requestPaint,Ge=a.unstable_now,Lv=a.unstable_getCurrentPriorityLevel,bf=a.unstable_ImmediatePriority,Tf=a.unstable_UserBlockingPriority,Pl=a.unstable_NormalPriority,Nv=a.unstable_LowPriority,xf=a.unstable_IdlePriority,Hv=a.log,qv=a.unstable_setDisableYieldValue,Ti=null,ce=null;function Yv(t){if(ce&&typeof ce.onCommitFiberRoot=="function")try{ce.onCommitFiberRoot(Ti,t,void 0,(t.current.flags&128)===128)}catch{}}function Tn(t){if(typeof Hv=="function"&&qv(t),ce&&typeof ce.setStrictMode=="function")try{ce.setStrictMode(Ti,t)}catch{}}var fe=Math.clz32?Math.clz32:Kv,Gv=Math.log,Xv=Math.LN2;function Kv(t){return t>>>=0,t===0?32:31-(Gv(t)/Xv|0)|0}var Fl=128,Jl=4194304;function Fn(t){var e=t&42;if(e!==0)return e;switch(t&-t){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t&4194176;case 4194304:case 8388608:case 16777216:case 33554432:return t&62914560;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return t}}function $l(t,e){var n=t.pendingLanes;if(n===0)return 0;var l=0,r=t.suspendedLanes,o=t.pingedLanes,y=t.warmLanes;t=t.finishedLanes!==0;var v=n&134217727;return v!==0?(n=v&~r,n!==0?l=Fn(n):(o&=v,o!==0?l=Fn(o):t||(y=v&~y,y!==0&&(l=Fn(y))))):(v=n&~r,v!==0?l=Fn(v):o!==0?l=Fn(o):t||(y=n&~y,y!==0&&(l=Fn(y)))),l===0?0:e!==0&&e!==l&&(e&r)===0&&(r=l&-l,y=e&-e,r>=y||r===32&&(y&4194176)!==0)?e:l}function xi(t,e){return(t.pendingLanes&~(t.suspendedLanes&~t.pingedLanes)&e)===0}function Qv(t,e){switch(t){case 1:case 2:case 4:case 8:return e+250;case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e+5e3;case 4194304:case 8388608:case 16777216:case 33554432:return-1;case 67108864:case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Ef(){var t=Fl;return Fl<<=1,(Fl&4194176)===0&&(Fl=128),t}function Af(){var t=Jl;return Jl<<=1,(Jl&62914560)===0&&(Jl=4194304),t}function Lu(t){for(var e=[],n=0;31>n;n++)e.push(t);return e}function Ei(t,e){t.pendingLanes|=e,e!==268435456&&(t.suspendedLanes=0,t.pingedLanes=0,t.warmLanes=0)}function Zv(t,e,n,l,r,o){var y=t.pendingLanes;t.pendingLanes=n,t.suspendedLanes=0,t.pingedLanes=0,t.warmLanes=0,t.expiredLanes&=n,t.entangledLanes&=n,t.errorRecoveryDisabledLanes&=n,t.shellSuspendCounter=0;var v=t.entanglements,T=t.expirationTimes,D=t.hiddenUpdates;for(n=y&~n;0<n;){var B=31-fe(n),q=1<<B;v[B]=0,T[B]=-1;var _=D[B];if(_!==null)for(D[B]=null,B=0;B<_.length;B++){var U=_[B];U!==null&&(U.lane&=-536870913)}n&=~q}l!==0&&Rf(t,l,0),o!==0&&r===0&&t.tag!==0&&(t.suspendedLanes|=o&~(y&~e))}function Rf(t,e,n){t.pendingLanes|=e,t.suspendedLanes&=~e;var l=31-fe(e);t.entangledLanes|=e,t.entanglements[l]=t.entanglements[l]|1073741824|n&4194218}function Df(t,e){var n=t.entangledLanes|=e;for(t=t.entanglements;n;){var l=31-fe(n),r=1<<l;r&e|t[l]&e&&(t[l]|=e),n&=~r}}function Mf(t){return t&=-t,2<t?8<t?(t&134217727)!==0?32:268435456:8:2}function Cf(){var t=Q.p;return t!==0?t:(t=window.event,t===void 0?32:Jm(t.type))}function kv(t,e){var n=Q.p;try{return Q.p=t,e()}finally{Q.p=n}}var xn=Math.random().toString(36).slice(2),$t="__reactFiber$"+xn,ue="__reactProps$"+xn,Ra="__reactContainer$"+xn,Nu="__reactEvents$"+xn,Pv="__reactListeners$"+xn,Fv="__reactHandles$"+xn,Of="__reactResources$"+xn,Ai="__reactMarker$"+xn;function Hu(t){delete t[$t],delete t[ue],delete t[Nu],delete t[Pv],delete t[Fv]}function Jn(t){var e=t[$t];if(e)return e;for(var n=t.parentNode;n;){if(e=n[Ra]||n[$t]){if(n=e.alternate,e.child!==null||n!==null&&n.child!==null)for(t=Bm(t);t!==null;){if(n=t[$t])return n;t=Bm(t)}return e}t=n,n=t.parentNode}return null}function Da(t){if(t=t[$t]||t[Ra]){var e=t.tag;if(e===5||e===6||e===13||e===26||e===27||e===3)return t}return null}function Ri(t){var e=t.tag;if(e===5||e===26||e===27||e===6)return t.stateNode;throw Error(u(33))}function Ma(t){var e=t[Of];return e||(e=t[Of]={hoistableStyles:new Map,hoistableScripts:new Map}),e}function Xt(t){t[Ai]=!0}var wf=new Set,_f={};function $n(t,e){Ca(t,e),Ca(t+"Capture",e)}function Ca(t,e){for(_f[t]=e,t=0;t<e.length;t++)wf.add(e[t])}var We=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),Jv=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),Vf={},zf={};function $v(t){return Uu.call(zf,t)?!0:Uu.call(Vf,t)?!1:Jv.test(t)?zf[t]=!0:(Vf[t]=!0,!1)}function Wl(t,e,n){if($v(e))if(n===null)t.removeAttribute(e);else{switch(typeof n){case"undefined":case"function":case"symbol":t.removeAttribute(e);return;case"boolean":var l=e.toLowerCase().slice(0,5);if(l!=="data-"&&l!=="aria-"){t.removeAttribute(e);return}}t.setAttribute(e,""+n)}}function Il(t,e,n){if(n===null)t.removeAttribute(e);else{switch(typeof n){case"undefined":case"function":case"symbol":case"boolean":t.removeAttribute(e);return}t.setAttribute(e,""+n)}}function Ie(t,e,n,l){if(l===null)t.removeAttribute(n);else{switch(typeof l){case"undefined":case"function":case"symbol":case"boolean":t.removeAttribute(n);return}t.setAttributeNS(e,n,""+l)}}function Te(t){switch(typeof t){case"bigint":case"boolean":case"number":case"string":case"undefined":return t;case"object":return t;default:return""}}function Uf(t){var e=t.type;return(t=t.nodeName)&&t.toLowerCase()==="input"&&(e==="checkbox"||e==="radio")}function Wv(t){var e=Uf(t)?"checked":"value",n=Object.getOwnPropertyDescriptor(t.constructor.prototype,e),l=""+t[e];if(!t.hasOwnProperty(e)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var r=n.get,o=n.set;return Object.defineProperty(t,e,{configurable:!0,get:function(){return r.call(this)},set:function(y){l=""+y,o.call(this,y)}}),Object.defineProperty(t,e,{enumerable:n.enumerable}),{getValue:function(){return l},setValue:function(y){l=""+y},stopTracking:function(){t._valueTracker=null,delete t[e]}}}}function ts(t){t._valueTracker||(t._valueTracker=Wv(t))}function Bf(t){if(!t)return!1;var e=t._valueTracker;if(!e)return!0;var n=e.getValue(),l="";return t&&(l=Uf(t)?t.checked?"true":"false":t.value),t=l,t!==n?(e.setValue(t),!0):!1}function es(t){if(t=t||(typeof document<"u"?document:void 0),typeof t>"u")return null;try{return t.activeElement||t.body}catch{return t.body}}var Iv=/[\n"\\]/g;function xe(t){return t.replace(Iv,function(e){return"\\"+e.charCodeAt(0).toString(16)+" "})}function qu(t,e,n,l,r,o,y,v){t.name="",y!=null&&typeof y!="function"&&typeof y!="symbol"&&typeof y!="boolean"?t.type=y:t.removeAttribute("type"),e!=null?y==="number"?(e===0&&t.value===""||t.value!=e)&&(t.value=""+Te(e)):t.value!==""+Te(e)&&(t.value=""+Te(e)):y!=="submit"&&y!=="reset"||t.removeAttribute("value"),e!=null?Yu(t,y,Te(e)):n!=null?Yu(t,y,Te(n)):l!=null&&t.removeAttribute("value"),r==null&&o!=null&&(t.defaultChecked=!!o),r!=null&&(t.checked=r&&typeof r!="function"&&typeof r!="symbol"),v!=null&&typeof v!="function"&&typeof v!="symbol"&&typeof v!="boolean"?t.name=""+Te(v):t.removeAttribute("name")}function jf(t,e,n,l,r,o,y,v){if(o!=null&&typeof o!="function"&&typeof o!="symbol"&&typeof o!="boolean"&&(t.type=o),e!=null||n!=null){if(!(o!=="submit"&&o!=="reset"||e!=null))return;n=n!=null?""+Te(n):"",e=e!=null?""+Te(e):n,v||e===t.value||(t.value=e),t.defaultValue=e}l=l??r,l=typeof l!="function"&&typeof l!="symbol"&&!!l,t.checked=v?t.checked:!!l,t.defaultChecked=!!l,y!=null&&typeof y!="function"&&typeof y!="symbol"&&typeof y!="boolean"&&(t.name=y)}function Yu(t,e,n){e==="number"&&es(t.ownerDocument)===t||t.defaultValue===""+n||(t.defaultValue=""+n)}function Oa(t,e,n,l){if(t=t.options,e){e={};for(var r=0;r<n.length;r++)e["$"+n[r]]=!0;for(n=0;n<t.length;n++)r=e.hasOwnProperty("$"+t[n].value),t[n].selected!==r&&(t[n].selected=r),r&&l&&(t[n].defaultSelected=!0)}else{for(n=""+Te(n),e=null,r=0;r<t.length;r++){if(t[r].value===n){t[r].selected=!0,l&&(t[r].defaultSelected=!0);return}e!==null||t[r].disabled||(e=t[r])}e!==null&&(e.selected=!0)}}function Lf(t,e,n){if(e!=null&&(e=""+Te(e),e!==t.value&&(t.value=e),n==null)){t.defaultValue!==e&&(t.defaultValue=e);return}t.defaultValue=n!=null?""+Te(n):""}function Nf(t,e,n,l){if(e==null){if(l!=null){if(n!=null)throw Error(u(92));if(et(l)){if(1<l.length)throw Error(u(93));l=l[0]}n=l}n==null&&(n=""),e=n}n=Te(e),t.defaultValue=n,l=t.textContent,l===n&&l!==""&&l!==null&&(t.value=l)}function wa(t,e){if(e){var n=t.firstChild;if(n&&n===t.lastChild&&n.nodeType===3){n.nodeValue=e;return}}t.textContent=e}var t0=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function Hf(t,e,n){var l=e.indexOf("--")===0;n==null||typeof n=="boolean"||n===""?l?t.setProperty(e,""):e==="float"?t.cssFloat="":t[e]="":l?t.setProperty(e,n):typeof n!="number"||n===0||t0.has(e)?e==="float"?t.cssFloat=n:t[e]=(""+n).trim():t[e]=n+"px"}function qf(t,e,n){if(e!=null&&typeof e!="object")throw Error(u(62));if(t=t.style,n!=null){for(var l in n)!n.hasOwnProperty(l)||e!=null&&e.hasOwnProperty(l)||(l.indexOf("--")===0?t.setProperty(l,""):l==="float"?t.cssFloat="":t[l]="");for(var r in e)l=e[r],e.hasOwnProperty(r)&&n[r]!==l&&Hf(t,r,l)}else for(var o in e)e.hasOwnProperty(o)&&Hf(t,o,e[o])}function Gu(t){if(t.indexOf("-")===-1)return!1;switch(t){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var e0=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),n0=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function ns(t){return n0.test(""+t)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":t}var Xu=null;function Ku(t){return t=t.target||t.srcElement||window,t.correspondingUseElement&&(t=t.correspondingUseElement),t.nodeType===3?t.parentNode:t}var _a=null,Va=null;function Yf(t){var e=Da(t);if(e&&(t=e.stateNode)){var n=t[ue]||null;t:switch(t=e.stateNode,e.type){case"input":if(qu(t,n.value,n.defaultValue,n.defaultValue,n.checked,n.defaultChecked,n.type,n.name),e=n.name,n.type==="radio"&&e!=null){for(n=t;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll('input[name="'+xe(""+e)+'"][type="radio"]'),e=0;e<n.length;e++){var l=n[e];if(l!==t&&l.form===t.form){var r=l[ue]||null;if(!r)throw Error(u(90));qu(l,r.value,r.defaultValue,r.defaultValue,r.checked,r.defaultChecked,r.type,r.name)}}for(e=0;e<n.length;e++)l=n[e],l.form===t.form&&Bf(l)}break t;case"textarea":Lf(t,n.value,n.defaultValue);break t;case"select":e=n.value,e!=null&&Oa(t,!!n.multiple,e,!1)}}}var Qu=!1;function Gf(t,e,n){if(Qu)return t(e,n);Qu=!0;try{var l=t(e);return l}finally{if(Qu=!1,(_a!==null||Va!==null)&&(Hs(),_a&&(e=_a,t=Va,Va=_a=null,Yf(e),t)))for(e=0;e<t.length;e++)Yf(t[e])}}function Di(t,e){var n=t.stateNode;if(n===null)return null;var l=n[ue]||null;if(l===null)return null;n=l[e];t:switch(e){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(l=!l.disabled)||(t=t.type,l=!(t==="button"||t==="input"||t==="select"||t==="textarea")),t=!l;break t;default:t=!1}if(t)return null;if(n&&typeof n!="function")throw Error(u(231,e,typeof n));return n}var Zu=!1;if(We)try{var Mi={};Object.defineProperty(Mi,"passive",{get:function(){Zu=!0}}),window.addEventListener("test",Mi,Mi),window.removeEventListener("test",Mi,Mi)}catch{Zu=!1}var En=null,ku=null,as=null;function Xf(){if(as)return as;var t,e=ku,n=e.length,l,r="value"in En?En.value:En.textContent,o=r.length;for(t=0;t<n&&e[t]===r[t];t++);var y=n-t;for(l=1;l<=y&&e[n-l]===r[o-l];l++);return as=r.slice(t,1<l?1-l:void 0)}function is(t){var e=t.keyCode;return"charCode"in t?(t=t.charCode,t===0&&e===13&&(t=13)):t=e,t===10&&(t=13),32<=t||t===13?t:0}function ls(){return!0}function Kf(){return!1}function re(t){function e(n,l,r,o,y){this._reactName=n,this._targetInst=r,this.type=l,this.nativeEvent=o,this.target=y,this.currentTarget=null;for(var v in t)t.hasOwnProperty(v)&&(n=t[v],this[v]=n?n(o):o[v]);return this.isDefaultPrevented=(o.defaultPrevented!=null?o.defaultPrevented:o.returnValue===!1)?ls:Kf,this.isPropagationStopped=Kf,this}return tt(e.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=ls)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=ls)},persist:function(){},isPersistent:ls}),e}var Wn={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(t){return t.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},ss=re(Wn),Ci=tt({},Wn,{view:0,detail:0}),a0=re(Ci),Pu,Fu,Oi,us=tt({},Ci,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:$u,button:0,buttons:0,relatedTarget:function(t){return t.relatedTarget===void 0?t.fromElement===t.srcElement?t.toElement:t.fromElement:t.relatedTarget},movementX:function(t){return"movementX"in t?t.movementX:(t!==Oi&&(Oi&&t.type==="mousemove"?(Pu=t.screenX-Oi.screenX,Fu=t.screenY-Oi.screenY):Fu=Pu=0,Oi=t),Pu)},movementY:function(t){return"movementY"in t?t.movementY:Fu}}),Qf=re(us),i0=tt({},us,{dataTransfer:0}),l0=re(i0),s0=tt({},Ci,{relatedTarget:0}),Ju=re(s0),u0=tt({},Wn,{animationName:0,elapsedTime:0,pseudoElement:0}),r0=re(u0),o0=tt({},Wn,{clipboardData:function(t){return"clipboardData"in t?t.clipboardData:window.clipboardData}}),c0=re(o0),f0=tt({},Wn,{data:0}),Zf=re(f0),h0={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},d0={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},m0={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function p0(t){var e=this.nativeEvent;return e.getModifierState?e.getModifierState(t):(t=m0[t])?!!e[t]:!1}function $u(){return p0}var y0=tt({},Ci,{key:function(t){if(t.key){var e=h0[t.key]||t.key;if(e!=="Unidentified")return e}return t.type==="keypress"?(t=is(t),t===13?"Enter":String.fromCharCode(t)):t.type==="keydown"||t.type==="keyup"?d0[t.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:$u,charCode:function(t){return t.type==="keypress"?is(t):0},keyCode:function(t){return t.type==="keydown"||t.type==="keyup"?t.keyCode:0},which:function(t){return t.type==="keypress"?is(t):t.type==="keydown"||t.type==="keyup"?t.keyCode:0}}),g0=re(y0),v0=tt({},us,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),kf=re(v0),S0=tt({},Ci,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:$u}),b0=re(S0),T0=tt({},Wn,{propertyName:0,elapsedTime:0,pseudoElement:0}),x0=re(T0),E0=tt({},us,{deltaX:function(t){return"deltaX"in t?t.deltaX:"wheelDeltaX"in t?-t.wheelDeltaX:0},deltaY:function(t){return"deltaY"in t?t.deltaY:"wheelDeltaY"in t?-t.wheelDeltaY:"wheelDelta"in t?-t.wheelDelta:0},deltaZ:0,deltaMode:0}),A0=re(E0),R0=tt({},Wn,{newState:0,oldState:0}),D0=re(R0),M0=[9,13,27,32],Wu=We&&"CompositionEvent"in window,wi=null;We&&"documentMode"in document&&(wi=document.documentMode);var C0=We&&"TextEvent"in window&&!wi,Pf=We&&(!Wu||wi&&8<wi&&11>=wi),Ff=" ",Jf=!1;function $f(t,e){switch(t){case"keyup":return M0.indexOf(e.keyCode)!==-1;case"keydown":return e.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Wf(t){return t=t.detail,typeof t=="object"&&"data"in t?t.data:null}var za=!1;function O0(t,e){switch(t){case"compositionend":return Wf(e);case"keypress":return e.which!==32?null:(Jf=!0,Ff);case"textInput":return t=e.data,t===Ff&&Jf?null:t;default:return null}}function w0(t,e){if(za)return t==="compositionend"||!Wu&&$f(t,e)?(t=Xf(),as=ku=En=null,za=!1,t):null;switch(t){case"paste":return null;case"keypress":if(!(e.ctrlKey||e.altKey||e.metaKey)||e.ctrlKey&&e.altKey){if(e.char&&1<e.char.length)return e.char;if(e.which)return String.fromCharCode(e.which)}return null;case"compositionend":return Pf&&e.locale!=="ko"?null:e.data;default:return null}}var _0={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function If(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e==="input"?!!_0[t.type]:e==="textarea"}function th(t,e,n,l){_a?Va?Va.push(l):Va=[l]:_a=l,e=Ks(e,"onChange"),0<e.length&&(n=new ss("onChange","change",null,n,l),t.push({event:n,listeners:e}))}var _i=null,Vi=null;function V0(t){Dm(t,0)}function rs(t){var e=Ri(t);if(Bf(e))return t}function eh(t,e){if(t==="change")return e}var nh=!1;if(We){var Iu;if(We){var tr="oninput"in document;if(!tr){var ah=document.createElement("div");ah.setAttribute("oninput","return;"),tr=typeof ah.oninput=="function"}Iu=tr}else Iu=!1;nh=Iu&&(!document.documentMode||9<document.documentMode)}function ih(){_i&&(_i.detachEvent("onpropertychange",lh),Vi=_i=null)}function lh(t){if(t.propertyName==="value"&&rs(Vi)){var e=[];th(e,Vi,t,Ku(t)),Gf(V0,e)}}function z0(t,e,n){t==="focusin"?(ih(),_i=e,Vi=n,_i.attachEvent("onpropertychange",lh)):t==="focusout"&&ih()}function U0(t){if(t==="selectionchange"||t==="keyup"||t==="keydown")return rs(Vi)}function B0(t,e){if(t==="click")return rs(e)}function j0(t,e){if(t==="input"||t==="change")return rs(e)}function L0(t,e){return t===e&&(t!==0||1/t===1/e)||t!==t&&e!==e}var he=typeof Object.is=="function"?Object.is:L0;function zi(t,e){if(he(t,e))return!0;if(typeof t!="object"||t===null||typeof e!="object"||e===null)return!1;var n=Object.keys(t),l=Object.keys(e);if(n.length!==l.length)return!1;for(l=0;l<n.length;l++){var r=n[l];if(!Uu.call(e,r)||!he(t[r],e[r]))return!1}return!0}function sh(t){for(;t&&t.firstChild;)t=t.firstChild;return t}function uh(t,e){var n=sh(t);t=0;for(var l;n;){if(n.nodeType===3){if(l=t+n.textContent.length,t<=e&&l>=e)return{node:n,offset:e-t};t=l}t:{for(;n;){if(n.nextSibling){n=n.nextSibling;break t}n=n.parentNode}n=void 0}n=sh(n)}}function rh(t,e){return t&&e?t===e?!0:t&&t.nodeType===3?!1:e&&e.nodeType===3?rh(t,e.parentNode):"contains"in t?t.contains(e):t.compareDocumentPosition?!!(t.compareDocumentPosition(e)&16):!1:!1}function oh(t){t=t!=null&&t.ownerDocument!=null&&t.ownerDocument.defaultView!=null?t.ownerDocument.defaultView:window;for(var e=es(t.document);e instanceof t.HTMLIFrameElement;){try{var n=typeof e.contentWindow.location.href=="string"}catch{n=!1}if(n)t=e.contentWindow;else break;e=es(t.document)}return e}function er(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e&&(e==="input"&&(t.type==="text"||t.type==="search"||t.type==="tel"||t.type==="url"||t.type==="password")||e==="textarea"||t.contentEditable==="true")}function N0(t,e){var n=oh(e);e=t.focusedElem;var l=t.selectionRange;if(n!==e&&e&&e.ownerDocument&&rh(e.ownerDocument.documentElement,e)){if(l!==null&&er(e)){if(t=l.start,n=l.end,n===void 0&&(n=t),"selectionStart"in e)e.selectionStart=t,e.selectionEnd=Math.min(n,e.value.length);else if(n=(t=e.ownerDocument||document)&&t.defaultView||window,n.getSelection){n=n.getSelection();var r=e.textContent.length,o=Math.min(l.start,r);l=l.end===void 0?o:Math.min(l.end,r),!n.extend&&o>l&&(r=l,l=o,o=r),r=uh(e,o);var y=uh(e,l);r&&y&&(n.rangeCount!==1||n.anchorNode!==r.node||n.anchorOffset!==r.offset||n.focusNode!==y.node||n.focusOffset!==y.offset)&&(t=t.createRange(),t.setStart(r.node,r.offset),n.removeAllRanges(),o>l?(n.addRange(t),n.extend(y.node,y.offset)):(t.setEnd(y.node,y.offset),n.addRange(t)))}}for(t=[],n=e;n=n.parentNode;)n.nodeType===1&&t.push({element:n,left:n.scrollLeft,top:n.scrollTop});for(typeof e.focus=="function"&&e.focus(),e=0;e<t.length;e++)n=t[e],n.element.scrollLeft=n.left,n.element.scrollTop=n.top}}var H0=We&&"documentMode"in document&&11>=document.documentMode,Ua=null,nr=null,Ui=null,ar=!1;function ch(t,e,n){var l=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;ar||Ua==null||Ua!==es(l)||(l=Ua,"selectionStart"in l&&er(l)?l={start:l.selectionStart,end:l.selectionEnd}:(l=(l.ownerDocument&&l.ownerDocument.defaultView||window).getSelection(),l={anchorNode:l.anchorNode,anchorOffset:l.anchorOffset,focusNode:l.focusNode,focusOffset:l.focusOffset}),Ui&&zi(Ui,l)||(Ui=l,l=Ks(nr,"onSelect"),0<l.length&&(e=new ss("onSelect","select",null,e,n),t.push({event:e,listeners:l}),e.target=Ua)))}function In(t,e){var n={};return n[t.toLowerCase()]=e.toLowerCase(),n["Webkit"+t]="webkit"+e,n["Moz"+t]="moz"+e,n}var Ba={animationend:In("Animation","AnimationEnd"),animationiteration:In("Animation","AnimationIteration"),animationstart:In("Animation","AnimationStart"),transitionrun:In("Transition","TransitionRun"),transitionstart:In("Transition","TransitionStart"),transitioncancel:In("Transition","TransitionCancel"),transitionend:In("Transition","TransitionEnd")},ir={},fh={};We&&(fh=document.createElement("div").style,"AnimationEvent"in window||(delete Ba.animationend.animation,delete Ba.animationiteration.animation,delete Ba.animationstart.animation),"TransitionEvent"in window||delete Ba.transitionend.transition);function ta(t){if(ir[t])return ir[t];if(!Ba[t])return t;var e=Ba[t],n;for(n in e)if(e.hasOwnProperty(n)&&n in fh)return ir[t]=e[n];return t}var hh=ta("animationend"),dh=ta("animationiteration"),mh=ta("animationstart"),q0=ta("transitionrun"),Y0=ta("transitionstart"),G0=ta("transitioncancel"),ph=ta("transitionend"),yh=new Map,gh="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll scrollEnd toggle touchMove waiting wheel".split(" ");function Be(t,e){yh.set(t,e),$n(e,[t])}var Ee=[],ja=0,lr=0;function os(){for(var t=ja,e=lr=ja=0;e<t;){var n=Ee[e];Ee[e++]=null;var l=Ee[e];Ee[e++]=null;var r=Ee[e];Ee[e++]=null;var o=Ee[e];if(Ee[e++]=null,l!==null&&r!==null){var y=l.pending;y===null?r.next=r:(r.next=y.next,y.next=r),l.pending=r}o!==0&&vh(n,r,o)}}function cs(t,e,n,l){Ee[ja++]=t,Ee[ja++]=e,Ee[ja++]=n,Ee[ja++]=l,lr|=l,t.lanes|=l,t=t.alternate,t!==null&&(t.lanes|=l)}function sr(t,e,n,l){return cs(t,e,n,l),fs(t)}function An(t,e){return cs(t,null,null,e),fs(t)}function vh(t,e,n){t.lanes|=n;var l=t.alternate;l!==null&&(l.lanes|=n);for(var r=!1,o=t.return;o!==null;)o.childLanes|=n,l=o.alternate,l!==null&&(l.childLanes|=n),o.tag===22&&(t=o.stateNode,t===null||t._visibility&1||(r=!0)),t=o,o=o.return;r&&e!==null&&t.tag===3&&(o=t.stateNode,r=31-fe(n),o=o.hiddenUpdates,t=o[r],t===null?o[r]=[e]:t.push(e),e.lane=n|536870912)}function fs(t){if(50<ll)throw ll=0,ho=null,Error(u(185));for(var e=t.return;e!==null;)t=e,e=t.return;return t.tag===3?t.stateNode:null}var La={},Sh=new WeakMap;function Ae(t,e){if(typeof t=="object"&&t!==null){var n=Sh.get(t);return n!==void 0?n:(e={value:t,source:e,stack:$(e)},Sh.set(t,e),e)}return{value:t,source:e,stack:$(e)}}var Na=[],Ha=0,hs=null,ds=0,Re=[],De=0,ea=null,tn=1,en="";function na(t,e){Na[Ha++]=ds,Na[Ha++]=hs,hs=t,ds=e}function bh(t,e,n){Re[De++]=tn,Re[De++]=en,Re[De++]=ea,ea=t;var l=tn;t=en;var r=32-fe(l)-1;l&=~(1<<r),n+=1;var o=32-fe(e)+r;if(30<o){var y=r-r%5;o=(l&(1<<y)-1).toString(32),l>>=y,r-=y,tn=1<<32-fe(e)+r|n<<r|l,en=o+t}else tn=1<<o|n<<r|l,en=t}function ur(t){t.return!==null&&(na(t,1),bh(t,1,0))}function rr(t){for(;t===hs;)hs=Na[--Ha],Na[Ha]=null,ds=Na[--Ha],Na[Ha]=null;for(;t===ea;)ea=Re[--De],Re[De]=null,en=Re[--De],Re[De]=null,tn=Re[--De],Re[De]=null}var ie=null,kt=null,pt=!1,je=null,Xe=!1,or=Error(u(519));function aa(t){var e=Error(u(418,""));throw Li(Ae(e,t)),or}function Th(t){var e=t.stateNode,n=t.type,l=t.memoizedProps;switch(e[$t]=t,e[ue]=l,n){case"dialog":ht("cancel",e),ht("close",e);break;case"iframe":case"object":case"embed":ht("load",e);break;case"video":case"audio":for(n=0;n<ul.length;n++)ht(ul[n],e);break;case"source":ht("error",e);break;case"img":case"image":case"link":ht("error",e),ht("load",e);break;case"details":ht("toggle",e);break;case"input":ht("invalid",e),jf(e,l.value,l.defaultValue,l.checked,l.defaultChecked,l.type,l.name,!0),ts(e);break;case"select":ht("invalid",e);break;case"textarea":ht("invalid",e),Nf(e,l.value,l.defaultValue,l.children),ts(e)}n=l.children,typeof n!="string"&&typeof n!="number"&&typeof n!="bigint"||e.textContent===""+n||l.suppressHydrationWarning===!0||wm(e.textContent,n)?(l.popover!=null&&(ht("beforetoggle",e),ht("toggle",e)),l.onScroll!=null&&ht("scroll",e),l.onScrollEnd!=null&&ht("scrollend",e),l.onClick!=null&&(e.onclick=Qs),e=!0):e=!1,e||aa(t)}function xh(t){for(ie=t.return;ie;)switch(ie.tag){case 3:case 27:Xe=!0;return;case 5:case 13:Xe=!1;return;default:ie=ie.return}}function Bi(t){if(t!==ie)return!1;if(!pt)return xh(t),pt=!0,!1;var e=!1,n;if((n=t.tag!==3&&t.tag!==27)&&((n=t.tag===5)&&(n=t.type,n=!(n!=="form"&&n!=="button")||wo(t.type,t.memoizedProps)),n=!n),n&&(e=!0),e&&kt&&aa(t),xh(t),t.tag===13){if(t=t.memoizedState,t=t!==null?t.dehydrated:null,!t)throw Error(u(317));t:{for(t=t.nextSibling,e=0;t;){if(t.nodeType===8)if(n=t.data,n==="/$"){if(e===0){kt=Ne(t.nextSibling);break t}e--}else n!=="$"&&n!=="$!"&&n!=="$?"||e++;t=t.nextSibling}kt=null}}else kt=ie?Ne(t.stateNode.nextSibling):null;return!0}function ji(){kt=ie=null,pt=!1}function Li(t){je===null?je=[t]:je.push(t)}var Ni=Error(u(460)),Eh=Error(u(474)),cr={then:function(){}};function Ah(t){return t=t.status,t==="fulfilled"||t==="rejected"}function ms(){}function Rh(t,e,n){switch(n=t[n],n===void 0?t.push(e):n!==e&&(e.then(ms,ms),e=n),e.status){case"fulfilled":return e.value;case"rejected":throw t=e.reason,t===Ni?Error(u(483)):t;default:if(typeof e.status=="string")e.then(ms,ms);else{if(t=xt,t!==null&&100<t.shellSuspendCounter)throw Error(u(482));t=e,t.status="pending",t.then(function(l){if(e.status==="pending"){var r=e;r.status="fulfilled",r.value=l}},function(l){if(e.status==="pending"){var r=e;r.status="rejected",r.reason=l}})}switch(e.status){case"fulfilled":return e.value;case"rejected":throw t=e.reason,t===Ni?Error(u(483)):t}throw Hi=e,Ni}}var Hi=null;function Dh(){if(Hi===null)throw Error(u(459));var t=Hi;return Hi=null,t}var qa=null,qi=0;function ps(t){var e=qi;return qi+=1,qa===null&&(qa=[]),Rh(qa,t,e)}function Yi(t,e){e=e.props.ref,t.ref=e!==void 0?e:null}function ys(t,e){throw e.$$typeof===f?Error(u(525)):(t=Object.prototype.toString.call(e),Error(u(31,t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)))}function Mh(t){var e=t._init;return e(t._payload)}function Ch(t){function e(C,A){if(t){var w=C.deletions;w===null?(C.deletions=[A],C.flags|=16):w.push(A)}}function n(C,A){if(!t)return null;for(;A!==null;)e(C,A),A=A.sibling;return null}function l(C){for(var A=new Map;C!==null;)C.key!==null?A.set(C.key,C):A.set(C.index,C),C=C.sibling;return A}function r(C,A){return C=jn(C,A),C.index=0,C.sibling=null,C}function o(C,A,w){return C.index=w,t?(w=C.alternate,w!==null?(w=w.index,w<A?(C.flags|=33554434,A):w):(C.flags|=33554434,A)):(C.flags|=1048576,A)}function y(C){return t&&C.alternate===null&&(C.flags|=33554434),C}function v(C,A,w,N){return A===null||A.tag!==6?(A=io(w,C.mode,N),A.return=C,A):(A=r(A,w),A.return=C,A)}function T(C,A,w,N){var Z=w.type;return Z===d?B(C,A,w.props.children,N,w.key):A!==null&&(A.elementType===Z||typeof Z=="object"&&Z!==null&&Z.$$typeof===L&&Mh(Z)===A.type)?(A=r(A,w.props),Yi(A,w),A.return=C,A):(A=Us(w.type,w.key,w.props,null,C.mode,N),Yi(A,w),A.return=C,A)}function D(C,A,w,N){return A===null||A.tag!==4||A.stateNode.containerInfo!==w.containerInfo||A.stateNode.implementation!==w.implementation?(A=lo(w,C.mode,N),A.return=C,A):(A=r(A,w.children||[]),A.return=C,A)}function B(C,A,w,N,Z){return A===null||A.tag!==7?(A=da(w,C.mode,N,Z),A.return=C,A):(A=r(A,w),A.return=C,A)}function q(C,A,w){if(typeof A=="string"&&A!==""||typeof A=="number"||typeof A=="bigint")return A=io(""+A,C.mode,w),A.return=C,A;if(typeof A=="object"&&A!==null){switch(A.$$typeof){case h:return w=Us(A.type,A.key,A.props,null,C.mode,w),Yi(w,A),w.return=C,w;case p:return A=lo(A,C.mode,w),A.return=C,A;case L:var N=A._init;return A=N(A._payload),q(C,A,w)}if(et(A)||k(A))return A=da(A,C.mode,w,null),A.return=C,A;if(typeof A.then=="function")return q(C,ps(A),w);if(A.$$typeof===x)return q(C,_s(C,A),w);ys(C,A)}return null}function _(C,A,w,N){var Z=A!==null?A.key:null;if(typeof w=="string"&&w!==""||typeof w=="number"||typeof w=="bigint")return Z!==null?null:v(C,A,""+w,N);if(typeof w=="object"&&w!==null){switch(w.$$typeof){case h:return w.key===Z?T(C,A,w,N):null;case p:return w.key===Z?D(C,A,w,N):null;case L:return Z=w._init,w=Z(w._payload),_(C,A,w,N)}if(et(w)||k(w))return Z!==null?null:B(C,A,w,N,null);if(typeof w.then=="function")return _(C,A,ps(w),N);if(w.$$typeof===x)return _(C,A,_s(C,w),N);ys(C,w)}return null}function U(C,A,w,N,Z){if(typeof N=="string"&&N!==""||typeof N=="number"||typeof N=="bigint")return C=C.get(w)||null,v(A,C,""+N,Z);if(typeof N=="object"&&N!==null){switch(N.$$typeof){case h:return C=C.get(N.key===null?w:N.key)||null,T(A,C,N,Z);case p:return C=C.get(N.key===null?w:N.key)||null,D(A,C,N,Z);case L:var ct=N._init;return N=ct(N._payload),U(C,A,w,N,Z)}if(et(N)||k(N))return C=C.get(w)||null,B(A,C,N,Z,null);if(typeof N.then=="function")return U(C,A,w,ps(N),Z);if(N.$$typeof===x)return U(C,A,w,_s(A,N),Z);ys(A,N)}return null}function F(C,A,w,N){for(var Z=null,ct=null,I=A,nt=A=0,Zt=null;I!==null&&nt<w.length;nt++){I.index>nt?(Zt=I,I=null):Zt=I.sibling;var yt=_(C,I,w[nt],N);if(yt===null){I===null&&(I=Zt);break}t&&I&&yt.alternate===null&&e(C,I),A=o(yt,A,nt),ct===null?Z=yt:ct.sibling=yt,ct=yt,I=Zt}if(nt===w.length)return n(C,I),pt&&na(C,nt),Z;if(I===null){for(;nt<w.length;nt++)I=q(C,w[nt],N),I!==null&&(A=o(I,A,nt),ct===null?Z=I:ct.sibling=I,ct=I);return pt&&na(C,nt),Z}for(I=l(I);nt<w.length;nt++)Zt=U(I,C,nt,w[nt],N),Zt!==null&&(t&&Zt.alternate!==null&&I.delete(Zt.key===null?nt:Zt.key),A=o(Zt,A,nt),ct===null?Z=Zt:ct.sibling=Zt,ct=Zt);return t&&I.forEach(function(Xn){return e(C,Xn)}),pt&&na(C,nt),Z}function lt(C,A,w,N){if(w==null)throw Error(u(151));for(var Z=null,ct=null,I=A,nt=A=0,Zt=null,yt=w.next();I!==null&&!yt.done;nt++,yt=w.next()){I.index>nt?(Zt=I,I=null):Zt=I.sibling;var Xn=_(C,I,yt.value,N);if(Xn===null){I===null&&(I=Zt);break}t&&I&&Xn.alternate===null&&e(C,I),A=o(Xn,A,nt),ct===null?Z=Xn:ct.sibling=Xn,ct=Xn,I=Zt}if(yt.done)return n(C,I),pt&&na(C,nt),Z;if(I===null){for(;!yt.done;nt++,yt=w.next())yt=q(C,yt.value,N),yt!==null&&(A=o(yt,A,nt),ct===null?Z=yt:ct.sibling=yt,ct=yt);return pt&&na(C,nt),Z}for(I=l(I);!yt.done;nt++,yt=w.next())yt=U(I,C,nt,yt.value,N),yt!==null&&(t&&yt.alternate!==null&&I.delete(yt.key===null?nt:yt.key),A=o(yt,A,nt),ct===null?Z=yt:ct.sibling=yt,ct=yt);return t&&I.forEach(function(nS){return e(C,nS)}),pt&&na(C,nt),Z}function zt(C,A,w,N){if(typeof w=="object"&&w!==null&&w.type===d&&w.key===null&&(w=w.props.children),typeof w=="object"&&w!==null){switch(w.$$typeof){case h:t:{for(var Z=w.key;A!==null;){if(A.key===Z){if(Z=w.type,Z===d){if(A.tag===7){n(C,A.sibling),N=r(A,w.props.children),N.return=C,C=N;break t}}else if(A.elementType===Z||typeof Z=="object"&&Z!==null&&Z.$$typeof===L&&Mh(Z)===A.type){n(C,A.sibling),N=r(A,w.props),Yi(N,w),N.return=C,C=N;break t}n(C,A);break}else e(C,A);A=A.sibling}w.type===d?(N=da(w.props.children,C.mode,N,w.key),N.return=C,C=N):(N=Us(w.type,w.key,w.props,null,C.mode,N),Yi(N,w),N.return=C,C=N)}return y(C);case p:t:{for(Z=w.key;A!==null;){if(A.key===Z)if(A.tag===4&&A.stateNode.containerInfo===w.containerInfo&&A.stateNode.implementation===w.implementation){n(C,A.sibling),N=r(A,w.children||[]),N.return=C,C=N;break t}else{n(C,A);break}else e(C,A);A=A.sibling}N=lo(w,C.mode,N),N.return=C,C=N}return y(C);case L:return Z=w._init,w=Z(w._payload),zt(C,A,w,N)}if(et(w))return F(C,A,w,N);if(k(w)){if(Z=k(w),typeof Z!="function")throw Error(u(150));return w=Z.call(w),lt(C,A,w,N)}if(typeof w.then=="function")return zt(C,A,ps(w),N);if(w.$$typeof===x)return zt(C,A,_s(C,w),N);ys(C,w)}return typeof w=="string"&&w!==""||typeof w=="number"||typeof w=="bigint"?(w=""+w,A!==null&&A.tag===6?(n(C,A.sibling),N=r(A,w),N.return=C,C=N):(n(C,A),N=io(w,C.mode,N),N.return=C,C=N),y(C)):n(C,A)}return function(C,A,w,N){try{qi=0;var Z=zt(C,A,w,N);return qa=null,Z}catch(I){if(I===Ni)throw I;var ct=we(29,I,null,C.mode);return ct.lanes=N,ct.return=C,ct}finally{}}}var ia=Ch(!0),Oh=Ch(!1),Ya=vt(null),gs=vt(0);function wh(t,e){t=dn,Rt(gs,t),Rt(Ya,e),dn=t|e.baseLanes}function fr(){Rt(gs,dn),Rt(Ya,Ya.current)}function hr(){dn=gs.current,Lt(Ya),Lt(gs)}var Me=vt(null),Ke=null;function Rn(t){var e=t.alternate;Rt(Yt,Yt.current&1),Rt(Me,t),Ke===null&&(e===null||Ya.current!==null||e.memoizedState!==null)&&(Ke=t)}function _h(t){if(t.tag===22){if(Rt(Yt,Yt.current),Rt(Me,t),Ke===null){var e=t.alternate;e!==null&&e.memoizedState!==null&&(Ke=t)}}else Dn()}function Dn(){Rt(Yt,Yt.current),Rt(Me,Me.current)}function nn(t){Lt(Me),Ke===t&&(Ke=null),Lt(Yt)}var Yt=vt(0);function vs(t){for(var e=t;e!==null;){if(e.tag===13){var n=e.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return e}else if(e.tag===19&&e.memoizedProps.revealOrder!==void 0){if((e.flags&128)!==0)return e}else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break;for(;e.sibling===null;){if(e.return===null||e.return===t)return null;e=e.return}e.sibling.return=e.return,e=e.sibling}return null}var X0=typeof AbortController<"u"?AbortController:function(){var t=[],e=this.signal={aborted:!1,addEventListener:function(n,l){t.push(l)}};this.abort=function(){e.aborted=!0,t.forEach(function(n){return n()})}},K0=a.unstable_scheduleCallback,Q0=a.unstable_NormalPriority,Gt={$$typeof:x,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function dr(){return{controller:new X0,data:new Map,refCount:0}}function Gi(t){t.refCount--,t.refCount===0&&K0(Q0,function(){t.controller.abort()})}var Xi=null,mr=0,Ga=0,Xa=null;function Z0(t,e){if(Xi===null){var n=Xi=[];mr=0,Ga=To(),Xa={status:"pending",value:void 0,then:function(l){n.push(l)}}}return mr++,e.then(Vh,Vh),e}function Vh(){if(--mr===0&&Xi!==null){Xa!==null&&(Xa.status="fulfilled");var t=Xi;Xi=null,Ga=0,Xa=null;for(var e=0;e<t.length;e++)(0,t[e])()}}function k0(t,e){var n=[],l={status:"pending",value:null,reason:null,then:function(r){n.push(r)}};return t.then(function(){l.status="fulfilled",l.value=e;for(var r=0;r<n.length;r++)(0,n[r])(e)},function(r){for(l.status="rejected",l.reason=r,r=0;r<n.length;r++)(0,n[r])(void 0)}),l}var zh=X.S;X.S=function(t,e){typeof e=="object"&&e!==null&&typeof e.then=="function"&&Z0(t,e),zh!==null&&zh(t,e)};var la=vt(null);function pr(){var t=la.current;return t!==null?t:xt.pooledCache}function Ss(t,e){e===null?Rt(la,la.current):Rt(la,e.pool)}function Uh(){var t=pr();return t===null?null:{parent:Gt._currentValue,pool:t}}var Mn=0,ot=null,St=null,Nt=null,bs=!1,Ka=!1,sa=!1,Ts=0,Ki=0,Qa=null,P0=0;function Bt(){throw Error(u(321))}function yr(t,e){if(e===null)return!1;for(var n=0;n<e.length&&n<t.length;n++)if(!he(t[n],e[n]))return!1;return!0}function gr(t,e,n,l,r,o){return Mn=o,ot=e,e.memoizedState=null,e.updateQueue=null,e.lanes=0,X.H=t===null||t.memoizedState===null?ua:Cn,sa=!1,o=n(l,r),sa=!1,Ka&&(o=jh(e,n,l,r)),Bh(t),o}function Bh(t){X.H=Qe;var e=St!==null&&St.next!==null;if(Mn=0,Nt=St=ot=null,bs=!1,Ki=0,Qa=null,e)throw Error(u(300));t===null||Kt||(t=t.dependencies,t!==null&&ws(t)&&(Kt=!0))}function jh(t,e,n,l){ot=t;var r=0;do{if(Ka&&(Qa=null),Ki=0,Ka=!1,25<=r)throw Error(u(301));if(r+=1,Nt=St=null,t.updateQueue!=null){var o=t.updateQueue;o.lastEffect=null,o.events=null,o.stores=null,o.memoCache!=null&&(o.memoCache.index=0)}X.H=ra,o=e(n,l)}while(Ka);return o}function F0(){var t=X.H,e=t.useState()[0];return e=typeof e.then=="function"?Qi(e):e,t=t.useState()[0],(St!==null?St.memoizedState:null)!==t&&(ot.flags|=1024),e}function vr(){var t=Ts!==0;return Ts=0,t}function Sr(t,e,n){e.updateQueue=t.updateQueue,e.flags&=-2053,t.lanes&=~n}function br(t){if(bs){for(t=t.memoizedState;t!==null;){var e=t.queue;e!==null&&(e.pending=null),t=t.next}bs=!1}Mn=0,Nt=St=ot=null,Ka=!1,Ki=Ts=0,Qa=null}function oe(){var t={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return Nt===null?ot.memoizedState=Nt=t:Nt=Nt.next=t,Nt}function Ht(){if(St===null){var t=ot.alternate;t=t!==null?t.memoizedState:null}else t=St.next;var e=Nt===null?ot.memoizedState:Nt.next;if(e!==null)Nt=e,St=t;else{if(t===null)throw ot.alternate===null?Error(u(467)):Error(u(310));St=t,t={memoizedState:St.memoizedState,baseState:St.baseState,baseQueue:St.baseQueue,queue:St.queue,next:null},Nt===null?ot.memoizedState=Nt=t:Nt=Nt.next=t}return Nt}var xs;xs=function(){return{lastEffect:null,events:null,stores:null,memoCache:null}};function Qi(t){var e=Ki;return Ki+=1,Qa===null&&(Qa=[]),t=Rh(Qa,t,e),e=ot,(Nt===null?e.memoizedState:Nt.next)===null&&(e=e.alternate,X.H=e===null||e.memoizedState===null?ua:Cn),t}function Es(t){if(t!==null&&typeof t=="object"){if(typeof t.then=="function")return Qi(t);if(t.$$typeof===x)return Wt(t)}throw Error(u(438,String(t)))}function Tr(t){var e=null,n=ot.updateQueue;if(n!==null&&(e=n.memoCache),e==null){var l=ot.alternate;l!==null&&(l=l.updateQueue,l!==null&&(l=l.memoCache,l!=null&&(e={data:l.data.map(function(r){return r.slice()}),index:0})))}if(e==null&&(e={data:[],index:0}),n===null&&(n=xs(),ot.updateQueue=n),n.memoCache=e,n=e.data[e.index],n===void 0)for(n=e.data[e.index]=Array(t),l=0;l<t;l++)n[l]=P;return e.index++,n}function an(t,e){return typeof e=="function"?e(t):e}function As(t){var e=Ht();return xr(e,St,t)}function xr(t,e,n){var l=t.queue;if(l===null)throw Error(u(311));l.lastRenderedReducer=n;var r=t.baseQueue,o=l.pending;if(o!==null){if(r!==null){var y=r.next;r.next=o.next,o.next=y}e.baseQueue=r=o,l.pending=null}if(o=t.baseState,r===null)t.memoizedState=o;else{e=r.next;var v=y=null,T=null,D=e,B=!1;do{var q=D.lane&-536870913;if(q!==D.lane?(mt&q)===q:(Mn&q)===q){var _=D.revertLane;if(_===0)T!==null&&(T=T.next={lane:0,revertLane:0,action:D.action,hasEagerState:D.hasEagerState,eagerState:D.eagerState,next:null}),q===Ga&&(B=!0);else if((Mn&_)===_){D=D.next,_===Ga&&(B=!0);continue}else q={lane:0,revertLane:D.revertLane,action:D.action,hasEagerState:D.hasEagerState,eagerState:D.eagerState,next:null},T===null?(v=T=q,y=o):T=T.next=q,ot.lanes|=_,Ln|=_;q=D.action,sa&&n(o,q),o=D.hasEagerState?D.eagerState:n(o,q)}else _={lane:q,revertLane:D.revertLane,action:D.action,hasEagerState:D.hasEagerState,eagerState:D.eagerState,next:null},T===null?(v=T=_,y=o):T=T.next=_,ot.lanes|=q,Ln|=q;D=D.next}while(D!==null&&D!==e);if(T===null?y=o:T.next=v,!he(o,t.memoizedState)&&(Kt=!0,B&&(n=Xa,n!==null)))throw n;t.memoizedState=o,t.baseState=y,t.baseQueue=T,l.lastRenderedState=o}return r===null&&(l.lanes=0),[t.memoizedState,l.dispatch]}function Er(t){var e=Ht(),n=e.queue;if(n===null)throw Error(u(311));n.lastRenderedReducer=t;var l=n.dispatch,r=n.pending,o=e.memoizedState;if(r!==null){n.pending=null;var y=r=r.next;do o=t(o,y.action),y=y.next;while(y!==r);he(o,e.memoizedState)||(Kt=!0),e.memoizedState=o,e.baseQueue===null&&(e.baseState=o),n.lastRenderedState=o}return[o,l]}function Lh(t,e,n){var l=ot,r=Ht(),o=pt;if(o){if(n===void 0)throw Error(u(407));n=n()}else n=e();var y=!he((St||r).memoizedState,n);if(y&&(r.memoizedState=n,Kt=!0),r=r.queue,Dr(qh.bind(null,l,r,t),[t]),r.getSnapshot!==e||y||Nt!==null&&Nt.memoizedState.tag&1){if(l.flags|=2048,Za(9,Hh.bind(null,l,r,n,e),{destroy:void 0},null),xt===null)throw Error(u(349));o||(Mn&60)!==0||Nh(l,e,n)}return n}function Nh(t,e,n){t.flags|=16384,t={getSnapshot:e,value:n},e=ot.updateQueue,e===null?(e=xs(),ot.updateQueue=e,e.stores=[t]):(n=e.stores,n===null?e.stores=[t]:n.push(t))}function Hh(t,e,n,l){e.value=n,e.getSnapshot=l,Yh(e)&&Gh(t)}function qh(t,e,n){return n(function(){Yh(e)&&Gh(t)})}function Yh(t){var e=t.getSnapshot;t=t.value;try{var n=e();return!he(t,n)}catch{return!0}}function Gh(t){var e=An(t,2);e!==null&&le(e,t,2)}function Ar(t){var e=oe();if(typeof t=="function"){var n=t;if(t=n(),sa){Tn(!0);try{n()}finally{Tn(!1)}}}return e.memoizedState=e.baseState=t,e.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:an,lastRenderedState:t},e}function Xh(t,e,n,l){return t.baseState=n,xr(t,St,typeof l=="function"?l:an)}function J0(t,e,n,l,r){if(Ms(t))throw Error(u(485));if(t=e.action,t!==null){var o={payload:r,action:t,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(y){o.listeners.push(y)}};X.T!==null?n(!0):o.isTransition=!1,l(o),n=e.pending,n===null?(o.next=e.pending=o,Kh(e,o)):(o.next=n.next,e.pending=n.next=o)}}function Kh(t,e){var n=e.action,l=e.payload,r=t.state;if(e.isTransition){var o=X.T,y={};X.T=y;try{var v=n(r,l),T=X.S;T!==null&&T(y,v),Qh(t,e,v)}catch(D){Rr(t,e,D)}finally{X.T=o}}else try{o=n(r,l),Qh(t,e,o)}catch(D){Rr(t,e,D)}}function Qh(t,e,n){n!==null&&typeof n=="object"&&typeof n.then=="function"?n.then(function(l){Zh(t,e,l)},function(l){return Rr(t,e,l)}):Zh(t,e,n)}function Zh(t,e,n){e.status="fulfilled",e.value=n,kh(e),t.state=n,e=t.pending,e!==null&&(n=e.next,n===e?t.pending=null:(n=n.next,e.next=n,Kh(t,n)))}function Rr(t,e,n){var l=t.pending;if(t.pending=null,l!==null){l=l.next;do e.status="rejected",e.reason=n,kh(e),e=e.next;while(e!==l)}t.action=null}function kh(t){t=t.listeners;for(var e=0;e<t.length;e++)(0,t[e])()}function Ph(t,e){return e}function Fh(t,e){if(pt){var n=xt.formState;if(n!==null){t:{var l=ot;if(pt){if(kt){e:{for(var r=kt,o=Xe;r.nodeType!==8;){if(!o){r=null;break e}if(r=Ne(r.nextSibling),r===null){r=null;break e}}o=r.data,r=o==="F!"||o==="F"?r:null}if(r){kt=Ne(r.nextSibling),l=r.data==="F!";break t}}aa(l)}l=!1}l&&(e=n[0])}}return n=oe(),n.memoizedState=n.baseState=e,l={pending:null,lanes:0,dispatch:null,lastRenderedReducer:Ph,lastRenderedState:e},n.queue=l,n=dd.bind(null,ot,l),l.dispatch=n,l=Ar(!1),o=_r.bind(null,ot,!1,l.queue),l=oe(),r={state:e,dispatch:null,action:t,pending:null},l.queue=r,n=J0.bind(null,ot,r,o,n),r.dispatch=n,l.memoizedState=t,[e,n,!1]}function Jh(t){var e=Ht();return $h(e,St,t)}function $h(t,e,n){e=xr(t,e,Ph)[0],t=As(an)[0],e=typeof e=="object"&&e!==null&&typeof e.then=="function"?Qi(e):e;var l=Ht(),r=l.queue,o=r.dispatch;return n!==l.memoizedState&&(ot.flags|=2048,Za(9,$0.bind(null,r,n),{destroy:void 0},null)),[e,o,t]}function $0(t,e){t.action=e}function Wh(t){var e=Ht(),n=St;if(n!==null)return $h(e,n,t);Ht(),e=e.memoizedState,n=Ht();var l=n.queue.dispatch;return n.memoizedState=t,[e,l,!1]}function Za(t,e,n,l){return t={tag:t,create:e,inst:n,deps:l,next:null},e=ot.updateQueue,e===null&&(e=xs(),ot.updateQueue=e),n=e.lastEffect,n===null?e.lastEffect=t.next=t:(l=n.next,n.next=t,t.next=l,e.lastEffect=t),t}function Ih(){return Ht().memoizedState}function Rs(t,e,n,l){var r=oe();ot.flags|=t,r.memoizedState=Za(1|e,n,{destroy:void 0},l===void 0?null:l)}function Ds(t,e,n,l){var r=Ht();l=l===void 0?null:l;var o=r.memoizedState.inst;St!==null&&l!==null&&yr(l,St.memoizedState.deps)?r.memoizedState=Za(e,n,o,l):(ot.flags|=t,r.memoizedState=Za(1|e,n,o,l))}function td(t,e){Rs(8390656,8,t,e)}function Dr(t,e){Ds(2048,8,t,e)}function ed(t,e){return Ds(4,2,t,e)}function nd(t,e){return Ds(4,4,t,e)}function ad(t,e){if(typeof e=="function"){t=t();var n=e(t);return function(){typeof n=="function"?n():e(null)}}if(e!=null)return t=t(),e.current=t,function(){e.current=null}}function id(t,e,n){n=n!=null?n.concat([t]):null,Ds(4,4,ad.bind(null,e,t),n)}function Mr(){}function ld(t,e){var n=Ht();e=e===void 0?null:e;var l=n.memoizedState;return e!==null&&yr(e,l[1])?l[0]:(n.memoizedState=[t,e],t)}function sd(t,e){var n=Ht();e=e===void 0?null:e;var l=n.memoizedState;if(e!==null&&yr(e,l[1]))return l[0];if(l=t(),sa){Tn(!0);try{t()}finally{Tn(!1)}}return n.memoizedState=[l,e],l}function Cr(t,e,n){return n===void 0||(Mn&1073741824)!==0?t.memoizedState=e:(t.memoizedState=n,t=rm(),ot.lanes|=t,Ln|=t,n)}function ud(t,e,n,l){return he(n,e)?n:Ya.current!==null?(t=Cr(t,n,l),he(t,e)||(Kt=!0),t):(Mn&42)===0?(Kt=!0,t.memoizedState=n):(t=rm(),ot.lanes|=t,Ln|=t,e)}function rd(t,e,n,l,r){var o=Q.p;Q.p=o!==0&&8>o?o:8;var y=X.T,v={};X.T=v,_r(t,!1,e,n);try{var T=r(),D=X.S;if(D!==null&&D(v,T),T!==null&&typeof T=="object"&&typeof T.then=="function"){var B=k0(T,l);Zi(t,e,B,ye(t))}else Zi(t,e,l,ye(t))}catch(q){Zi(t,e,{then:function(){},status:"rejected",reason:q},ye())}finally{Q.p=o,X.T=y}}function W0(){}function Or(t,e,n,l){if(t.tag!==5)throw Error(u(476));var r=od(t).queue;rd(t,r,e,dt,n===null?W0:function(){return cd(t),n(l)})}function od(t){var e=t.memoizedState;if(e!==null)return e;e={memoizedState:dt,baseState:dt,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:an,lastRenderedState:dt},next:null};var n={};return e.next={memoizedState:n,baseState:n,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:an,lastRenderedState:n},next:null},t.memoizedState=e,t=t.alternate,t!==null&&(t.memoizedState=e),e}function cd(t){var e=od(t).next.queue;Zi(t,e,{},ye())}function wr(){return Wt(hl)}function fd(){return Ht().memoizedState}function hd(){return Ht().memoizedState}function I0(t){for(var e=t.return;e!==null;){switch(e.tag){case 24:case 3:var n=ye();t=_n(n);var l=Vn(e,t,n);l!==null&&(le(l,e,n),Fi(l,e,n)),e={cache:dr()},t.payload=e;return}e=e.return}}function t1(t,e,n){var l=ye();n={lane:l,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null},Ms(t)?md(e,n):(n=sr(t,e,n,l),n!==null&&(le(n,t,l),pd(n,e,l)))}function dd(t,e,n){var l=ye();Zi(t,e,n,l)}function Zi(t,e,n,l){var r={lane:l,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null};if(Ms(t))md(e,r);else{var o=t.alternate;if(t.lanes===0&&(o===null||o.lanes===0)&&(o=e.lastRenderedReducer,o!==null))try{var y=e.lastRenderedState,v=o(y,n);if(r.hasEagerState=!0,r.eagerState=v,he(v,y))return cs(t,e,r,0),xt===null&&os(),!1}catch{}finally{}if(n=sr(t,e,r,l),n!==null)return le(n,t,l),pd(n,e,l),!0}return!1}function _r(t,e,n,l){if(l={lane:2,revertLane:To(),action:l,hasEagerState:!1,eagerState:null,next:null},Ms(t)){if(e)throw Error(u(479))}else e=sr(t,n,l,2),e!==null&&le(e,t,2)}function Ms(t){var e=t.alternate;return t===ot||e!==null&&e===ot}function md(t,e){Ka=bs=!0;var n=t.pending;n===null?e.next=e:(e.next=n.next,n.next=e),t.pending=e}function pd(t,e,n){if((n&4194176)!==0){var l=e.lanes;l&=t.pendingLanes,n|=l,e.lanes=n,Df(t,n)}}var Qe={readContext:Wt,use:Es,useCallback:Bt,useContext:Bt,useEffect:Bt,useImperativeHandle:Bt,useLayoutEffect:Bt,useInsertionEffect:Bt,useMemo:Bt,useReducer:Bt,useRef:Bt,useState:Bt,useDebugValue:Bt,useDeferredValue:Bt,useTransition:Bt,useSyncExternalStore:Bt,useId:Bt};Qe.useCacheRefresh=Bt,Qe.useMemoCache=Bt,Qe.useHostTransitionStatus=Bt,Qe.useFormState=Bt,Qe.useActionState=Bt,Qe.useOptimistic=Bt;var ua={readContext:Wt,use:Es,useCallback:function(t,e){return oe().memoizedState=[t,e===void 0?null:e],t},useContext:Wt,useEffect:td,useImperativeHandle:function(t,e,n){n=n!=null?n.concat([t]):null,Rs(4194308,4,ad.bind(null,e,t),n)},useLayoutEffect:function(t,e){return Rs(4194308,4,t,e)},useInsertionEffect:function(t,e){Rs(4,2,t,e)},useMemo:function(t,e){var n=oe();e=e===void 0?null:e;var l=t();if(sa){Tn(!0);try{t()}finally{Tn(!1)}}return n.memoizedState=[l,e],l},useReducer:function(t,e,n){var l=oe();if(n!==void 0){var r=n(e);if(sa){Tn(!0);try{n(e)}finally{Tn(!1)}}}else r=e;return l.memoizedState=l.baseState=r,t={pending:null,lanes:0,dispatch:null,lastRenderedReducer:t,lastRenderedState:r},l.queue=t,t=t.dispatch=t1.bind(null,ot,t),[l.memoizedState,t]},useRef:function(t){var e=oe();return t={current:t},e.memoizedState=t},useState:function(t){t=Ar(t);var e=t.queue,n=dd.bind(null,ot,e);return e.dispatch=n,[t.memoizedState,n]},useDebugValue:Mr,useDeferredValue:function(t,e){var n=oe();return Cr(n,t,e)},useTransition:function(){var t=Ar(!1);return t=rd.bind(null,ot,t.queue,!0,!1),oe().memoizedState=t,[!1,t]},useSyncExternalStore:function(t,e,n){var l=ot,r=oe();if(pt){if(n===void 0)throw Error(u(407));n=n()}else{if(n=e(),xt===null)throw Error(u(349));(mt&60)!==0||Nh(l,e,n)}r.memoizedState=n;var o={value:n,getSnapshot:e};return r.queue=o,td(qh.bind(null,l,o,t),[t]),l.flags|=2048,Za(9,Hh.bind(null,l,o,n,e),{destroy:void 0},null),n},useId:function(){var t=oe(),e=xt.identifierPrefix;if(pt){var n=en,l=tn;n=(l&~(1<<32-fe(l)-1)).toString(32)+n,e=":"+e+"R"+n,n=Ts++,0<n&&(e+="H"+n.toString(32)),e+=":"}else n=P0++,e=":"+e+"r"+n.toString(32)+":";return t.memoizedState=e},useCacheRefresh:function(){return oe().memoizedState=I0.bind(null,ot)}};ua.useMemoCache=Tr,ua.useHostTransitionStatus=wr,ua.useFormState=Fh,ua.useActionState=Fh,ua.useOptimistic=function(t){var e=oe();e.memoizedState=e.baseState=t;var n={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return e.queue=n,e=_r.bind(null,ot,!0,n),n.dispatch=e,[t,e]};var Cn={readContext:Wt,use:Es,useCallback:ld,useContext:Wt,useEffect:Dr,useImperativeHandle:id,useInsertionEffect:ed,useLayoutEffect:nd,useMemo:sd,useReducer:As,useRef:Ih,useState:function(){return As(an)},useDebugValue:Mr,useDeferredValue:function(t,e){var n=Ht();return ud(n,St.memoizedState,t,e)},useTransition:function(){var t=As(an)[0],e=Ht().memoizedState;return[typeof t=="boolean"?t:Qi(t),e]},useSyncExternalStore:Lh,useId:fd};Cn.useCacheRefresh=hd,Cn.useMemoCache=Tr,Cn.useHostTransitionStatus=wr,Cn.useFormState=Jh,Cn.useActionState=Jh,Cn.useOptimistic=function(t,e){var n=Ht();return Xh(n,St,t,e)};var ra={readContext:Wt,use:Es,useCallback:ld,useContext:Wt,useEffect:Dr,useImperativeHandle:id,useInsertionEffect:ed,useLayoutEffect:nd,useMemo:sd,useReducer:Er,useRef:Ih,useState:function(){return Er(an)},useDebugValue:Mr,useDeferredValue:function(t,e){var n=Ht();return St===null?Cr(n,t,e):ud(n,St.memoizedState,t,e)},useTransition:function(){var t=Er(an)[0],e=Ht().memoizedState;return[typeof t=="boolean"?t:Qi(t),e]},useSyncExternalStore:Lh,useId:fd};ra.useCacheRefresh=hd,ra.useMemoCache=Tr,ra.useHostTransitionStatus=wr,ra.useFormState=Wh,ra.useActionState=Wh,ra.useOptimistic=function(t,e){var n=Ht();return St!==null?Xh(n,St,t,e):(n.baseState=t,[t,n.queue.dispatch])};function Vr(t,e,n,l){e=t.memoizedState,n=n(l,e),n=n==null?e:tt({},e,n),t.memoizedState=n,t.lanes===0&&(t.updateQueue.baseState=n)}var zr={isMounted:function(t){return(t=t._reactInternals)?W(t)===t:!1},enqueueSetState:function(t,e,n){t=t._reactInternals;var l=ye(),r=_n(l);r.payload=e,n!=null&&(r.callback=n),e=Vn(t,r,l),e!==null&&(le(e,t,l),Fi(e,t,l))},enqueueReplaceState:function(t,e,n){t=t._reactInternals;var l=ye(),r=_n(l);r.tag=1,r.payload=e,n!=null&&(r.callback=n),e=Vn(t,r,l),e!==null&&(le(e,t,l),Fi(e,t,l))},enqueueForceUpdate:function(t,e){t=t._reactInternals;var n=ye(),l=_n(n);l.tag=2,e!=null&&(l.callback=e),e=Vn(t,l,n),e!==null&&(le(e,t,n),Fi(e,t,n))}};function yd(t,e,n,l,r,o,y){return t=t.stateNode,typeof t.shouldComponentUpdate=="function"?t.shouldComponentUpdate(l,o,y):e.prototype&&e.prototype.isPureReactComponent?!zi(n,l)||!zi(r,o):!0}function gd(t,e,n,l){t=e.state,typeof e.componentWillReceiveProps=="function"&&e.componentWillReceiveProps(n,l),typeof e.UNSAFE_componentWillReceiveProps=="function"&&e.UNSAFE_componentWillReceiveProps(n,l),e.state!==t&&zr.enqueueReplaceState(e,e.state,null)}function oa(t,e){var n=e;if("ref"in e){n={};for(var l in e)l!=="ref"&&(n[l]=e[l])}if(t=t.defaultProps){n===e&&(n=tt({},n));for(var r in t)n[r]===void 0&&(n[r]=t[r])}return n}var Cs=typeof reportError=="function"?reportError:function(t){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var e=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof t=="object"&&t!==null&&typeof t.message=="string"?String(t.message):String(t),error:t});if(!window.dispatchEvent(e))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",t);return}console.error(t)};function vd(t){Cs(t)}function Sd(t){console.error(t)}function bd(t){Cs(t)}function Os(t,e){try{var n=t.onUncaughtError;n(e.value,{componentStack:e.stack})}catch(l){setTimeout(function(){throw l})}}function Td(t,e,n){try{var l=t.onCaughtError;l(n.value,{componentStack:n.stack,errorBoundary:e.tag===1?e.stateNode:null})}catch(r){setTimeout(function(){throw r})}}function Ur(t,e,n){return n=_n(n),n.tag=3,n.payload={element:null},n.callback=function(){Os(t,e)},n}function xd(t){return t=_n(t),t.tag=3,t}function Ed(t,e,n,l){var r=n.type.getDerivedStateFromError;if(typeof r=="function"){var o=l.value;t.payload=function(){return r(o)},t.callback=function(){Td(e,n,l)}}var y=n.stateNode;y!==null&&typeof y.componentDidCatch=="function"&&(t.callback=function(){Td(e,n,l),typeof r!="function"&&(Nn===null?Nn=new Set([this]):Nn.add(this));var v=l.stack;this.componentDidCatch(l.value,{componentStack:v!==null?v:""})})}function e1(t,e,n,l,r){if(n.flags|=32768,l!==null&&typeof l=="object"&&typeof l.then=="function"){if(e=n.alternate,e!==null&&Pi(e,n,r,!0),n=Me.current,n!==null){switch(n.tag){case 13:return Ke===null?yo():n.alternate===null&&Vt===0&&(Vt=3),n.flags&=-257,n.flags|=65536,n.lanes=r,l===cr?n.flags|=16384:(e=n.updateQueue,e===null?n.updateQueue=new Set([l]):e.add(l),vo(t,l,r)),!1;case 22:return n.flags|=65536,l===cr?n.flags|=16384:(e=n.updateQueue,e===null?(e={transitions:null,markerInstances:null,retryQueue:new Set([l])},n.updateQueue=e):(n=e.retryQueue,n===null?e.retryQueue=new Set([l]):n.add(l)),vo(t,l,r)),!1}throw Error(u(435,n.tag))}return vo(t,l,r),yo(),!1}if(pt)return e=Me.current,e!==null?((e.flags&65536)===0&&(e.flags|=256),e.flags|=65536,e.lanes=r,l!==or&&(t=Error(u(422),{cause:l}),Li(Ae(t,n)))):(l!==or&&(e=Error(u(423),{cause:l}),Li(Ae(e,n))),t=t.current.alternate,t.flags|=65536,r&=-r,t.lanes|=r,l=Ae(l,n),r=Ur(t.stateNode,l,r),Fr(t,r),Vt!==4&&(Vt=2)),!1;var o=Error(u(520),{cause:l});if(o=Ae(o,n),al===null?al=[o]:al.push(o),Vt!==4&&(Vt=2),e===null)return!0;l=Ae(l,n),n=e;do{switch(n.tag){case 3:return n.flags|=65536,t=r&-r,n.lanes|=t,t=Ur(n.stateNode,l,t),Fr(n,t),!1;case 1:if(e=n.type,o=n.stateNode,(n.flags&128)===0&&(typeof e.getDerivedStateFromError=="function"||o!==null&&typeof o.componentDidCatch=="function"&&(Nn===null||!Nn.has(o))))return n.flags|=65536,r&=-r,n.lanes|=r,r=xd(r),Ed(r,t,n,l),Fr(n,r),!1}n=n.return}while(n!==null);return!1}var Ad=Error(u(461)),Kt=!1;function Pt(t,e,n,l){e.child=t===null?Oh(e,null,n,l):ia(e,t.child,n,l)}function Rd(t,e,n,l,r){n=n.render;var o=e.ref;if("ref"in l){var y={};for(var v in l)v!=="ref"&&(y[v]=l[v])}else y=l;return fa(e),l=gr(t,e,n,y,o,r),v=vr(),t!==null&&!Kt?(Sr(t,e,r),ln(t,e,r)):(pt&&v&&ur(e),e.flags|=1,Pt(t,e,l,r),e.child)}function Dd(t,e,n,l,r){if(t===null){var o=n.type;return typeof o=="function"&&!ao(o)&&o.defaultProps===void 0&&n.compare===null?(e.tag=15,e.type=o,Md(t,e,o,l,r)):(t=Us(n.type,null,l,e,e.mode,r),t.ref=e.ref,t.return=e,e.child=t)}if(o=t.child,!Xr(t,r)){var y=o.memoizedProps;if(n=n.compare,n=n!==null?n:zi,n(y,l)&&t.ref===e.ref)return ln(t,e,r)}return e.flags|=1,t=jn(o,l),t.ref=e.ref,t.return=e,e.child=t}function Md(t,e,n,l,r){if(t!==null){var o=t.memoizedProps;if(zi(o,l)&&t.ref===e.ref)if(Kt=!1,e.pendingProps=l=o,Xr(t,r))(t.flags&131072)!==0&&(Kt=!0);else return e.lanes=t.lanes,ln(t,e,r)}return Br(t,e,n,l,r)}function Cd(t,e,n){var l=e.pendingProps,r=l.children,o=(e.stateNode._pendingVisibility&2)!==0,y=t!==null?t.memoizedState:null;if(ki(t,e),l.mode==="hidden"||o){if((e.flags&128)!==0){if(l=y!==null?y.baseLanes|n:n,t!==null){for(r=e.child=t.child,o=0;r!==null;)o=o|r.lanes|r.childLanes,r=r.sibling;e.childLanes=o&~l}else e.childLanes=0,e.child=null;return Od(t,e,l,n)}if((n&536870912)!==0)e.memoizedState={baseLanes:0,cachePool:null},t!==null&&Ss(e,y!==null?y.cachePool:null),y!==null?wh(e,y):fr(),_h(e);else return e.lanes=e.childLanes=536870912,Od(t,e,y!==null?y.baseLanes|n:n,n)}else y!==null?(Ss(e,y.cachePool),wh(e,y),Dn(),e.memoizedState=null):(t!==null&&Ss(e,null),fr(),Dn());return Pt(t,e,r,n),e.child}function Od(t,e,n,l){var r=pr();return r=r===null?null:{parent:Gt._currentValue,pool:r},e.memoizedState={baseLanes:n,cachePool:r},t!==null&&Ss(e,null),fr(),_h(e),t!==null&&Pi(t,e,l,!0),null}function ki(t,e){var n=e.ref;if(n===null)t!==null&&t.ref!==null&&(e.flags|=2097664);else{if(typeof n!="function"&&typeof n!="object")throw Error(u(284));(t===null||t.ref!==n)&&(e.flags|=2097664)}}function Br(t,e,n,l,r){return fa(e),n=gr(t,e,n,l,void 0,r),l=vr(),t!==null&&!Kt?(Sr(t,e,r),ln(t,e,r)):(pt&&l&&ur(e),e.flags|=1,Pt(t,e,n,r),e.child)}function wd(t,e,n,l,r,o){return fa(e),e.updateQueue=null,n=jh(e,l,n,r),Bh(t),l=vr(),t!==null&&!Kt?(Sr(t,e,o),ln(t,e,o)):(pt&&l&&ur(e),e.flags|=1,Pt(t,e,n,o),e.child)}function _d(t,e,n,l,r){if(fa(e),e.stateNode===null){var o=La,y=n.contextType;typeof y=="object"&&y!==null&&(o=Wt(y)),o=new n(l,o),e.memoizedState=o.state!==null&&o.state!==void 0?o.state:null,o.updater=zr,e.stateNode=o,o._reactInternals=e,o=e.stateNode,o.props=l,o.state=e.memoizedState,o.refs={},kr(e),y=n.contextType,o.context=typeof y=="object"&&y!==null?Wt(y):La,o.state=e.memoizedState,y=n.getDerivedStateFromProps,typeof y=="function"&&(Vr(e,n,y,l),o.state=e.memoizedState),typeof n.getDerivedStateFromProps=="function"||typeof o.getSnapshotBeforeUpdate=="function"||typeof o.UNSAFE_componentWillMount!="function"&&typeof o.componentWillMount!="function"||(y=o.state,typeof o.componentWillMount=="function"&&o.componentWillMount(),typeof o.UNSAFE_componentWillMount=="function"&&o.UNSAFE_componentWillMount(),y!==o.state&&zr.enqueueReplaceState(o,o.state,null),$i(e,l,o,r),Ji(),o.state=e.memoizedState),typeof o.componentDidMount=="function"&&(e.flags|=4194308),l=!0}else if(t===null){o=e.stateNode;var v=e.memoizedProps,T=oa(n,v);o.props=T;var D=o.context,B=n.contextType;y=La,typeof B=="object"&&B!==null&&(y=Wt(B));var q=n.getDerivedStateFromProps;B=typeof q=="function"||typeof o.getSnapshotBeforeUpdate=="function",v=e.pendingProps!==v,B||typeof o.UNSAFE_componentWillReceiveProps!="function"&&typeof o.componentWillReceiveProps!="function"||(v||D!==y)&&gd(e,o,l,y),wn=!1;var _=e.memoizedState;o.state=_,$i(e,l,o,r),Ji(),D=e.memoizedState,v||_!==D||wn?(typeof q=="function"&&(Vr(e,n,q,l),D=e.memoizedState),(T=wn||yd(e,n,T,l,_,D,y))?(B||typeof o.UNSAFE_componentWillMount!="function"&&typeof o.componentWillMount!="function"||(typeof o.componentWillMount=="function"&&o.componentWillMount(),typeof o.UNSAFE_componentWillMount=="function"&&o.UNSAFE_componentWillMount()),typeof o.componentDidMount=="function"&&(e.flags|=4194308)):(typeof o.componentDidMount=="function"&&(e.flags|=4194308),e.memoizedProps=l,e.memoizedState=D),o.props=l,o.state=D,o.context=y,l=T):(typeof o.componentDidMount=="function"&&(e.flags|=4194308),l=!1)}else{o=e.stateNode,Pr(t,e),y=e.memoizedProps,B=oa(n,y),o.props=B,q=e.pendingProps,_=o.context,D=n.contextType,T=La,typeof D=="object"&&D!==null&&(T=Wt(D)),v=n.getDerivedStateFromProps,(D=typeof v=="function"||typeof o.getSnapshotBeforeUpdate=="function")||typeof o.UNSAFE_componentWillReceiveProps!="function"&&typeof o.componentWillReceiveProps!="function"||(y!==q||_!==T)&&gd(e,o,l,T),wn=!1,_=e.memoizedState,o.state=_,$i(e,l,o,r),Ji();var U=e.memoizedState;y!==q||_!==U||wn||t!==null&&t.dependencies!==null&&ws(t.dependencies)?(typeof v=="function"&&(Vr(e,n,v,l),U=e.memoizedState),(B=wn||yd(e,n,B,l,_,U,T)||t!==null&&t.dependencies!==null&&ws(t.dependencies))?(D||typeof o.UNSAFE_componentWillUpdate!="function"&&typeof o.componentWillUpdate!="function"||(typeof o.componentWillUpdate=="function"&&o.componentWillUpdate(l,U,T),typeof o.UNSAFE_componentWillUpdate=="function"&&o.UNSAFE_componentWillUpdate(l,U,T)),typeof o.componentDidUpdate=="function"&&(e.flags|=4),typeof o.getSnapshotBeforeUpdate=="function"&&(e.flags|=1024)):(typeof o.componentDidUpdate!="function"||y===t.memoizedProps&&_===t.memoizedState||(e.flags|=4),typeof o.getSnapshotBeforeUpdate!="function"||y===t.memoizedProps&&_===t.memoizedState||(e.flags|=1024),e.memoizedProps=l,e.memoizedState=U),o.props=l,o.state=U,o.context=T,l=B):(typeof o.componentDidUpdate!="function"||y===t.memoizedProps&&_===t.memoizedState||(e.flags|=4),typeof o.getSnapshotBeforeUpdate!="function"||y===t.memoizedProps&&_===t.memoizedState||(e.flags|=1024),l=!1)}return o=l,ki(t,e),l=(e.flags&128)!==0,o||l?(o=e.stateNode,n=l&&typeof n.getDerivedStateFromError!="function"?null:o.render(),e.flags|=1,t!==null&&l?(e.child=ia(e,t.child,null,r),e.child=ia(e,null,n,r)):Pt(t,e,n,r),e.memoizedState=o.state,t=e.child):t=ln(t,e,r),t}function Vd(t,e,n,l){return ji(),e.flags|=256,Pt(t,e,n,l),e.child}var jr={dehydrated:null,treeContext:null,retryLane:0};function Lr(t){return{baseLanes:t,cachePool:Uh()}}function Nr(t,e,n){return t=t!==null?t.childLanes&~n:0,e&&(t|=_e),t}function zd(t,e,n){var l=e.pendingProps,r=!1,o=(e.flags&128)!==0,y;if((y=o)||(y=t!==null&&t.memoizedState===null?!1:(Yt.current&2)!==0),y&&(r=!0,e.flags&=-129),y=(e.flags&32)!==0,e.flags&=-33,t===null){if(pt){if(r?Rn(e):Dn(),pt){var v=kt,T;if(T=v){t:{for(T=v,v=Xe;T.nodeType!==8;){if(!v){v=null;break t}if(T=Ne(T.nextSibling),T===null){v=null;break t}}v=T}v!==null?(e.memoizedState={dehydrated:v,treeContext:ea!==null?{id:tn,overflow:en}:null,retryLane:536870912},T=we(18,null,null,0),T.stateNode=v,T.return=e,e.child=T,ie=e,kt=null,T=!0):T=!1}T||aa(e)}if(v=e.memoizedState,v!==null&&(v=v.dehydrated,v!==null))return v.data==="$!"?e.lanes=16:e.lanes=536870912,null;nn(e)}return v=l.children,l=l.fallback,r?(Dn(),r=e.mode,v=qr({mode:"hidden",children:v},r),l=da(l,r,n,null),v.return=e,l.return=e,v.sibling=l,e.child=v,r=e.child,r.memoizedState=Lr(n),r.childLanes=Nr(t,y,n),e.memoizedState=jr,l):(Rn(e),Hr(e,v))}if(T=t.memoizedState,T!==null&&(v=T.dehydrated,v!==null)){if(o)e.flags&256?(Rn(e),e.flags&=-257,e=Yr(t,e,n)):e.memoizedState!==null?(Dn(),e.child=t.child,e.flags|=128,e=null):(Dn(),r=l.fallback,v=e.mode,l=qr({mode:"visible",children:l.children},v),r=da(r,v,n,null),r.flags|=2,l.return=e,r.return=e,l.sibling=r,e.child=l,ia(e,t.child,null,n),l=e.child,l.memoizedState=Lr(n),l.childLanes=Nr(t,y,n),e.memoizedState=jr,e=r);else if(Rn(e),v.data==="$!"){if(y=v.nextSibling&&v.nextSibling.dataset,y)var D=y.dgst;y=D,l=Error(u(419)),l.stack="",l.digest=y,Li({value:l,source:null,stack:null}),e=Yr(t,e,n)}else if(Kt||Pi(t,e,n,!1),y=(n&t.childLanes)!==0,Kt||y){if(y=xt,y!==null){if(l=n&-n,(l&42)!==0)l=1;else switch(l){case 2:l=1;break;case 8:l=4;break;case 32:l=16;break;case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:l=64;break;case 268435456:l=134217728;break;default:l=0}if(l=(l&(y.suspendedLanes|n))!==0?0:l,l!==0&&l!==T.retryLane)throw T.retryLane=l,An(t,l),le(y,t,l),Ad}v.data==="$?"||yo(),e=Yr(t,e,n)}else v.data==="$?"?(e.flags|=128,e.child=t.child,e=y1.bind(null,t),v._reactRetry=e,e=null):(t=T.treeContext,kt=Ne(v.nextSibling),ie=e,pt=!0,je=null,Xe=!1,t!==null&&(Re[De++]=tn,Re[De++]=en,Re[De++]=ea,tn=t.id,en=t.overflow,ea=e),e=Hr(e,l.children),e.flags|=4096);return e}return r?(Dn(),r=l.fallback,v=e.mode,T=t.child,D=T.sibling,l=jn(T,{mode:"hidden",children:l.children}),l.subtreeFlags=T.subtreeFlags&31457280,D!==null?r=jn(D,r):(r=da(r,v,n,null),r.flags|=2),r.return=e,l.return=e,l.sibling=r,e.child=l,l=r,r=e.child,v=t.child.memoizedState,v===null?v=Lr(n):(T=v.cachePool,T!==null?(D=Gt._currentValue,T=T.parent!==D?{parent:D,pool:D}:T):T=Uh(),v={baseLanes:v.baseLanes|n,cachePool:T}),r.memoizedState=v,r.childLanes=Nr(t,y,n),e.memoizedState=jr,l):(Rn(e),n=t.child,t=n.sibling,n=jn(n,{mode:"visible",children:l.children}),n.return=e,n.sibling=null,t!==null&&(y=e.deletions,y===null?(e.deletions=[t],e.flags|=16):y.push(t)),e.child=n,e.memoizedState=null,n)}function Hr(t,e){return e=qr({mode:"visible",children:e},t.mode),e.return=t,t.child=e}function qr(t,e){return lm(t,e,0,null)}function Yr(t,e,n){return ia(e,t.child,null,n),t=Hr(e,e.pendingProps.children),t.flags|=2,e.memoizedState=null,t}function Ud(t,e,n){t.lanes|=e;var l=t.alternate;l!==null&&(l.lanes|=e),Qr(t.return,e,n)}function Gr(t,e,n,l,r){var o=t.memoizedState;o===null?t.memoizedState={isBackwards:e,rendering:null,renderingStartTime:0,last:l,tail:n,tailMode:r}:(o.isBackwards=e,o.rendering=null,o.renderingStartTime=0,o.last=l,o.tail=n,o.tailMode=r)}function Bd(t,e,n){var l=e.pendingProps,r=l.revealOrder,o=l.tail;if(Pt(t,e,l.children,n),l=Yt.current,(l&2)!==0)l=l&1|2,e.flags|=128;else{if(t!==null&&(t.flags&128)!==0)t:for(t=e.child;t!==null;){if(t.tag===13)t.memoizedState!==null&&Ud(t,n,e);else if(t.tag===19)Ud(t,n,e);else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break t;for(;t.sibling===null;){if(t.return===null||t.return===e)break t;t=t.return}t.sibling.return=t.return,t=t.sibling}l&=1}switch(Rt(Yt,l),r){case"forwards":for(n=e.child,r=null;n!==null;)t=n.alternate,t!==null&&vs(t)===null&&(r=n),n=n.sibling;n=r,n===null?(r=e.child,e.child=null):(r=n.sibling,n.sibling=null),Gr(e,!1,r,n,o);break;case"backwards":for(n=null,r=e.child,e.child=null;r!==null;){if(t=r.alternate,t!==null&&vs(t)===null){e.child=r;break}t=r.sibling,r.sibling=n,n=r,r=t}Gr(e,!0,n,null,o);break;case"together":Gr(e,!1,null,null,void 0);break;default:e.memoizedState=null}return e.child}function ln(t,e,n){if(t!==null&&(e.dependencies=t.dependencies),Ln|=e.lanes,(n&e.childLanes)===0)if(t!==null){if(Pi(t,e,n,!1),(n&e.childLanes)===0)return null}else return null;if(t!==null&&e.child!==t.child)throw Error(u(153));if(e.child!==null){for(t=e.child,n=jn(t,t.pendingProps),e.child=n,n.return=e;t.sibling!==null;)t=t.sibling,n=n.sibling=jn(t,t.pendingProps),n.return=e;n.sibling=null}return e.child}function Xr(t,e){return(t.lanes&e)!==0?!0:(t=t.dependencies,!!(t!==null&&ws(t)))}function n1(t,e,n){switch(e.tag){case 3:Zl(e,e.stateNode.containerInfo),On(e,Gt,t.memoizedState.cache),ji();break;case 27:case 5:zu(e);break;case 4:Zl(e,e.stateNode.containerInfo);break;case 10:On(e,e.type,e.memoizedProps.value);break;case 13:var l=e.memoizedState;if(l!==null)return l.dehydrated!==null?(Rn(e),e.flags|=128,null):(n&e.child.childLanes)!==0?zd(t,e,n):(Rn(e),t=ln(t,e,n),t!==null?t.sibling:null);Rn(e);break;case 19:var r=(t.flags&128)!==0;if(l=(n&e.childLanes)!==0,l||(Pi(t,e,n,!1),l=(n&e.childLanes)!==0),r){if(l)return Bd(t,e,n);e.flags|=128}if(r=e.memoizedState,r!==null&&(r.rendering=null,r.tail=null,r.lastEffect=null),Rt(Yt,Yt.current),l)break;return null;case 22:case 23:return e.lanes=0,Cd(t,e,n);case 24:On(e,Gt,t.memoizedState.cache)}return ln(t,e,n)}function jd(t,e,n){if(t!==null)if(t.memoizedProps!==e.pendingProps)Kt=!0;else{if(!Xr(t,n)&&(e.flags&128)===0)return Kt=!1,n1(t,e,n);Kt=(t.flags&131072)!==0}else Kt=!1,pt&&(e.flags&1048576)!==0&&bh(e,ds,e.index);switch(e.lanes=0,e.tag){case 16:t:{t=e.pendingProps;var l=e.elementType,r=l._init;if(l=r(l._payload),e.type=l,typeof l=="function")ao(l)?(t=oa(l,t),e.tag=1,e=_d(null,e,l,t,n)):(e.tag=0,e=Br(null,e,l,t,n));else{if(l!=null){if(r=l.$$typeof,r===M){e.tag=11,e=Rd(null,e,l,t,n);break t}else if(r===O){e.tag=14,e=Dd(null,e,l,t,n);break t}}throw e=J(l)||l,Error(u(306,e,""))}}return e;case 0:return Br(t,e,e.type,e.pendingProps,n);case 1:return l=e.type,r=oa(l,e.pendingProps),_d(t,e,l,r,n);case 3:t:{if(Zl(e,e.stateNode.containerInfo),t===null)throw Error(u(387));var o=e.pendingProps;r=e.memoizedState,l=r.element,Pr(t,e),$i(e,o,null,n);var y=e.memoizedState;if(o=y.cache,On(e,Gt,o),o!==r.cache&&Zr(e,[Gt],n,!0),Ji(),o=y.element,r.isDehydrated)if(r={element:o,isDehydrated:!1,cache:y.cache},e.updateQueue.baseState=r,e.memoizedState=r,e.flags&256){e=Vd(t,e,o,n);break t}else if(o!==l){l=Ae(Error(u(424)),e),Li(l),e=Vd(t,e,o,n);break t}else for(kt=Ne(e.stateNode.containerInfo.firstChild),ie=e,pt=!0,je=null,Xe=!0,n=Oh(e,null,o,n),e.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(ji(),o===l){e=ln(t,e,n);break t}Pt(t,e,o,n)}e=e.child}return e;case 26:return ki(t,e),t===null?(n=Hm(e.type,null,e.pendingProps,null))?e.memoizedState=n:pt||(n=e.type,t=e.pendingProps,l=Zs(bn.current).createElement(n),l[$t]=e,l[ue]=t,Ft(l,n,t),Xt(l),e.stateNode=l):e.memoizedState=Hm(e.type,t.memoizedProps,e.pendingProps,t.memoizedState),null;case 27:return zu(e),t===null&&pt&&(l=e.stateNode=jm(e.type,e.pendingProps,bn.current),ie=e,Xe=!0,kt=Ne(l.firstChild)),l=e.pendingProps.children,t!==null||pt?Pt(t,e,l,n):e.child=ia(e,null,l,n),ki(t,e),e.child;case 5:return t===null&&pt&&((r=l=kt)&&(l=V1(l,e.type,e.pendingProps,Xe),l!==null?(e.stateNode=l,ie=e,kt=Ne(l.firstChild),Xe=!1,r=!0):r=!1),r||aa(e)),zu(e),r=e.type,o=e.pendingProps,y=t!==null?t.memoizedProps:null,l=o.children,wo(r,o)?l=null:y!==null&&wo(r,y)&&(e.flags|=32),e.memoizedState!==null&&(r=gr(t,e,F0,null,null,n),hl._currentValue=r),ki(t,e),Pt(t,e,l,n),e.child;case 6:return t===null&&pt&&((t=n=kt)&&(n=z1(n,e.pendingProps,Xe),n!==null?(e.stateNode=n,ie=e,kt=null,t=!0):t=!1),t||aa(e)),null;case 13:return zd(t,e,n);case 4:return Zl(e,e.stateNode.containerInfo),l=e.pendingProps,t===null?e.child=ia(e,null,l,n):Pt(t,e,l,n),e.child;case 11:return Rd(t,e,e.type,e.pendingProps,n);case 7:return Pt(t,e,e.pendingProps,n),e.child;case 8:return Pt(t,e,e.pendingProps.children,n),e.child;case 12:return Pt(t,e,e.pendingProps.children,n),e.child;case 10:return l=e.pendingProps,On(e,e.type,l.value),Pt(t,e,l.children,n),e.child;case 9:return r=e.type._context,l=e.pendingProps.children,fa(e),r=Wt(r),l=l(r),e.flags|=1,Pt(t,e,l,n),e.child;case 14:return Dd(t,e,e.type,e.pendingProps,n);case 15:return Md(t,e,e.type,e.pendingProps,n);case 19:return Bd(t,e,n);case 22:return Cd(t,e,n);case 24:return fa(e),l=Wt(Gt),t===null?(r=pr(),r===null&&(r=xt,o=dr(),r.pooledCache=o,o.refCount++,o!==null&&(r.pooledCacheLanes|=n),r=o),e.memoizedState={parent:l,cache:r},kr(e),On(e,Gt,r)):((t.lanes&n)!==0&&(Pr(t,e),$i(e,null,null,n),Ji()),r=t.memoizedState,o=e.memoizedState,r.parent!==l?(r={parent:l,cache:l},e.memoizedState=r,e.lanes===0&&(e.memoizedState=e.updateQueue.baseState=r),On(e,Gt,l)):(l=o.cache,On(e,Gt,l),l!==r.cache&&Zr(e,[Gt],n,!0))),Pt(t,e,e.pendingProps.children,n),e.child;case 29:throw e.pendingProps}throw Error(u(156,e.tag))}var Kr=vt(null),ca=null,sn=null;function On(t,e,n){Rt(Kr,e._currentValue),e._currentValue=n}function un(t){t._currentValue=Kr.current,Lt(Kr)}function Qr(t,e,n){for(;t!==null;){var l=t.alternate;if((t.childLanes&e)!==e?(t.childLanes|=e,l!==null&&(l.childLanes|=e)):l!==null&&(l.childLanes&e)!==e&&(l.childLanes|=e),t===n)break;t=t.return}}function Zr(t,e,n,l){var r=t.child;for(r!==null&&(r.return=t);r!==null;){var o=r.dependencies;if(o!==null){var y=r.child;o=o.firstContext;t:for(;o!==null;){var v=o;o=r;for(var T=0;T<e.length;T++)if(v.context===e[T]){o.lanes|=n,v=o.alternate,v!==null&&(v.lanes|=n),Qr(o.return,n,t),l||(y=null);break t}o=v.next}}else if(r.tag===18){if(y=r.return,y===null)throw Error(u(341));y.lanes|=n,o=y.alternate,o!==null&&(o.lanes|=n),Qr(y,n,t),y=null}else y=r.child;if(y!==null)y.return=r;else for(y=r;y!==null;){if(y===t){y=null;break}if(r=y.sibling,r!==null){r.return=y.return,y=r;break}y=y.return}r=y}}function Pi(t,e,n,l){t=null;for(var r=e,o=!1;r!==null;){if(!o){if((r.flags&524288)!==0)o=!0;else if((r.flags&262144)!==0)break}if(r.tag===10){var y=r.alternate;if(y===null)throw Error(u(387));if(y=y.memoizedProps,y!==null){var v=r.type;he(r.pendingProps.value,y.value)||(t!==null?t.push(v):t=[v])}}else if(r===Ql.current){if(y=r.alternate,y===null)throw Error(u(387));y.memoizedState.memoizedState!==r.memoizedState.memoizedState&&(t!==null?t.push(hl):t=[hl])}r=r.return}t!==null&&Zr(e,t,n,l),e.flags|=262144}function ws(t){for(t=t.firstContext;t!==null;){if(!he(t.context._currentValue,t.memoizedValue))return!0;t=t.next}return!1}function fa(t){ca=t,sn=null,t=t.dependencies,t!==null&&(t.firstContext=null)}function Wt(t){return Ld(ca,t)}function _s(t,e){return ca===null&&fa(t),Ld(t,e)}function Ld(t,e){var n=e._currentValue;if(e={context:e,memoizedValue:n,next:null},sn===null){if(t===null)throw Error(u(308));sn=e,t.dependencies={lanes:0,firstContext:e},t.flags|=524288}else sn=sn.next=e;return n}var wn=!1;function kr(t){t.updateQueue={baseState:t.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function Pr(t,e){t=t.updateQueue,e.updateQueue===t&&(e.updateQueue={baseState:t.baseState,firstBaseUpdate:t.firstBaseUpdate,lastBaseUpdate:t.lastBaseUpdate,shared:t.shared,callbacks:null})}function _n(t){return{lane:t,tag:0,payload:null,callback:null,next:null}}function Vn(t,e,n){var l=t.updateQueue;if(l===null)return null;if(l=l.shared,(Mt&2)!==0){var r=l.pending;return r===null?e.next=e:(e.next=r.next,r.next=e),l.pending=e,e=fs(t),vh(t,null,n),e}return cs(t,l,e,n),fs(t)}function Fi(t,e,n){if(e=e.updateQueue,e!==null&&(e=e.shared,(n&4194176)!==0)){var l=e.lanes;l&=t.pendingLanes,n|=l,e.lanes=n,Df(t,n)}}function Fr(t,e){var n=t.updateQueue,l=t.alternate;if(l!==null&&(l=l.updateQueue,n===l)){var r=null,o=null;if(n=n.firstBaseUpdate,n!==null){do{var y={lane:n.lane,tag:n.tag,payload:n.payload,callback:null,next:null};o===null?r=o=y:o=o.next=y,n=n.next}while(n!==null);o===null?r=o=e:o=o.next=e}else r=o=e;n={baseState:l.baseState,firstBaseUpdate:r,lastBaseUpdate:o,shared:l.shared,callbacks:l.callbacks},t.updateQueue=n;return}t=n.lastBaseUpdate,t===null?n.firstBaseUpdate=e:t.next=e,n.lastBaseUpdate=e}var Jr=!1;function Ji(){if(Jr){var t=Xa;if(t!==null)throw t}}function $i(t,e,n,l){Jr=!1;var r=t.updateQueue;wn=!1;var o=r.firstBaseUpdate,y=r.lastBaseUpdate,v=r.shared.pending;if(v!==null){r.shared.pending=null;var T=v,D=T.next;T.next=null,y===null?o=D:y.next=D,y=T;var B=t.alternate;B!==null&&(B=B.updateQueue,v=B.lastBaseUpdate,v!==y&&(v===null?B.firstBaseUpdate=D:v.next=D,B.lastBaseUpdate=T))}if(o!==null){var q=r.baseState;y=0,B=D=T=null,v=o;do{var _=v.lane&-536870913,U=_!==v.lane;if(U?(mt&_)===_:(l&_)===_){_!==0&&_===Ga&&(Jr=!0),B!==null&&(B=B.next={lane:0,tag:v.tag,payload:v.payload,callback:null,next:null});t:{var F=t,lt=v;_=e;var zt=n;switch(lt.tag){case 1:if(F=lt.payload,typeof F=="function"){q=F.call(zt,q,_);break t}q=F;break t;case 3:F.flags=F.flags&-65537|128;case 0:if(F=lt.payload,_=typeof F=="function"?F.call(zt,q,_):F,_==null)break t;q=tt({},q,_);break t;case 2:wn=!0}}_=v.callback,_!==null&&(t.flags|=64,U&&(t.flags|=8192),U=r.callbacks,U===null?r.callbacks=[_]:U.push(_))}else U={lane:_,tag:v.tag,payload:v.payload,callback:v.callback,next:null},B===null?(D=B=U,T=q):B=B.next=U,y|=_;if(v=v.next,v===null){if(v=r.shared.pending,v===null)break;U=v,v=U.next,U.next=null,r.lastBaseUpdate=U,r.shared.pending=null}}while(!0);B===null&&(T=q),r.baseState=T,r.firstBaseUpdate=D,r.lastBaseUpdate=B,o===null&&(r.shared.lanes=0),Ln|=y,t.lanes=y,t.memoizedState=q}}function Nd(t,e){if(typeof t!="function")throw Error(u(191,t));t.call(e)}function Hd(t,e){var n=t.callbacks;if(n!==null)for(t.callbacks=null,t=0;t<n.length;t++)Nd(n[t],e)}function Wi(t,e){try{var n=e.updateQueue,l=n!==null?n.lastEffect:null;if(l!==null){var r=l.next;n=r;do{if((n.tag&t)===t){l=void 0;var o=n.create,y=n.inst;l=o(),y.destroy=l}n=n.next}while(n!==r)}}catch(v){Tt(e,e.return,v)}}function zn(t,e,n){try{var l=e.updateQueue,r=l!==null?l.lastEffect:null;if(r!==null){var o=r.next;l=o;do{if((l.tag&t)===t){var y=l.inst,v=y.destroy;if(v!==void 0){y.destroy=void 0,r=e;var T=n;try{v()}catch(D){Tt(r,T,D)}}}l=l.next}while(l!==o)}}catch(D){Tt(e,e.return,D)}}function qd(t){var e=t.updateQueue;if(e!==null){var n=t.stateNode;try{Hd(e,n)}catch(l){Tt(t,t.return,l)}}}function Yd(t,e,n){n.props=oa(t.type,t.memoizedProps),n.state=t.memoizedState;try{n.componentWillUnmount()}catch(l){Tt(t,e,l)}}function ha(t,e){try{var n=t.ref;if(n!==null){var l=t.stateNode;switch(t.tag){case 26:case 27:case 5:var r=l;break;default:r=l}typeof n=="function"?t.refCleanup=n(r):n.current=r}}catch(o){Tt(t,e,o)}}function de(t,e){var n=t.ref,l=t.refCleanup;if(n!==null)if(typeof l=="function")try{l()}catch(r){Tt(t,e,r)}finally{t.refCleanup=null,t=t.alternate,t!=null&&(t.refCleanup=null)}else if(typeof n=="function")try{n(null)}catch(r){Tt(t,e,r)}else n.current=null}function Gd(t){var e=t.type,n=t.memoizedProps,l=t.stateNode;try{t:switch(e){case"button":case"input":case"select":case"textarea":n.autoFocus&&l.focus();break t;case"img":n.src?l.src=n.src:n.srcSet&&(l.srcset=n.srcSet)}}catch(r){Tt(t,t.return,r)}}function Xd(t,e,n){try{var l=t.stateNode;M1(l,t.type,n,e),l[ue]=e}catch(r){Tt(t,t.return,r)}}function Kd(t){return t.tag===5||t.tag===3||t.tag===26||t.tag===27||t.tag===4}function $r(t){t:for(;;){for(;t.sibling===null;){if(t.return===null||Kd(t.return))return null;t=t.return}for(t.sibling.return=t.return,t=t.sibling;t.tag!==5&&t.tag!==6&&t.tag!==27&&t.tag!==18;){if(t.flags&2||t.child===null||t.tag===4)continue t;t.child.return=t,t=t.child}if(!(t.flags&2))return t.stateNode}}function Wr(t,e,n){var l=t.tag;if(l===5||l===6)t=t.stateNode,e?n.nodeType===8?n.parentNode.insertBefore(t,e):n.insertBefore(t,e):(n.nodeType===8?(e=n.parentNode,e.insertBefore(t,n)):(e=n,e.appendChild(t)),n=n._reactRootContainer,n!=null||e.onclick!==null||(e.onclick=Qs));else if(l!==4&&l!==27&&(t=t.child,t!==null))for(Wr(t,e,n),t=t.sibling;t!==null;)Wr(t,e,n),t=t.sibling}function Vs(t,e,n){var l=t.tag;if(l===5||l===6)t=t.stateNode,e?n.insertBefore(t,e):n.appendChild(t);else if(l!==4&&l!==27&&(t=t.child,t!==null))for(Vs(t,e,n),t=t.sibling;t!==null;)Vs(t,e,n),t=t.sibling}var rn=!1,_t=!1,Ir=!1,Qd=typeof WeakSet=="function"?WeakSet:Set,Qt=null,Zd=!1;function a1(t,e){if(t=t.containerInfo,Co=Ws,t=oh(t),er(t)){if("selectionStart"in t)var n={start:t.selectionStart,end:t.selectionEnd};else t:{n=(n=t.ownerDocument)&&n.defaultView||window;var l=n.getSelection&&n.getSelection();if(l&&l.rangeCount!==0){n=l.anchorNode;var r=l.anchorOffset,o=l.focusNode;l=l.focusOffset;try{n.nodeType,o.nodeType}catch{n=null;break t}var y=0,v=-1,T=-1,D=0,B=0,q=t,_=null;e:for(;;){for(var U;q!==n||r!==0&&q.nodeType!==3||(v=y+r),q!==o||l!==0&&q.nodeType!==3||(T=y+l),q.nodeType===3&&(y+=q.nodeValue.length),(U=q.firstChild)!==null;)_=q,q=U;for(;;){if(q===t)break e;if(_===n&&++D===r&&(v=y),_===o&&++B===l&&(T=y),(U=q.nextSibling)!==null)break;q=_,_=q.parentNode}q=U}n=v===-1||T===-1?null:{start:v,end:T}}else n=null}n=n||{start:0,end:0}}else n=null;for(Oo={focusedElem:t,selectionRange:n},Ws=!1,Qt=e;Qt!==null;)if(e=Qt,t=e.child,(e.subtreeFlags&1028)!==0&&t!==null)t.return=e,Qt=t;else for(;Qt!==null;){switch(e=Qt,o=e.alternate,t=e.flags,e.tag){case 0:break;case 11:case 15:break;case 1:if((t&1024)!==0&&o!==null){t=void 0,n=e,r=o.memoizedProps,o=o.memoizedState,l=n.stateNode;try{var F=oa(n.type,r,n.elementType===n.type);t=l.getSnapshotBeforeUpdate(F,o),l.__reactInternalSnapshotBeforeUpdate=t}catch(lt){Tt(n,n.return,lt)}}break;case 3:if((t&1024)!==0){if(t=e.stateNode.containerInfo,n=t.nodeType,n===9)zo(t);else if(n===1)switch(t.nodeName){case"HEAD":case"HTML":case"BODY":zo(t);break;default:t.textContent=""}}break;case 5:case 26:case 27:case 6:case 4:case 17:break;default:if((t&1024)!==0)throw Error(u(163))}if(t=e.sibling,t!==null){t.return=e.return,Qt=t;break}Qt=e.return}return F=Zd,Zd=!1,F}function kd(t,e,n){var l=n.flags;switch(n.tag){case 0:case 11:case 15:cn(t,n),l&4&&Wi(5,n);break;case 1:if(cn(t,n),l&4)if(t=n.stateNode,e===null)try{t.componentDidMount()}catch(v){Tt(n,n.return,v)}else{var r=oa(n.type,e.memoizedProps);e=e.memoizedState;try{t.componentDidUpdate(r,e,t.__reactInternalSnapshotBeforeUpdate)}catch(v){Tt(n,n.return,v)}}l&64&&qd(n),l&512&&ha(n,n.return);break;case 3:if(cn(t,n),l&64&&(l=n.updateQueue,l!==null)){if(t=null,n.child!==null)switch(n.child.tag){case 27:case 5:t=n.child.stateNode;break;case 1:t=n.child.stateNode}try{Hd(l,t)}catch(v){Tt(n,n.return,v)}}break;case 26:cn(t,n),l&512&&ha(n,n.return);break;case 27:case 5:cn(t,n),e===null&&l&4&&Gd(n),l&512&&ha(n,n.return);break;case 12:cn(t,n);break;case 13:cn(t,n),l&4&&Jd(t,n);break;case 22:if(r=n.memoizedState!==null||rn,!r){e=e!==null&&e.memoizedState!==null||_t;var o=rn,y=_t;rn=r,(_t=e)&&!y?Un(t,n,(n.subtreeFlags&8772)!==0):cn(t,n),rn=o,_t=y}l&512&&(n.memoizedProps.mode==="manual"?ha(n,n.return):de(n,n.return));break;default:cn(t,n)}}function Pd(t){var e=t.alternate;e!==null&&(t.alternate=null,Pd(e)),t.child=null,t.deletions=null,t.sibling=null,t.tag===5&&(e=t.stateNode,e!==null&&Hu(e)),t.stateNode=null,t.return=null,t.dependencies=null,t.memoizedProps=null,t.memoizedState=null,t.pendingProps=null,t.stateNode=null,t.updateQueue=null}var qt=null,me=!1;function on(t,e,n){for(n=n.child;n!==null;)Fd(t,e,n),n=n.sibling}function Fd(t,e,n){if(ce&&typeof ce.onCommitFiberUnmount=="function")try{ce.onCommitFiberUnmount(Ti,n)}catch{}switch(n.tag){case 26:_t||de(n,e),on(t,e,n),n.memoizedState?n.memoizedState.count--:n.stateNode&&(n=n.stateNode,n.parentNode.removeChild(n));break;case 27:_t||de(n,e);var l=qt,r=me;for(qt=n.stateNode,on(t,e,n),n=n.stateNode,e=n.attributes;e.length;)n.removeAttributeNode(e[0]);Hu(n),qt=l,me=r;break;case 5:_t||de(n,e);case 6:r=qt;var o=me;if(qt=null,on(t,e,n),qt=r,me=o,qt!==null)if(me)try{t=qt,l=n.stateNode,t.nodeType===8?t.parentNode.removeChild(l):t.removeChild(l)}catch(y){Tt(n,e,y)}else try{qt.removeChild(n.stateNode)}catch(y){Tt(n,e,y)}break;case 18:qt!==null&&(me?(e=qt,n=n.stateNode,e.nodeType===8?Vo(e.parentNode,n):e.nodeType===1&&Vo(e,n),yl(e)):Vo(qt,n.stateNode));break;case 4:l=qt,r=me,qt=n.stateNode.containerInfo,me=!0,on(t,e,n),qt=l,me=r;break;case 0:case 11:case 14:case 15:_t||zn(2,n,e),_t||zn(4,n,e),on(t,e,n);break;case 1:_t||(de(n,e),l=n.stateNode,typeof l.componentWillUnmount=="function"&&Yd(n,e,l)),on(t,e,n);break;case 21:on(t,e,n);break;case 22:_t||de(n,e),_t=(l=_t)||n.memoizedState!==null,on(t,e,n),_t=l;break;default:on(t,e,n)}}function Jd(t,e){if(e.memoizedState===null&&(t=e.alternate,t!==null&&(t=t.memoizedState,t!==null&&(t=t.dehydrated,t!==null))))try{yl(t)}catch(n){Tt(e,e.return,n)}}function i1(t){switch(t.tag){case 13:case 19:var e=t.stateNode;return e===null&&(e=t.stateNode=new Qd),e;case 22:return t=t.stateNode,e=t._retryCache,e===null&&(e=t._retryCache=new Qd),e;default:throw Error(u(435,t.tag))}}function to(t,e){var n=i1(t);e.forEach(function(l){var r=g1.bind(null,t,l);n.has(l)||(n.add(l),l.then(r,r))})}function Ce(t,e){var n=e.deletions;if(n!==null)for(var l=0;l<n.length;l++){var r=n[l],o=t,y=e,v=y;t:for(;v!==null;){switch(v.tag){case 27:case 5:qt=v.stateNode,me=!1;break t;case 3:qt=v.stateNode.containerInfo,me=!0;break t;case 4:qt=v.stateNode.containerInfo,me=!0;break t}v=v.return}if(qt===null)throw Error(u(160));Fd(o,y,r),qt=null,me=!1,o=r.alternate,o!==null&&(o.return=null),r.return=null}if(e.subtreeFlags&13878)for(e=e.child;e!==null;)$d(e,t),e=e.sibling}var Le=null;function $d(t,e){var n=t.alternate,l=t.flags;switch(t.tag){case 0:case 11:case 14:case 15:Ce(e,t),Oe(t),l&4&&(zn(3,t,t.return),Wi(3,t),zn(5,t,t.return));break;case 1:Ce(e,t),Oe(t),l&512&&(_t||n===null||de(n,n.return)),l&64&&rn&&(t=t.updateQueue,t!==null&&(l=t.callbacks,l!==null&&(n=t.shared.hiddenCallbacks,t.shared.hiddenCallbacks=n===null?l:n.concat(l))));break;case 26:var r=Le;if(Ce(e,t),Oe(t),l&512&&(_t||n===null||de(n,n.return)),l&4){var o=n!==null?n.memoizedState:null;if(l=t.memoizedState,n===null)if(l===null)if(t.stateNode===null){t:{l=t.type,n=t.memoizedProps,r=r.ownerDocument||r;e:switch(l){case"title":o=r.getElementsByTagName("title")[0],(!o||o[Ai]||o[$t]||o.namespaceURI==="http://www.w3.org/2000/svg"||o.hasAttribute("itemprop"))&&(o=r.createElement(l),r.head.insertBefore(o,r.querySelector("head > title"))),Ft(o,l,n),o[$t]=t,Xt(o),l=o;break t;case"link":var y=Gm("link","href",r).get(l+(n.href||""));if(y){for(var v=0;v<y.length;v++)if(o=y[v],o.getAttribute("href")===(n.href==null?null:n.href)&&o.getAttribute("rel")===(n.rel==null?null:n.rel)&&o.getAttribute("title")===(n.title==null?null:n.title)&&o.getAttribute("crossorigin")===(n.crossOrigin==null?null:n.crossOrigin)){y.splice(v,1);break e}}o=r.createElement(l),Ft(o,l,n),r.head.appendChild(o);break;case"meta":if(y=Gm("meta","content",r).get(l+(n.content||""))){for(v=0;v<y.length;v++)if(o=y[v],o.getAttribute("content")===(n.content==null?null:""+n.content)&&o.getAttribute("name")===(n.name==null?null:n.name)&&o.getAttribute("property")===(n.property==null?null:n.property)&&o.getAttribute("http-equiv")===(n.httpEquiv==null?null:n.httpEquiv)&&o.getAttribute("charset")===(n.charSet==null?null:n.charSet)){y.splice(v,1);break e}}o=r.createElement(l),Ft(o,l,n),r.head.appendChild(o);break;default:throw Error(u(468,l))}o[$t]=t,Xt(o),l=o}t.stateNode=l}else Xm(r,t.type,t.stateNode);else t.stateNode=Ym(r,l,t.memoizedProps);else o!==l?(o===null?n.stateNode!==null&&(n=n.stateNode,n.parentNode.removeChild(n)):o.count--,l===null?Xm(r,t.type,t.stateNode):Ym(r,l,t.memoizedProps)):l===null&&t.stateNode!==null&&Xd(t,t.memoizedProps,n.memoizedProps)}break;case 27:if(l&4&&t.alternate===null){r=t.stateNode,o=t.memoizedProps;try{for(var T=r.firstChild;T;){var D=T.nextSibling,B=T.nodeName;T[Ai]||B==="HEAD"||B==="BODY"||B==="SCRIPT"||B==="STYLE"||B==="LINK"&&T.rel.toLowerCase()==="stylesheet"||r.removeChild(T),T=D}for(var q=t.type,_=r.attributes;_.length;)r.removeAttributeNode(_[0]);Ft(r,q,o),r[$t]=t,r[ue]=o}catch(F){Tt(t,t.return,F)}}case 5:if(Ce(e,t),Oe(t),l&512&&(_t||n===null||de(n,n.return)),t.flags&32){r=t.stateNode;try{wa(r,"")}catch(F){Tt(t,t.return,F)}}l&4&&t.stateNode!=null&&(r=t.memoizedProps,Xd(t,r,n!==null?n.memoizedProps:r)),l&1024&&(Ir=!0);break;case 6:if(Ce(e,t),Oe(t),l&4){if(t.stateNode===null)throw Error(u(162));l=t.memoizedProps,n=t.stateNode;try{n.nodeValue=l}catch(F){Tt(t,t.return,F)}}break;case 3:if(Fs=null,r=Le,Le=ks(e.containerInfo),Ce(e,t),Le=r,Oe(t),l&4&&n!==null&&n.memoizedState.isDehydrated)try{yl(e.containerInfo)}catch(F){Tt(t,t.return,F)}Ir&&(Ir=!1,Wd(t));break;case 4:l=Le,Le=ks(t.stateNode.containerInfo),Ce(e,t),Oe(t),Le=l;break;case 12:Ce(e,t),Oe(t);break;case 13:Ce(e,t),Oe(t),t.child.flags&8192&&t.memoizedState!==null!=(n!==null&&n.memoizedState!==null)&&(oo=Ge()),l&4&&(l=t.updateQueue,l!==null&&(t.updateQueue=null,to(t,l)));break;case 22:if(l&512&&(_t||n===null||de(n,n.return)),T=t.memoizedState!==null,D=n!==null&&n.memoizedState!==null,B=rn,q=_t,rn=B||T,_t=q||D,Ce(e,t),_t=q,rn=B,Oe(t),e=t.stateNode,e._current=t,e._visibility&=-3,e._visibility|=e._pendingVisibility&2,l&8192&&(e._visibility=T?e._visibility&-2:e._visibility|1,T&&(e=rn||_t,n===null||D||e||ka(t)),t.memoizedProps===null||t.memoizedProps.mode!=="manual"))t:for(n=null,e=t;;){if(e.tag===5||e.tag===26||e.tag===27){if(n===null){D=n=e;try{if(r=D.stateNode,T)o=r.style,typeof o.setProperty=="function"?o.setProperty("display","none","important"):o.display="none";else{y=D.stateNode,v=D.memoizedProps.style;var U=v!=null&&v.hasOwnProperty("display")?v.display:null;y.style.display=U==null||typeof U=="boolean"?"":(""+U).trim()}}catch(F){Tt(D,D.return,F)}}}else if(e.tag===6){if(n===null){D=e;try{D.stateNode.nodeValue=T?"":D.memoizedProps}catch(F){Tt(D,D.return,F)}}}else if((e.tag!==22&&e.tag!==23||e.memoizedState===null||e===t)&&e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break t;for(;e.sibling===null;){if(e.return===null||e.return===t)break t;n===e&&(n=null),e=e.return}n===e&&(n=null),e.sibling.return=e.return,e=e.sibling}l&4&&(l=t.updateQueue,l!==null&&(n=l.retryQueue,n!==null&&(l.retryQueue=null,to(t,n))));break;case 19:Ce(e,t),Oe(t),l&4&&(l=t.updateQueue,l!==null&&(t.updateQueue=null,to(t,l)));break;case 21:break;default:Ce(e,t),Oe(t)}}function Oe(t){var e=t.flags;if(e&2){try{if(t.tag!==27){t:{for(var n=t.return;n!==null;){if(Kd(n)){var l=n;break t}n=n.return}throw Error(u(160))}switch(l.tag){case 27:var r=l.stateNode,o=$r(t);Vs(t,o,r);break;case 5:var y=l.stateNode;l.flags&32&&(wa(y,""),l.flags&=-33);var v=$r(t);Vs(t,v,y);break;case 3:case 4:var T=l.stateNode.containerInfo,D=$r(t);Wr(t,D,T);break;default:throw Error(u(161))}}}catch(B){Tt(t,t.return,B)}t.flags&=-3}e&4096&&(t.flags&=-4097)}function Wd(t){if(t.subtreeFlags&1024)for(t=t.child;t!==null;){var e=t;Wd(e),e.tag===5&&e.flags&1024&&e.stateNode.reset(),t=t.sibling}}function cn(t,e){if(e.subtreeFlags&8772)for(e=e.child;e!==null;)kd(t,e.alternate,e),e=e.sibling}function ka(t){for(t=t.child;t!==null;){var e=t;switch(e.tag){case 0:case 11:case 14:case 15:zn(4,e,e.return),ka(e);break;case 1:de(e,e.return);var n=e.stateNode;typeof n.componentWillUnmount=="function"&&Yd(e,e.return,n),ka(e);break;case 26:case 27:case 5:de(e,e.return),ka(e);break;case 22:de(e,e.return),e.memoizedState===null&&ka(e);break;default:ka(e)}t=t.sibling}}function Un(t,e,n){for(n=n&&(e.subtreeFlags&8772)!==0,e=e.child;e!==null;){var l=e.alternate,r=t,o=e,y=o.flags;switch(o.tag){case 0:case 11:case 15:Un(r,o,n),Wi(4,o);break;case 1:if(Un(r,o,n),l=o,r=l.stateNode,typeof r.componentDidMount=="function")try{r.componentDidMount()}catch(D){Tt(l,l.return,D)}if(l=o,r=l.updateQueue,r!==null){var v=l.stateNode;try{var T=r.shared.hiddenCallbacks;if(T!==null)for(r.shared.hiddenCallbacks=null,r=0;r<T.length;r++)Nd(T[r],v)}catch(D){Tt(l,l.return,D)}}n&&y&64&&qd(o),ha(o,o.return);break;case 26:case 27:case 5:Un(r,o,n),n&&l===null&&y&4&&Gd(o),ha(o,o.return);break;case 12:Un(r,o,n);break;case 13:Un(r,o,n),n&&y&4&&Jd(r,o);break;case 22:o.memoizedState===null&&Un(r,o,n),ha(o,o.return);break;default:Un(r,o,n)}e=e.sibling}}function eo(t,e){var n=null;t!==null&&t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(n=t.memoizedState.cachePool.pool),t=null,e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(t=e.memoizedState.cachePool.pool),t!==n&&(t!=null&&t.refCount++,n!=null&&Gi(n))}function no(t,e){t=null,e.alternate!==null&&(t=e.alternate.memoizedState.cache),e=e.memoizedState.cache,e!==t&&(e.refCount++,t!=null&&Gi(t))}function Bn(t,e,n,l){if(e.subtreeFlags&10256)for(e=e.child;e!==null;)Id(t,e,n,l),e=e.sibling}function Id(t,e,n,l){var r=e.flags;switch(e.tag){case 0:case 11:case 15:Bn(t,e,n,l),r&2048&&Wi(9,e);break;case 3:Bn(t,e,n,l),r&2048&&(t=null,e.alternate!==null&&(t=e.alternate.memoizedState.cache),e=e.memoizedState.cache,e!==t&&(e.refCount++,t!=null&&Gi(t)));break;case 12:if(r&2048){Bn(t,e,n,l),t=e.stateNode;try{var o=e.memoizedProps,y=o.id,v=o.onPostCommit;typeof v=="function"&&v(y,e.alternate===null?"mount":"update",t.passiveEffectDuration,-0)}catch(T){Tt(e,e.return,T)}}else Bn(t,e,n,l);break;case 23:break;case 22:o=e.stateNode,e.memoizedState!==null?o._visibility&4?Bn(t,e,n,l):Ii(t,e):o._visibility&4?Bn(t,e,n,l):(o._visibility|=4,Pa(t,e,n,l,(e.subtreeFlags&10256)!==0)),r&2048&&eo(e.alternate,e);break;case 24:Bn(t,e,n,l),r&2048&&no(e.alternate,e);break;default:Bn(t,e,n,l)}}function Pa(t,e,n,l,r){for(r=r&&(e.subtreeFlags&10256)!==0,e=e.child;e!==null;){var o=t,y=e,v=n,T=l,D=y.flags;switch(y.tag){case 0:case 11:case 15:Pa(o,y,v,T,r),Wi(8,y);break;case 23:break;case 22:var B=y.stateNode;y.memoizedState!==null?B._visibility&4?Pa(o,y,v,T,r):Ii(o,y):(B._visibility|=4,Pa(o,y,v,T,r)),r&&D&2048&&eo(y.alternate,y);break;case 24:Pa(o,y,v,T,r),r&&D&2048&&no(y.alternate,y);break;default:Pa(o,y,v,T,r)}e=e.sibling}}function Ii(t,e){if(e.subtreeFlags&10256)for(e=e.child;e!==null;){var n=t,l=e,r=l.flags;switch(l.tag){case 22:Ii(n,l),r&2048&&eo(l.alternate,l);break;case 24:Ii(n,l),r&2048&&no(l.alternate,l);break;default:Ii(n,l)}e=e.sibling}}var tl=8192;function Fa(t){if(t.subtreeFlags&tl)for(t=t.child;t!==null;)tm(t),t=t.sibling}function tm(t){switch(t.tag){case 26:Fa(t),t.flags&tl&&t.memoizedState!==null&&Z1(Le,t.memoizedState,t.memoizedProps);break;case 5:Fa(t);break;case 3:case 4:var e=Le;Le=ks(t.stateNode.containerInfo),Fa(t),Le=e;break;case 22:t.memoizedState===null&&(e=t.alternate,e!==null&&e.memoizedState!==null?(e=tl,tl=16777216,Fa(t),tl=e):Fa(t));break;default:Fa(t)}}function em(t){var e=t.alternate;if(e!==null&&(t=e.child,t!==null)){e.child=null;do e=t.sibling,t.sibling=null,t=e;while(t!==null)}}function el(t){var e=t.deletions;if((t.flags&16)!==0){if(e!==null)for(var n=0;n<e.length;n++){var l=e[n];Qt=l,am(l,t)}em(t)}if(t.subtreeFlags&10256)for(t=t.child;t!==null;)nm(t),t=t.sibling}function nm(t){switch(t.tag){case 0:case 11:case 15:el(t),t.flags&2048&&zn(9,t,t.return);break;case 3:el(t);break;case 12:el(t);break;case 22:var e=t.stateNode;t.memoizedState!==null&&e._visibility&4&&(t.return===null||t.return.tag!==13)?(e._visibility&=-5,zs(t)):el(t);break;default:el(t)}}function zs(t){var e=t.deletions;if((t.flags&16)!==0){if(e!==null)for(var n=0;n<e.length;n++){var l=e[n];Qt=l,am(l,t)}em(t)}for(t=t.child;t!==null;){switch(e=t,e.tag){case 0:case 11:case 15:zn(8,e,e.return),zs(e);break;case 22:n=e.stateNode,n._visibility&4&&(n._visibility&=-5,zs(e));break;default:zs(e)}t=t.sibling}}function am(t,e){for(;Qt!==null;){var n=Qt;switch(n.tag){case 0:case 11:case 15:zn(8,n,e);break;case 23:case 22:if(n.memoizedState!==null&&n.memoizedState.cachePool!==null){var l=n.memoizedState.cachePool.pool;l!=null&&l.refCount++}break;case 24:Gi(n.memoizedState.cache)}if(l=n.child,l!==null)l.return=n,Qt=l;else t:for(n=t;Qt!==null;){l=Qt;var r=l.sibling,o=l.return;if(Pd(l),l===n){Qt=null;break t}if(r!==null){r.return=o,Qt=r;break t}Qt=o}}}function l1(t,e,n,l){this.tag=t,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=e,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=l,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function we(t,e,n,l){return new l1(t,e,n,l)}function ao(t){return t=t.prototype,!(!t||!t.isReactComponent)}function jn(t,e){var n=t.alternate;return n===null?(n=we(t.tag,e,t.key,t.mode),n.elementType=t.elementType,n.type=t.type,n.stateNode=t.stateNode,n.alternate=t,t.alternate=n):(n.pendingProps=e,n.type=t.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=t.flags&31457280,n.childLanes=t.childLanes,n.lanes=t.lanes,n.child=t.child,n.memoizedProps=t.memoizedProps,n.memoizedState=t.memoizedState,n.updateQueue=t.updateQueue,e=t.dependencies,n.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext},n.sibling=t.sibling,n.index=t.index,n.ref=t.ref,n.refCleanup=t.refCleanup,n}function im(t,e){t.flags&=31457282;var n=t.alternate;return n===null?(t.childLanes=0,t.lanes=e,t.child=null,t.subtreeFlags=0,t.memoizedProps=null,t.memoizedState=null,t.updateQueue=null,t.dependencies=null,t.stateNode=null):(t.childLanes=n.childLanes,t.lanes=n.lanes,t.child=n.child,t.subtreeFlags=0,t.deletions=null,t.memoizedProps=n.memoizedProps,t.memoizedState=n.memoizedState,t.updateQueue=n.updateQueue,t.type=n.type,e=n.dependencies,t.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),t}function Us(t,e,n,l,r,o){var y=0;if(l=t,typeof t=="function")ao(t)&&(y=1);else if(typeof t=="string")y=K1(t,n,Ye.current)?26:t==="html"||t==="head"||t==="body"?27:5;else t:switch(t){case d:return da(n.children,r,o,e);case m:y=8,r|=24;break;case g:return t=we(12,n,e,r|2),t.elementType=g,t.lanes=o,t;case z:return t=we(13,n,e,r),t.elementType=z,t.lanes=o,t;case V:return t=we(19,n,e,r),t.elementType=V,t.lanes=o,t;case j:return lm(n,r,o,e);default:if(typeof t=="object"&&t!==null)switch(t.$$typeof){case S:case x:y=10;break t;case b:y=9;break t;case M:y=11;break t;case O:y=14;break t;case L:y=16,l=null;break t}y=29,n=Error(u(130,t===null?"null":typeof t,"")),l=null}return e=we(y,n,e,r),e.elementType=t,e.type=l,e.lanes=o,e}function da(t,e,n,l){return t=we(7,t,l,e),t.lanes=n,t}function lm(t,e,n,l){t=we(22,t,l,e),t.elementType=j,t.lanes=n;var r={_visibility:1,_pendingVisibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null,_current:null,detach:function(){var o=r._current;if(o===null)throw Error(u(456));if((r._pendingVisibility&2)===0){var y=An(o,2);y!==null&&(r._pendingVisibility|=2,le(y,o,2))}},attach:function(){var o=r._current;if(o===null)throw Error(u(456));if((r._pendingVisibility&2)!==0){var y=An(o,2);y!==null&&(r._pendingVisibility&=-3,le(y,o,2))}}};return t.stateNode=r,t}function io(t,e,n){return t=we(6,t,null,e),t.lanes=n,t}function lo(t,e,n){return e=we(4,t.children!==null?t.children:[],t.key,e),e.lanes=n,e.stateNode={containerInfo:t.containerInfo,pendingChildren:null,implementation:t.implementation},e}function fn(t){t.flags|=4}function sm(t,e){if(e.type!=="stylesheet"||(e.state.loading&4)!==0)t.flags&=-16777217;else if(t.flags|=16777216,!Km(e)){if(e=Me.current,e!==null&&((mt&4194176)===mt?Ke!==null:(mt&62914560)!==mt&&(mt&536870912)===0||e!==Ke))throw Hi=cr,Eh;t.flags|=8192}}function Bs(t,e){e!==null&&(t.flags|=4),t.flags&16384&&(e=t.tag!==22?Af():536870912,t.lanes|=e,$a|=e)}function nl(t,e){if(!pt)switch(t.tailMode){case"hidden":e=t.tail;for(var n=null;e!==null;)e.alternate!==null&&(n=e),e=e.sibling;n===null?t.tail=null:n.sibling=null;break;case"collapsed":n=t.tail;for(var l=null;n!==null;)n.alternate!==null&&(l=n),n=n.sibling;l===null?e||t.tail===null?t.tail=null:t.tail.sibling=null:l.sibling=null}}function Dt(t){var e=t.alternate!==null&&t.alternate.child===t.child,n=0,l=0;if(e)for(var r=t.child;r!==null;)n|=r.lanes|r.childLanes,l|=r.subtreeFlags&31457280,l|=r.flags&31457280,r.return=t,r=r.sibling;else for(r=t.child;r!==null;)n|=r.lanes|r.childLanes,l|=r.subtreeFlags,l|=r.flags,r.return=t,r=r.sibling;return t.subtreeFlags|=l,t.childLanes=n,e}function s1(t,e,n){var l=e.pendingProps;switch(rr(e),e.tag){case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Dt(e),null;case 1:return Dt(e),null;case 3:return n=e.stateNode,l=null,t!==null&&(l=t.memoizedState.cache),e.memoizedState.cache!==l&&(e.flags|=2048),un(Gt),Aa(),n.pendingContext&&(n.context=n.pendingContext,n.pendingContext=null),(t===null||t.child===null)&&(Bi(e)?fn(e):t===null||t.memoizedState.isDehydrated&&(e.flags&256)===0||(e.flags|=1024,je!==null&&(mo(je),je=null))),Dt(e),null;case 26:return n=e.memoizedState,t===null?(fn(e),n!==null?(Dt(e),sm(e,n)):(Dt(e),e.flags&=-16777217)):n?n!==t.memoizedState?(fn(e),Dt(e),sm(e,n)):(Dt(e),e.flags&=-16777217):(t.memoizedProps!==l&&fn(e),Dt(e),e.flags&=-16777217),null;case 27:kl(e),n=bn.current;var r=e.type;if(t!==null&&e.stateNode!=null)t.memoizedProps!==l&&fn(e);else{if(!l){if(e.stateNode===null)throw Error(u(166));return Dt(e),null}t=Ye.current,Bi(e)?Th(e):(t=jm(r,l,n),e.stateNode=t,fn(e))}return Dt(e),null;case 5:if(kl(e),n=e.type,t!==null&&e.stateNode!=null)t.memoizedProps!==l&&fn(e);else{if(!l){if(e.stateNode===null)throw Error(u(166));return Dt(e),null}if(t=Ye.current,Bi(e))Th(e);else{switch(r=Zs(bn.current),t){case 1:t=r.createElementNS("http://www.w3.org/2000/svg",n);break;case 2:t=r.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;default:switch(n){case"svg":t=r.createElementNS("http://www.w3.org/2000/svg",n);break;case"math":t=r.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;case"script":t=r.createElement("div"),t.innerHTML="<script><\/script>",t=t.removeChild(t.firstChild);break;case"select":t=typeof l.is=="string"?r.createElement("select",{is:l.is}):r.createElement("select"),l.multiple?t.multiple=!0:l.size&&(t.size=l.size);break;default:t=typeof l.is=="string"?r.createElement(n,{is:l.is}):r.createElement(n)}}t[$t]=e,t[ue]=l;t:for(r=e.child;r!==null;){if(r.tag===5||r.tag===6)t.appendChild(r.stateNode);else if(r.tag!==4&&r.tag!==27&&r.child!==null){r.child.return=r,r=r.child;continue}if(r===e)break t;for(;r.sibling===null;){if(r.return===null||r.return===e)break t;r=r.return}r.sibling.return=r.return,r=r.sibling}e.stateNode=t;t:switch(Ft(t,n,l),n){case"button":case"input":case"select":case"textarea":t=!!l.autoFocus;break t;case"img":t=!0;break t;default:t=!1}t&&fn(e)}}return Dt(e),e.flags&=-16777217,null;case 6:if(t&&e.stateNode!=null)t.memoizedProps!==l&&fn(e);else{if(typeof l!="string"&&e.stateNode===null)throw Error(u(166));if(t=bn.current,Bi(e)){if(t=e.stateNode,n=e.memoizedProps,l=null,r=ie,r!==null)switch(r.tag){case 27:case 5:l=r.memoizedProps}t[$t]=e,t=!!(t.nodeValue===n||l!==null&&l.suppressHydrationWarning===!0||wm(t.nodeValue,n)),t||aa(e)}else t=Zs(t).createTextNode(l),t[$t]=e,e.stateNode=t}return Dt(e),null;case 13:if(l=e.memoizedState,t===null||t.memoizedState!==null&&t.memoizedState.dehydrated!==null){if(r=Bi(e),l!==null&&l.dehydrated!==null){if(t===null){if(!r)throw Error(u(318));if(r=e.memoizedState,r=r!==null?r.dehydrated:null,!r)throw Error(u(317));r[$t]=e}else ji(),(e.flags&128)===0&&(e.memoizedState=null),e.flags|=4;Dt(e),r=!1}else je!==null&&(mo(je),je=null),r=!0;if(!r)return e.flags&256?(nn(e),e):(nn(e),null)}if(nn(e),(e.flags&128)!==0)return e.lanes=n,e;if(n=l!==null,t=t!==null&&t.memoizedState!==null,n){l=e.child,r=null,l.alternate!==null&&l.alternate.memoizedState!==null&&l.alternate.memoizedState.cachePool!==null&&(r=l.alternate.memoizedState.cachePool.pool);var o=null;l.memoizedState!==null&&l.memoizedState.cachePool!==null&&(o=l.memoizedState.cachePool.pool),o!==r&&(l.flags|=2048)}return n!==t&&n&&(e.child.flags|=8192),Bs(e,e.updateQueue),Dt(e),null;case 4:return Aa(),t===null&&Ro(e.stateNode.containerInfo),Dt(e),null;case 10:return un(e.type),Dt(e),null;case 19:if(Lt(Yt),r=e.memoizedState,r===null)return Dt(e),null;if(l=(e.flags&128)!==0,o=r.rendering,o===null)if(l)nl(r,!1);else{if(Vt!==0||t!==null&&(t.flags&128)!==0)for(t=e.child;t!==null;){if(o=vs(t),o!==null){for(e.flags|=128,nl(r,!1),t=o.updateQueue,e.updateQueue=t,Bs(e,t),e.subtreeFlags=0,t=n,n=e.child;n!==null;)im(n,t),n=n.sibling;return Rt(Yt,Yt.current&1|2),e.child}t=t.sibling}r.tail!==null&&Ge()>js&&(e.flags|=128,l=!0,nl(r,!1),e.lanes=4194304)}else{if(!l)if(t=vs(o),t!==null){if(e.flags|=128,l=!0,t=t.updateQueue,e.updateQueue=t,Bs(e,t),nl(r,!0),r.tail===null&&r.tailMode==="hidden"&&!o.alternate&&!pt)return Dt(e),null}else 2*Ge()-r.renderingStartTime>js&&n!==536870912&&(e.flags|=128,l=!0,nl(r,!1),e.lanes=4194304);r.isBackwards?(o.sibling=e.child,e.child=o):(t=r.last,t!==null?t.sibling=o:e.child=o,r.last=o)}return r.tail!==null?(e=r.tail,r.rendering=e,r.tail=e.sibling,r.renderingStartTime=Ge(),e.sibling=null,t=Yt.current,Rt(Yt,l?t&1|2:t&1),e):(Dt(e),null);case 22:case 23:return nn(e),hr(),l=e.memoizedState!==null,t!==null?t.memoizedState!==null!==l&&(e.flags|=8192):l&&(e.flags|=8192),l?(n&536870912)!==0&&(e.flags&128)===0&&(Dt(e),e.subtreeFlags&6&&(e.flags|=8192)):Dt(e),n=e.updateQueue,n!==null&&Bs(e,n.retryQueue),n=null,t!==null&&t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(n=t.memoizedState.cachePool.pool),l=null,e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(l=e.memoizedState.cachePool.pool),l!==n&&(e.flags|=2048),t!==null&&Lt(la),null;case 24:return n=null,t!==null&&(n=t.memoizedState.cache),e.memoizedState.cache!==n&&(e.flags|=2048),un(Gt),Dt(e),null;case 25:return null}throw Error(u(156,e.tag))}function u1(t,e){switch(rr(e),e.tag){case 1:return t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 3:return un(Gt),Aa(),t=e.flags,(t&65536)!==0&&(t&128)===0?(e.flags=t&-65537|128,e):null;case 26:case 27:case 5:return kl(e),null;case 13:if(nn(e),t=e.memoizedState,t!==null&&t.dehydrated!==null){if(e.alternate===null)throw Error(u(340));ji()}return t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 19:return Lt(Yt),null;case 4:return Aa(),null;case 10:return un(e.type),null;case 22:case 23:return nn(e),hr(),t!==null&&Lt(la),t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 24:return un(Gt),null;case 25:return null;default:return null}}function um(t,e){switch(rr(e),e.tag){case 3:un(Gt),Aa();break;case 26:case 27:case 5:kl(e);break;case 4:Aa();break;case 13:nn(e);break;case 19:Lt(Yt);break;case 10:un(e.type);break;case 22:case 23:nn(e),hr(),t!==null&&Lt(la);break;case 24:un(Gt)}}var r1={getCacheForType:function(t){var e=Wt(Gt),n=e.data.get(t);return n===void 0&&(n=t(),e.data.set(t,n)),n}},o1=typeof WeakMap=="function"?WeakMap:Map,Mt=0,xt=null,ft=null,mt=0,Et=0,pe=null,hn=!1,Ja=!1,so=!1,dn=0,Vt=0,Ln=0,ma=0,uo=0,_e=0,$a=0,al=null,Ze=null,ro=!1,oo=0,js=1/0,Ls=null,Nn=null,Ns=!1,pa=null,il=0,co=0,fo=null,ll=0,ho=null;function ye(){if((Mt&2)!==0&&mt!==0)return mt&-mt;if(X.T!==null){var t=Ga;return t!==0?t:To()}return Cf()}function rm(){_e===0&&(_e=(mt&536870912)===0||pt?Ef():536870912);var t=Me.current;return t!==null&&(t.flags|=32),_e}function le(t,e,n){(t===xt&&Et===2||t.cancelPendingCommit!==null)&&(Wa(t,0),mn(t,mt,_e,!1)),Ei(t,n),((Mt&2)===0||t!==xt)&&(t===xt&&((Mt&2)===0&&(ma|=n),Vt===4&&mn(t,mt,_e,!1)),ke(t))}function om(t,e,n){if((Mt&6)!==0)throw Error(u(327));var l=!n&&(e&60)===0&&(e&t.expiredLanes)===0||xi(t,e),r=l?h1(t,e):go(t,e,!0),o=l;do{if(r===0){Ja&&!l&&mn(t,e,0,!1);break}else if(r===6)mn(t,e,0,!hn);else{if(n=t.current.alternate,o&&!c1(n)){r=go(t,e,!1),o=!1;continue}if(r===2){if(o=e,t.errorRecoveryDisabledLanes&o)var y=0;else y=t.pendingLanes&-536870913,y=y!==0?y:y&536870912?536870912:0;if(y!==0){e=y;t:{var v=t;r=al;var T=v.current.memoizedState.isDehydrated;if(T&&(Wa(v,y).flags|=256),y=go(v,y,!1),y!==2){if(so&&!T){v.errorRecoveryDisabledLanes|=o,ma|=o,r=4;break t}o=Ze,Ze=r,o!==null&&mo(o)}r=y}if(o=!1,r!==2)continue}}if(r===1){Wa(t,0),mn(t,e,0,!0);break}t:{switch(l=t,r){case 0:case 1:throw Error(u(345));case 4:if((e&4194176)===e){mn(l,e,_e,!hn);break t}break;case 2:Ze=null;break;case 3:case 5:break;default:throw Error(u(329))}if(l.finishedWork=n,l.finishedLanes=e,(e&62914560)===e&&(o=oo+300-Ge(),10<o)){if(mn(l,e,_e,!hn),$l(l,0)!==0)break t;l.timeoutHandle=zm(cm.bind(null,l,n,Ze,Ls,ro,e,_e,ma,$a,hn,2,-0,0),o);break t}cm(l,n,Ze,Ls,ro,e,_e,ma,$a,hn,0,-0,0)}}break}while(!0);ke(t)}function mo(t){Ze===null?Ze=t:Ze.push.apply(Ze,t)}function cm(t,e,n,l,r,o,y,v,T,D,B,q,_){var U=e.subtreeFlags;if((U&8192||(U&16785408)===16785408)&&(fl={stylesheets:null,count:0,unsuspend:Q1},tm(e),e=k1(),e!==null)){t.cancelPendingCommit=e(gm.bind(null,t,n,l,r,y,v,T,1,q,_)),mn(t,o,y,!D);return}gm(t,n,l,r,y,v,T,B,q,_)}function c1(t){for(var e=t;;){var n=e.tag;if((n===0||n===11||n===15)&&e.flags&16384&&(n=e.updateQueue,n!==null&&(n=n.stores,n!==null)))for(var l=0;l<n.length;l++){var r=n[l],o=r.getSnapshot;r=r.value;try{if(!he(o(),r))return!1}catch{return!1}}if(n=e.child,e.subtreeFlags&16384&&n!==null)n.return=e,e=n;else{if(e===t)break;for(;e.sibling===null;){if(e.return===null||e.return===t)return!0;e=e.return}e.sibling.return=e.return,e=e.sibling}}return!0}function mn(t,e,n,l){e&=~uo,e&=~ma,t.suspendedLanes|=e,t.pingedLanes&=~e,l&&(t.warmLanes|=e),l=t.expirationTimes;for(var r=e;0<r;){var o=31-fe(r),y=1<<o;l[o]=-1,r&=~y}n!==0&&Rf(t,n,e)}function Hs(){return(Mt&6)===0?(sl(0),!1):!0}function po(){if(ft!==null){if(Et===0)var t=ft.return;else t=ft,sn=ca=null,br(t),qa=null,qi=0,t=ft;for(;t!==null;)um(t.alternate,t),t=t.return;ft=null}}function Wa(t,e){t.finishedWork=null,t.finishedLanes=0;var n=t.timeoutHandle;n!==-1&&(t.timeoutHandle=-1,O1(n)),n=t.cancelPendingCommit,n!==null&&(t.cancelPendingCommit=null,n()),po(),xt=t,ft=n=jn(t.current,null),mt=e,Et=0,pe=null,hn=!1,Ja=xi(t,e),so=!1,$a=_e=uo=ma=Ln=Vt=0,Ze=al=null,ro=!1,(e&8)!==0&&(e|=e&32);var l=t.entangledLanes;if(l!==0)for(t=t.entanglements,l&=e;0<l;){var r=31-fe(l),o=1<<r;e|=t[r],l&=~o}return dn=e,os(),n}function fm(t,e){ot=null,X.H=Qe,e===Ni?(e=Dh(),Et=3):e===Eh?(e=Dh(),Et=4):Et=e===Ad?8:e!==null&&typeof e=="object"&&typeof e.then=="function"?6:1,pe=e,ft===null&&(Vt=1,Os(t,Ae(e,t.current)))}function hm(){var t=X.H;return X.H=Qe,t===null?Qe:t}function dm(){var t=X.A;return X.A=r1,t}function yo(){Vt=4,hn||(mt&4194176)!==mt&&Me.current!==null||(Ja=!0),(Ln&134217727)===0&&(ma&134217727)===0||xt===null||mn(xt,mt,_e,!1)}function go(t,e,n){var l=Mt;Mt|=2;var r=hm(),o=dm();(xt!==t||mt!==e)&&(Ls=null,Wa(t,e)),e=!1;var y=Vt;t:do try{if(Et!==0&&ft!==null){var v=ft,T=pe;switch(Et){case 8:po(),y=6;break t;case 3:case 2:case 6:Me.current===null&&(e=!0);var D=Et;if(Et=0,pe=null,Ia(t,v,T,D),n&&Ja){y=0;break t}break;default:D=Et,Et=0,pe=null,Ia(t,v,T,D)}}f1(),y=Vt;break}catch(B){fm(t,B)}while(!0);return e&&t.shellSuspendCounter++,sn=ca=null,Mt=l,X.H=r,X.A=o,ft===null&&(xt=null,mt=0,os()),y}function f1(){for(;ft!==null;)mm(ft)}function h1(t,e){var n=Mt;Mt|=2;var l=hm(),r=dm();xt!==t||mt!==e?(Ls=null,js=Ge()+500,Wa(t,e)):Ja=xi(t,e);t:do try{if(Et!==0&&ft!==null){e=ft;var o=pe;e:switch(Et){case 1:Et=0,pe=null,Ia(t,e,o,1);break;case 2:if(Ah(o)){Et=0,pe=null,pm(e);break}e=function(){Et===2&&xt===t&&(Et=7),ke(t)},o.then(e,e);break t;case 3:Et=7;break t;case 4:Et=5;break t;case 7:Ah(o)?(Et=0,pe=null,pm(e)):(Et=0,pe=null,Ia(t,e,o,7));break;case 5:var y=null;switch(ft.tag){case 26:y=ft.memoizedState;case 5:case 27:var v=ft;if(!y||Km(y)){Et=0,pe=null;var T=v.sibling;if(T!==null)ft=T;else{var D=v.return;D!==null?(ft=D,qs(D)):ft=null}break e}}Et=0,pe=null,Ia(t,e,o,5);break;case 6:Et=0,pe=null,Ia(t,e,o,6);break;case 8:po(),Vt=6;break t;default:throw Error(u(462))}}d1();break}catch(B){fm(t,B)}while(!0);return sn=ca=null,X.H=l,X.A=r,Mt=n,ft!==null?0:(xt=null,mt=0,os(),Vt)}function d1(){for(;ft!==null&&!Bv();)mm(ft)}function mm(t){var e=jd(t.alternate,t,dn);t.memoizedProps=t.pendingProps,e===null?qs(t):ft=e}function pm(t){var e=t,n=e.alternate;switch(e.tag){case 15:case 0:e=wd(n,e,e.pendingProps,e.type,void 0,mt);break;case 11:e=wd(n,e,e.pendingProps,e.type.render,e.ref,mt);break;case 5:br(e);default:um(n,e),e=ft=im(e,dn),e=jd(n,e,dn)}t.memoizedProps=t.pendingProps,e===null?qs(t):ft=e}function Ia(t,e,n,l){sn=ca=null,br(e),qa=null,qi=0;var r=e.return;try{if(e1(t,r,e,n,mt)){Vt=1,Os(t,Ae(n,t.current)),ft=null;return}}catch(o){if(r!==null)throw ft=r,o;Vt=1,Os(t,Ae(n,t.current)),ft=null;return}e.flags&32768?(pt||l===1?t=!0:Ja||(mt&536870912)!==0?t=!1:(hn=t=!0,(l===2||l===3||l===6)&&(l=Me.current,l!==null&&l.tag===13&&(l.flags|=16384))),ym(e,t)):qs(e)}function qs(t){var e=t;do{if((e.flags&32768)!==0){ym(e,hn);return}t=e.return;var n=s1(e.alternate,e,dn);if(n!==null){ft=n;return}if(e=e.sibling,e!==null){ft=e;return}ft=e=t}while(e!==null);Vt===0&&(Vt=5)}function ym(t,e){do{var n=u1(t.alternate,t);if(n!==null){n.flags&=32767,ft=n;return}if(n=t.return,n!==null&&(n.flags|=32768,n.subtreeFlags=0,n.deletions=null),!e&&(t=t.sibling,t!==null)){ft=t;return}ft=t=n}while(t!==null);Vt=6,ft=null}function gm(t,e,n,l,r,o,y,v,T,D){var B=X.T,q=Q.p;try{Q.p=2,X.T=null,m1(t,e,n,l,q,r,o,y,v,T,D)}finally{X.T=B,Q.p=q}}function m1(t,e,n,l,r,o,y,v){do ti();while(pa!==null);if((Mt&6)!==0)throw Error(u(327));var T=t.finishedWork;if(l=t.finishedLanes,T===null)return null;if(t.finishedWork=null,t.finishedLanes=0,T===t.current)throw Error(u(177));t.callbackNode=null,t.callbackPriority=0,t.cancelPendingCommit=null;var D=T.lanes|T.childLanes;if(D|=lr,Zv(t,l,D,o,y,v),t===xt&&(ft=xt=null,mt=0),(T.subtreeFlags&10256)===0&&(T.flags&10256)===0||Ns||(Ns=!0,co=D,fo=n,v1(Pl,function(){return ti(),null})),n=(T.flags&15990)!==0,(T.subtreeFlags&15990)!==0||n?(n=X.T,X.T=null,o=Q.p,Q.p=2,y=Mt,Mt|=4,a1(t,T),$d(T,t),N0(Oo,t.containerInfo),Ws=!!Co,Oo=Co=null,t.current=T,kd(t,T.alternate,T),jv(),Mt=y,Q.p=o,X.T=n):t.current=T,Ns?(Ns=!1,pa=t,il=l):vm(t,D),D=t.pendingLanes,D===0&&(Nn=null),Yv(T.stateNode),ke(t),e!==null)for(r=t.onRecoverableError,T=0;T<e.length;T++)D=e[T],r(D.value,{componentStack:D.stack});return(il&3)!==0&&ti(),D=t.pendingLanes,(l&4194218)!==0&&(D&42)!==0?t===ho?ll++:(ll=0,ho=t):ll=0,sl(0),null}function vm(t,e){(t.pooledCacheLanes&=e)===0&&(e=t.pooledCache,e!=null&&(t.pooledCache=null,Gi(e)))}function ti(){if(pa!==null){var t=pa,e=co;co=0;var n=Mf(il),l=X.T,r=Q.p;try{if(Q.p=32>n?32:n,X.T=null,pa===null)var o=!1;else{n=fo,fo=null;var y=pa,v=il;if(pa=null,il=0,(Mt&6)!==0)throw Error(u(331));var T=Mt;if(Mt|=4,nm(y.current),Id(y,y.current,v,n),Mt=T,sl(0,!1),ce&&typeof ce.onPostCommitFiberRoot=="function")try{ce.onPostCommitFiberRoot(Ti,y)}catch{}o=!0}return o}finally{Q.p=r,X.T=l,vm(t,e)}}return!1}function Sm(t,e,n){e=Ae(n,e),e=Ur(t.stateNode,e,2),t=Vn(t,e,2),t!==null&&(Ei(t,2),ke(t))}function Tt(t,e,n){if(t.tag===3)Sm(t,t,n);else for(;e!==null;){if(e.tag===3){Sm(e,t,n);break}else if(e.tag===1){var l=e.stateNode;if(typeof e.type.getDerivedStateFromError=="function"||typeof l.componentDidCatch=="function"&&(Nn===null||!Nn.has(l))){t=Ae(n,t),n=xd(2),l=Vn(e,n,2),l!==null&&(Ed(n,l,e,t),Ei(l,2),ke(l));break}}e=e.return}}function vo(t,e,n){var l=t.pingCache;if(l===null){l=t.pingCache=new o1;var r=new Set;l.set(e,r)}else r=l.get(e),r===void 0&&(r=new Set,l.set(e,r));r.has(n)||(so=!0,r.add(n),t=p1.bind(null,t,e,n),e.then(t,t))}function p1(t,e,n){var l=t.pingCache;l!==null&&l.delete(e),t.pingedLanes|=t.suspendedLanes&n,t.warmLanes&=~n,xt===t&&(mt&n)===n&&(Vt===4||Vt===3&&(mt&62914560)===mt&&300>Ge()-oo?(Mt&2)===0&&Wa(t,0):uo|=n,$a===mt&&($a=0)),ke(t)}function bm(t,e){e===0&&(e=Af()),t=An(t,e),t!==null&&(Ei(t,e),ke(t))}function y1(t){var e=t.memoizedState,n=0;e!==null&&(n=e.retryLane),bm(t,n)}function g1(t,e){var n=0;switch(t.tag){case 13:var l=t.stateNode,r=t.memoizedState;r!==null&&(n=r.retryLane);break;case 19:l=t.stateNode;break;case 22:l=t.stateNode._retryCache;break;default:throw Error(u(314))}l!==null&&l.delete(e),bm(t,n)}function v1(t,e){return Bu(t,e)}var Ys=null,ei=null,So=!1,Gs=!1,bo=!1,ya=0;function ke(t){t!==ei&&t.next===null&&(ei===null?Ys=ei=t:ei=ei.next=t),Gs=!0,So||(So=!0,b1(S1))}function sl(t,e){if(!bo&&Gs){bo=!0;do for(var n=!1,l=Ys;l!==null;){if(t!==0){var r=l.pendingLanes;if(r===0)var o=0;else{var y=l.suspendedLanes,v=l.pingedLanes;o=(1<<31-fe(42|t)+1)-1,o&=r&~(y&~v),o=o&201326677?o&201326677|1:o?o|2:0}o!==0&&(n=!0,Em(l,o))}else o=mt,o=$l(l,l===xt?o:0),(o&3)===0||xi(l,o)||(n=!0,Em(l,o));l=l.next}while(n);bo=!1}}function S1(){Gs=So=!1;var t=0;ya!==0&&(C1()&&(t=ya),ya=0);for(var e=Ge(),n=null,l=Ys;l!==null;){var r=l.next,o=Tm(l,e);o===0?(l.next=null,n===null?Ys=r:n.next=r,r===null&&(ei=n)):(n=l,(t!==0||(o&3)!==0)&&(Gs=!0)),l=r}sl(t)}function Tm(t,e){for(var n=t.suspendedLanes,l=t.pingedLanes,r=t.expirationTimes,o=t.pendingLanes&-62914561;0<o;){var y=31-fe(o),v=1<<y,T=r[y];T===-1?((v&n)===0||(v&l)!==0)&&(r[y]=Qv(v,e)):T<=e&&(t.expiredLanes|=v),o&=~v}if(e=xt,n=mt,n=$l(t,t===e?n:0),l=t.callbackNode,n===0||t===e&&Et===2||t.cancelPendingCommit!==null)return l!==null&&l!==null&&ju(l),t.callbackNode=null,t.callbackPriority=0;if((n&3)===0||xi(t,n)){if(e=n&-n,e===t.callbackPriority)return e;switch(l!==null&&ju(l),Mf(n)){case 2:case 8:n=Tf;break;case 32:n=Pl;break;case 268435456:n=xf;break;default:n=Pl}return l=xm.bind(null,t),n=Bu(n,l),t.callbackPriority=e,t.callbackNode=n,e}return l!==null&&l!==null&&ju(l),t.callbackPriority=2,t.callbackNode=null,2}function xm(t,e){var n=t.callbackNode;if(ti()&&t.callbackNode!==n)return null;var l=mt;return l=$l(t,t===xt?l:0),l===0?null:(om(t,l,e),Tm(t,Ge()),t.callbackNode!=null&&t.callbackNode===n?xm.bind(null,t):null)}function Em(t,e){if(ti())return null;om(t,e,!0)}function b1(t){w1(function(){(Mt&6)!==0?Bu(bf,t):t()})}function To(){return ya===0&&(ya=Ef()),ya}function Am(t){return t==null||typeof t=="symbol"||typeof t=="boolean"?null:typeof t=="function"?t:ns(""+t)}function Rm(t,e){var n=e.ownerDocument.createElement("input");return n.name=e.name,n.value=e.value,t.id&&n.setAttribute("form",t.id),e.parentNode.insertBefore(n,e),t=new FormData(t),n.parentNode.removeChild(n),t}function T1(t,e,n,l,r){if(e==="submit"&&n&&n.stateNode===r){var o=Am((r[ue]||null).action),y=l.submitter;y&&(e=(e=y[ue]||null)?Am(e.formAction):y.getAttribute("formAction"),e!==null&&(o=e,y=null));var v=new ss("action","action",null,l,r);t.push({event:v,listeners:[{instance:null,listener:function(){if(l.defaultPrevented){if(ya!==0){var T=y?Rm(r,y):new FormData(r);Or(n,{pending:!0,data:T,method:r.method,action:o},null,T)}}else typeof o=="function"&&(v.preventDefault(),T=y?Rm(r,y):new FormData(r),Or(n,{pending:!0,data:T,method:r.method,action:o},o,T))},currentTarget:r}]})}}for(var xo=0;xo<gh.length;xo++){var Eo=gh[xo],x1=Eo.toLowerCase(),E1=Eo[0].toUpperCase()+Eo.slice(1);Be(x1,"on"+E1)}Be(hh,"onAnimationEnd"),Be(dh,"onAnimationIteration"),Be(mh,"onAnimationStart"),Be("dblclick","onDoubleClick"),Be("focusin","onFocus"),Be("focusout","onBlur"),Be(q0,"onTransitionRun"),Be(Y0,"onTransitionStart"),Be(G0,"onTransitionCancel"),Be(ph,"onTransitionEnd"),Ca("onMouseEnter",["mouseout","mouseover"]),Ca("onMouseLeave",["mouseout","mouseover"]),Ca("onPointerEnter",["pointerout","pointerover"]),Ca("onPointerLeave",["pointerout","pointerover"]),$n("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),$n("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),$n("onBeforeInput",["compositionend","keypress","textInput","paste"]),$n("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),$n("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),$n("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var ul="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),A1=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(ul));function Dm(t,e){e=(e&4)!==0;for(var n=0;n<t.length;n++){var l=t[n],r=l.event;l=l.listeners;t:{var o=void 0;if(e)for(var y=l.length-1;0<=y;y--){var v=l[y],T=v.instance,D=v.currentTarget;if(v=v.listener,T!==o&&r.isPropagationStopped())break t;o=v,r.currentTarget=D;try{o(r)}catch(B){Cs(B)}r.currentTarget=null,o=T}else for(y=0;y<l.length;y++){if(v=l[y],T=v.instance,D=v.currentTarget,v=v.listener,T!==o&&r.isPropagationStopped())break t;o=v,r.currentTarget=D;try{o(r)}catch(B){Cs(B)}r.currentTarget=null,o=T}}}}function ht(t,e){var n=e[Nu];n===void 0&&(n=e[Nu]=new Set);var l=t+"__bubble";n.has(l)||(Mm(e,t,2,!1),n.add(l))}function Ao(t,e,n){var l=0;e&&(l|=4),Mm(n,t,l,e)}var Xs="_reactListening"+Math.random().toString(36).slice(2);function Ro(t){if(!t[Xs]){t[Xs]=!0,wf.forEach(function(n){n!=="selectionchange"&&(A1.has(n)||Ao(n,!1,t),Ao(n,!0,t))});var e=t.nodeType===9?t:t.ownerDocument;e===null||e[Xs]||(e[Xs]=!0,Ao("selectionchange",!1,e))}}function Mm(t,e,n,l){switch(Jm(e)){case 2:var r=J1;break;case 8:r=$1;break;default:r=No}n=r.bind(null,e,n,t),r=void 0,!Zu||e!=="touchstart"&&e!=="touchmove"&&e!=="wheel"||(r=!0),l?r!==void 0?t.addEventListener(e,n,{capture:!0,passive:r}):t.addEventListener(e,n,!0):r!==void 0?t.addEventListener(e,n,{passive:r}):t.addEventListener(e,n,!1)}function Do(t,e,n,l,r){var o=l;if((e&1)===0&&(e&2)===0&&l!==null)t:for(;;){if(l===null)return;var y=l.tag;if(y===3||y===4){var v=l.stateNode.containerInfo;if(v===r||v.nodeType===8&&v.parentNode===r)break;if(y===4)for(y=l.return;y!==null;){var T=y.tag;if((T===3||T===4)&&(T=y.stateNode.containerInfo,T===r||T.nodeType===8&&T.parentNode===r))return;y=y.return}for(;v!==null;){if(y=Jn(v),y===null)return;if(T=y.tag,T===5||T===6||T===26||T===27){l=o=y;continue t}v=v.parentNode}}l=l.return}Gf(function(){var D=o,B=Ku(n),q=[];t:{var _=yh.get(t);if(_!==void 0){var U=ss,F=t;switch(t){case"keypress":if(is(n)===0)break t;case"keydown":case"keyup":U=g0;break;case"focusin":F="focus",U=Ju;break;case"focusout":F="blur",U=Ju;break;case"beforeblur":case"afterblur":U=Ju;break;case"click":if(n.button===2)break t;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":U=Qf;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":U=l0;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":U=b0;break;case hh:case dh:case mh:U=r0;break;case ph:U=x0;break;case"scroll":case"scrollend":U=a0;break;case"wheel":U=A0;break;case"copy":case"cut":case"paste":U=c0;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":U=kf;break;case"toggle":case"beforetoggle":U=D0}var lt=(e&4)!==0,zt=!lt&&(t==="scroll"||t==="scrollend"),C=lt?_!==null?_+"Capture":null:_;lt=[];for(var A=D,w;A!==null;){var N=A;if(w=N.stateNode,N=N.tag,N!==5&&N!==26&&N!==27||w===null||C===null||(N=Di(A,C),N!=null&&lt.push(rl(A,N,w))),zt)break;A=A.return}0<lt.length&&(_=new U(_,F,null,n,B),q.push({event:_,listeners:lt}))}}if((e&7)===0){t:{if(_=t==="mouseover"||t==="pointerover",U=t==="mouseout"||t==="pointerout",_&&n!==Xu&&(F=n.relatedTarget||n.fromElement)&&(Jn(F)||F[Ra]))break t;if((U||_)&&(_=B.window===B?B:(_=B.ownerDocument)?_.defaultView||_.parentWindow:window,U?(F=n.relatedTarget||n.toElement,U=D,F=F?Jn(F):null,F!==null&&(zt=W(F),lt=F.tag,F!==zt||lt!==5&&lt!==27&&lt!==6)&&(F=null)):(U=null,F=D),U!==F)){if(lt=Qf,N="onMouseLeave",C="onMouseEnter",A="mouse",(t==="pointerout"||t==="pointerover")&&(lt=kf,N="onPointerLeave",C="onPointerEnter",A="pointer"),zt=U==null?_:Ri(U),w=F==null?_:Ri(F),_=new lt(N,A+"leave",U,n,B),_.target=zt,_.relatedTarget=w,N=null,Jn(B)===D&&(lt=new lt(C,A+"enter",F,n,B),lt.target=w,lt.relatedTarget=zt,N=lt),zt=N,U&&F)e:{for(lt=U,C=F,A=0,w=lt;w;w=ni(w))A++;for(w=0,N=C;N;N=ni(N))w++;for(;0<A-w;)lt=ni(lt),A--;for(;0<w-A;)C=ni(C),w--;for(;A--;){if(lt===C||C!==null&&lt===C.alternate)break e;lt=ni(lt),C=ni(C)}lt=null}else lt=null;U!==null&&Cm(q,_,U,lt,!1),F!==null&&zt!==null&&Cm(q,zt,F,lt,!0)}}t:{if(_=D?Ri(D):window,U=_.nodeName&&_.nodeName.toLowerCase(),U==="select"||U==="input"&&_.type==="file")var Z=eh;else if(If(_))if(nh)Z=j0;else{Z=U0;var ct=z0}else U=_.nodeName,!U||U.toLowerCase()!=="input"||_.type!=="checkbox"&&_.type!=="radio"?D&&Gu(D.elementType)&&(Z=eh):Z=B0;if(Z&&(Z=Z(t,D))){th(q,Z,n,B);break t}ct&&ct(t,_,D),t==="focusout"&&D&&_.type==="number"&&D.memoizedProps.value!=null&&Yu(_,"number",_.value)}switch(ct=D?Ri(D):window,t){case"focusin":(If(ct)||ct.contentEditable==="true")&&(Ua=ct,nr=D,Ui=null);break;case"focusout":Ui=nr=Ua=null;break;case"mousedown":ar=!0;break;case"contextmenu":case"mouseup":case"dragend":ar=!1,ch(q,n,B);break;case"selectionchange":if(H0)break;case"keydown":case"keyup":ch(q,n,B)}var I;if(Wu)t:{switch(t){case"compositionstart":var nt="onCompositionStart";break t;case"compositionend":nt="onCompositionEnd";break t;case"compositionupdate":nt="onCompositionUpdate";break t}nt=void 0}else za?$f(t,n)&&(nt="onCompositionEnd"):t==="keydown"&&n.keyCode===229&&(nt="onCompositionStart");nt&&(Pf&&n.locale!=="ko"&&(za||nt!=="onCompositionStart"?nt==="onCompositionEnd"&&za&&(I=Xf()):(En=B,ku="value"in En?En.value:En.textContent,za=!0)),ct=Ks(D,nt),0<ct.length&&(nt=new Zf(nt,t,null,n,B),q.push({event:nt,listeners:ct}),I?nt.data=I:(I=Wf(n),I!==null&&(nt.data=I)))),(I=C0?O0(t,n):w0(t,n))&&(nt=Ks(D,"onBeforeInput"),0<nt.length&&(ct=new Zf("onBeforeInput","beforeinput",null,n,B),q.push({event:ct,listeners:nt}),ct.data=I)),T1(q,t,D,n,B)}Dm(q,e)})}function rl(t,e,n){return{instance:t,listener:e,currentTarget:n}}function Ks(t,e){for(var n=e+"Capture",l=[];t!==null;){var r=t,o=r.stateNode;r=r.tag,r!==5&&r!==26&&r!==27||o===null||(r=Di(t,n),r!=null&&l.unshift(rl(t,r,o)),r=Di(t,e),r!=null&&l.push(rl(t,r,o))),t=t.return}return l}function ni(t){if(t===null)return null;do t=t.return;while(t&&t.tag!==5&&t.tag!==27);return t||null}function Cm(t,e,n,l,r){for(var o=e._reactName,y=[];n!==null&&n!==l;){var v=n,T=v.alternate,D=v.stateNode;if(v=v.tag,T!==null&&T===l)break;v!==5&&v!==26&&v!==27||D===null||(T=D,r?(D=Di(n,o),D!=null&&y.unshift(rl(n,D,T))):r||(D=Di(n,o),D!=null&&y.push(rl(n,D,T)))),n=n.return}y.length!==0&&t.push({event:e,listeners:y})}var R1=/\r\n?/g,D1=/\u0000|\uFFFD/g;function Om(t){return(typeof t=="string"?t:""+t).replace(R1,`
`).replace(D1,"")}function wm(t,e){return e=Om(e),Om(t)===e}function Qs(){}function bt(t,e,n,l,r,o){switch(n){case"children":typeof l=="string"?e==="body"||e==="textarea"&&l===""||wa(t,l):(typeof l=="number"||typeof l=="bigint")&&e!=="body"&&wa(t,""+l);break;case"className":Il(t,"class",l);break;case"tabIndex":Il(t,"tabindex",l);break;case"dir":case"role":case"viewBox":case"width":case"height":Il(t,n,l);break;case"style":qf(t,l,o);break;case"data":if(e!=="object"){Il(t,"data",l);break}case"src":case"href":if(l===""&&(e!=="a"||n!=="href")){t.removeAttribute(n);break}if(l==null||typeof l=="function"||typeof l=="symbol"||typeof l=="boolean"){t.removeAttribute(n);break}l=ns(""+l),t.setAttribute(n,l);break;case"action":case"formAction":if(typeof l=="function"){t.setAttribute(n,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}else typeof o=="function"&&(n==="formAction"?(e!=="input"&&bt(t,e,"name",r.name,r,null),bt(t,e,"formEncType",r.formEncType,r,null),bt(t,e,"formMethod",r.formMethod,r,null),bt(t,e,"formTarget",r.formTarget,r,null)):(bt(t,e,"encType",r.encType,r,null),bt(t,e,"method",r.method,r,null),bt(t,e,"target",r.target,r,null)));if(l==null||typeof l=="symbol"||typeof l=="boolean"){t.removeAttribute(n);break}l=ns(""+l),t.setAttribute(n,l);break;case"onClick":l!=null&&(t.onclick=Qs);break;case"onScroll":l!=null&&ht("scroll",t);break;case"onScrollEnd":l!=null&&ht("scrollend",t);break;case"dangerouslySetInnerHTML":if(l!=null){if(typeof l!="object"||!("__html"in l))throw Error(u(61));if(n=l.__html,n!=null){if(r.children!=null)throw Error(u(60));t.innerHTML=n}}break;case"multiple":t.multiple=l&&typeof l!="function"&&typeof l!="symbol";break;case"muted":t.muted=l&&typeof l!="function"&&typeof l!="symbol";break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":break;case"autoFocus":break;case"xlinkHref":if(l==null||typeof l=="function"||typeof l=="boolean"||typeof l=="symbol"){t.removeAttribute("xlink:href");break}n=ns(""+l),t.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",n);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":l!=null&&typeof l!="function"&&typeof l!="symbol"?t.setAttribute(n,""+l):t.removeAttribute(n);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":l&&typeof l!="function"&&typeof l!="symbol"?t.setAttribute(n,""):t.removeAttribute(n);break;case"capture":case"download":l===!0?t.setAttribute(n,""):l!==!1&&l!=null&&typeof l!="function"&&typeof l!="symbol"?t.setAttribute(n,l):t.removeAttribute(n);break;case"cols":case"rows":case"size":case"span":l!=null&&typeof l!="function"&&typeof l!="symbol"&&!isNaN(l)&&1<=l?t.setAttribute(n,l):t.removeAttribute(n);break;case"rowSpan":case"start":l==null||typeof l=="function"||typeof l=="symbol"||isNaN(l)?t.removeAttribute(n):t.setAttribute(n,l);break;case"popover":ht("beforetoggle",t),ht("toggle",t),Wl(t,"popover",l);break;case"xlinkActuate":Ie(t,"http://www.w3.org/1999/xlink","xlink:actuate",l);break;case"xlinkArcrole":Ie(t,"http://www.w3.org/1999/xlink","xlink:arcrole",l);break;case"xlinkRole":Ie(t,"http://www.w3.org/1999/xlink","xlink:role",l);break;case"xlinkShow":Ie(t,"http://www.w3.org/1999/xlink","xlink:show",l);break;case"xlinkTitle":Ie(t,"http://www.w3.org/1999/xlink","xlink:title",l);break;case"xlinkType":Ie(t,"http://www.w3.org/1999/xlink","xlink:type",l);break;case"xmlBase":Ie(t,"http://www.w3.org/XML/1998/namespace","xml:base",l);break;case"xmlLang":Ie(t,"http://www.w3.org/XML/1998/namespace","xml:lang",l);break;case"xmlSpace":Ie(t,"http://www.w3.org/XML/1998/namespace","xml:space",l);break;case"is":Wl(t,"is",l);break;case"innerText":case"textContent":break;default:(!(2<n.length)||n[0]!=="o"&&n[0]!=="O"||n[1]!=="n"&&n[1]!=="N")&&(n=e0.get(n)||n,Wl(t,n,l))}}function Mo(t,e,n,l,r,o){switch(n){case"style":qf(t,l,o);break;case"dangerouslySetInnerHTML":if(l!=null){if(typeof l!="object"||!("__html"in l))throw Error(u(61));if(n=l.__html,n!=null){if(r.children!=null)throw Error(u(60));t.innerHTML=n}}break;case"children":typeof l=="string"?wa(t,l):(typeof l=="number"||typeof l=="bigint")&&wa(t,""+l);break;case"onScroll":l!=null&&ht("scroll",t);break;case"onScrollEnd":l!=null&&ht("scrollend",t);break;case"onClick":l!=null&&(t.onclick=Qs);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":break;case"innerText":case"textContent":break;default:if(!_f.hasOwnProperty(n))t:{if(n[0]==="o"&&n[1]==="n"&&(r=n.endsWith("Capture"),e=n.slice(2,r?n.length-7:void 0),o=t[ue]||null,o=o!=null?o[n]:null,typeof o=="function"&&t.removeEventListener(e,o,r),typeof l=="function")){typeof o!="function"&&o!==null&&(n in t?t[n]=null:t.hasAttribute(n)&&t.removeAttribute(n)),t.addEventListener(e,l,r);break t}n in t?t[n]=l:l===!0?t.setAttribute(n,""):Wl(t,n,l)}}}function Ft(t,e,n){switch(e){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":ht("error",t),ht("load",t);var l=!1,r=!1,o;for(o in n)if(n.hasOwnProperty(o)){var y=n[o];if(y!=null)switch(o){case"src":l=!0;break;case"srcSet":r=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(u(137,e));default:bt(t,e,o,y,n,null)}}r&&bt(t,e,"srcSet",n.srcSet,n,null),l&&bt(t,e,"src",n.src,n,null);return;case"input":ht("invalid",t);var v=o=y=r=null,T=null,D=null;for(l in n)if(n.hasOwnProperty(l)){var B=n[l];if(B!=null)switch(l){case"name":r=B;break;case"type":y=B;break;case"checked":T=B;break;case"defaultChecked":D=B;break;case"value":o=B;break;case"defaultValue":v=B;break;case"children":case"dangerouslySetInnerHTML":if(B!=null)throw Error(u(137,e));break;default:bt(t,e,l,B,n,null)}}jf(t,o,v,T,D,y,r,!1),ts(t);return;case"select":ht("invalid",t),l=y=o=null;for(r in n)if(n.hasOwnProperty(r)&&(v=n[r],v!=null))switch(r){case"value":o=v;break;case"defaultValue":y=v;break;case"multiple":l=v;default:bt(t,e,r,v,n,null)}e=o,n=y,t.multiple=!!l,e!=null?Oa(t,!!l,e,!1):n!=null&&Oa(t,!!l,n,!0);return;case"textarea":ht("invalid",t),o=r=l=null;for(y in n)if(n.hasOwnProperty(y)&&(v=n[y],v!=null))switch(y){case"value":l=v;break;case"defaultValue":r=v;break;case"children":o=v;break;case"dangerouslySetInnerHTML":if(v!=null)throw Error(u(91));break;default:bt(t,e,y,v,n,null)}Nf(t,l,r,o),ts(t);return;case"option":for(T in n)if(n.hasOwnProperty(T)&&(l=n[T],l!=null))switch(T){case"selected":t.selected=l&&typeof l!="function"&&typeof l!="symbol";break;default:bt(t,e,T,l,n,null)}return;case"dialog":ht("cancel",t),ht("close",t);break;case"iframe":case"object":ht("load",t);break;case"video":case"audio":for(l=0;l<ul.length;l++)ht(ul[l],t);break;case"image":ht("error",t),ht("load",t);break;case"details":ht("toggle",t);break;case"embed":case"source":case"link":ht("error",t),ht("load",t);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(D in n)if(n.hasOwnProperty(D)&&(l=n[D],l!=null))switch(D){case"children":case"dangerouslySetInnerHTML":throw Error(u(137,e));default:bt(t,e,D,l,n,null)}return;default:if(Gu(e)){for(B in n)n.hasOwnProperty(B)&&(l=n[B],l!==void 0&&Mo(t,e,B,l,n,void 0));return}}for(v in n)n.hasOwnProperty(v)&&(l=n[v],l!=null&&bt(t,e,v,l,n,null))}function M1(t,e,n,l){switch(e){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var r=null,o=null,y=null,v=null,T=null,D=null,B=null;for(U in n){var q=n[U];if(n.hasOwnProperty(U)&&q!=null)switch(U){case"checked":break;case"value":break;case"defaultValue":T=q;default:l.hasOwnProperty(U)||bt(t,e,U,null,l,q)}}for(var _ in l){var U=l[_];if(q=n[_],l.hasOwnProperty(_)&&(U!=null||q!=null))switch(_){case"type":o=U;break;case"name":r=U;break;case"checked":D=U;break;case"defaultChecked":B=U;break;case"value":y=U;break;case"defaultValue":v=U;break;case"children":case"dangerouslySetInnerHTML":if(U!=null)throw Error(u(137,e));break;default:U!==q&&bt(t,e,_,U,l,q)}}qu(t,y,v,T,D,B,o,r);return;case"select":U=y=v=_=null;for(o in n)if(T=n[o],n.hasOwnProperty(o)&&T!=null)switch(o){case"value":break;case"multiple":U=T;default:l.hasOwnProperty(o)||bt(t,e,o,null,l,T)}for(r in l)if(o=l[r],T=n[r],l.hasOwnProperty(r)&&(o!=null||T!=null))switch(r){case"value":_=o;break;case"defaultValue":v=o;break;case"multiple":y=o;default:o!==T&&bt(t,e,r,o,l,T)}e=v,n=y,l=U,_!=null?Oa(t,!!n,_,!1):!!l!=!!n&&(e!=null?Oa(t,!!n,e,!0):Oa(t,!!n,n?[]:"",!1));return;case"textarea":U=_=null;for(v in n)if(r=n[v],n.hasOwnProperty(v)&&r!=null&&!l.hasOwnProperty(v))switch(v){case"value":break;case"children":break;default:bt(t,e,v,null,l,r)}for(y in l)if(r=l[y],o=n[y],l.hasOwnProperty(y)&&(r!=null||o!=null))switch(y){case"value":_=r;break;case"defaultValue":U=r;break;case"children":break;case"dangerouslySetInnerHTML":if(r!=null)throw Error(u(91));break;default:r!==o&&bt(t,e,y,r,l,o)}Lf(t,_,U);return;case"option":for(var F in n)if(_=n[F],n.hasOwnProperty(F)&&_!=null&&!l.hasOwnProperty(F))switch(F){case"selected":t.selected=!1;break;default:bt(t,e,F,null,l,_)}for(T in l)if(_=l[T],U=n[T],l.hasOwnProperty(T)&&_!==U&&(_!=null||U!=null))switch(T){case"selected":t.selected=_&&typeof _!="function"&&typeof _!="symbol";break;default:bt(t,e,T,_,l,U)}return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var lt in n)_=n[lt],n.hasOwnProperty(lt)&&_!=null&&!l.hasOwnProperty(lt)&&bt(t,e,lt,null,l,_);for(D in l)if(_=l[D],U=n[D],l.hasOwnProperty(D)&&_!==U&&(_!=null||U!=null))switch(D){case"children":case"dangerouslySetInnerHTML":if(_!=null)throw Error(u(137,e));break;default:bt(t,e,D,_,l,U)}return;default:if(Gu(e)){for(var zt in n)_=n[zt],n.hasOwnProperty(zt)&&_!==void 0&&!l.hasOwnProperty(zt)&&Mo(t,e,zt,void 0,l,_);for(B in l)_=l[B],U=n[B],!l.hasOwnProperty(B)||_===U||_===void 0&&U===void 0||Mo(t,e,B,_,l,U);return}}for(var C in n)_=n[C],n.hasOwnProperty(C)&&_!=null&&!l.hasOwnProperty(C)&&bt(t,e,C,null,l,_);for(q in l)_=l[q],U=n[q],!l.hasOwnProperty(q)||_===U||_==null&&U==null||bt(t,e,q,_,l,U)}var Co=null,Oo=null;function Zs(t){return t.nodeType===9?t:t.ownerDocument}function _m(t){switch(t){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function Vm(t,e){if(t===0)switch(e){case"svg":return 1;case"math":return 2;default:return 0}return t===1&&e==="foreignObject"?0:t}function wo(t,e){return t==="textarea"||t==="noscript"||typeof e.children=="string"||typeof e.children=="number"||typeof e.children=="bigint"||typeof e.dangerouslySetInnerHTML=="object"&&e.dangerouslySetInnerHTML!==null&&e.dangerouslySetInnerHTML.__html!=null}var _o=null;function C1(){var t=window.event;return t&&t.type==="popstate"?t===_o?!1:(_o=t,!0):(_o=null,!1)}var zm=typeof setTimeout=="function"?setTimeout:void 0,O1=typeof clearTimeout=="function"?clearTimeout:void 0,Um=typeof Promise=="function"?Promise:void 0,w1=typeof queueMicrotask=="function"?queueMicrotask:typeof Um<"u"?function(t){return Um.resolve(null).then(t).catch(_1)}:zm;function _1(t){setTimeout(function(){throw t})}function Vo(t,e){var n=e,l=0;do{var r=n.nextSibling;if(t.removeChild(n),r&&r.nodeType===8)if(n=r.data,n==="/$"){if(l===0){t.removeChild(r),yl(e);return}l--}else n!=="$"&&n!=="$?"&&n!=="$!"||l++;n=r}while(n);yl(e)}function zo(t){var e=t.firstChild;for(e&&e.nodeType===10&&(e=e.nextSibling);e;){var n=e;switch(e=e.nextSibling,n.nodeName){case"HTML":case"HEAD":case"BODY":zo(n),Hu(n);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if(n.rel.toLowerCase()==="stylesheet")continue}t.removeChild(n)}}function V1(t,e,n,l){for(;t.nodeType===1;){var r=n;if(t.nodeName.toLowerCase()!==e.toLowerCase()){if(!l&&(t.nodeName!=="INPUT"||t.type!=="hidden"))break}else if(l){if(!t[Ai])switch(e){case"meta":if(!t.hasAttribute("itemprop"))break;return t;case"link":if(o=t.getAttribute("rel"),o==="stylesheet"&&t.hasAttribute("data-precedence"))break;if(o!==r.rel||t.getAttribute("href")!==(r.href==null?null:r.href)||t.getAttribute("crossorigin")!==(r.crossOrigin==null?null:r.crossOrigin)||t.getAttribute("title")!==(r.title==null?null:r.title))break;return t;case"style":if(t.hasAttribute("data-precedence"))break;return t;case"script":if(o=t.getAttribute("src"),(o!==(r.src==null?null:r.src)||t.getAttribute("type")!==(r.type==null?null:r.type)||t.getAttribute("crossorigin")!==(r.crossOrigin==null?null:r.crossOrigin))&&o&&t.hasAttribute("async")&&!t.hasAttribute("itemprop"))break;return t;default:return t}}else if(e==="input"&&t.type==="hidden"){var o=r.name==null?null:""+r.name;if(r.type==="hidden"&&t.getAttribute("name")===o)return t}else return t;if(t=Ne(t.nextSibling),t===null)break}return null}function z1(t,e,n){if(e==="")return null;for(;t.nodeType!==3;)if((t.nodeType!==1||t.nodeName!=="INPUT"||t.type!=="hidden")&&!n||(t=Ne(t.nextSibling),t===null))return null;return t}function Ne(t){for(;t!=null;t=t.nextSibling){var e=t.nodeType;if(e===1||e===3)break;if(e===8){if(e=t.data,e==="$"||e==="$!"||e==="$?"||e==="F!"||e==="F")break;if(e==="/$")return null}}return t}function Bm(t){t=t.previousSibling;for(var e=0;t;){if(t.nodeType===8){var n=t.data;if(n==="$"||n==="$!"||n==="$?"){if(e===0)return t;e--}else n==="/$"&&e++}t=t.previousSibling}return null}function jm(t,e,n){switch(e=Zs(n),t){case"html":if(t=e.documentElement,!t)throw Error(u(452));return t;case"head":if(t=e.head,!t)throw Error(u(453));return t;case"body":if(t=e.body,!t)throw Error(u(454));return t;default:throw Error(u(451))}}var Ve=new Map,Lm=new Set;function ks(t){return typeof t.getRootNode=="function"?t.getRootNode():t.ownerDocument}var pn=Q.d;Q.d={f:U1,r:B1,D:j1,C:L1,L:N1,m:H1,X:Y1,S:q1,M:G1};function U1(){var t=pn.f(),e=Hs();return t||e}function B1(t){var e=Da(t);e!==null&&e.tag===5&&e.type==="form"?cd(e):pn.r(t)}var ai=typeof document>"u"?null:document;function Nm(t,e,n){var l=ai;if(l&&typeof e=="string"&&e){var r=xe(e);r='link[rel="'+t+'"][href="'+r+'"]',typeof n=="string"&&(r+='[crossorigin="'+n+'"]'),Lm.has(r)||(Lm.add(r),t={rel:t,crossOrigin:n,href:e},l.querySelector(r)===null&&(e=l.createElement("link"),Ft(e,"link",t),Xt(e),l.head.appendChild(e)))}}function j1(t){pn.D(t),Nm("dns-prefetch",t,null)}function L1(t,e){pn.C(t,e),Nm("preconnect",t,e)}function N1(t,e,n){pn.L(t,e,n);var l=ai;if(l&&t&&e){var r='link[rel="preload"][as="'+xe(e)+'"]';e==="image"&&n&&n.imageSrcSet?(r+='[imagesrcset="'+xe(n.imageSrcSet)+'"]',typeof n.imageSizes=="string"&&(r+='[imagesizes="'+xe(n.imageSizes)+'"]')):r+='[href="'+xe(t)+'"]';var o=r;switch(e){case"style":o=ii(t);break;case"script":o=li(t)}Ve.has(o)||(t=tt({rel:"preload",href:e==="image"&&n&&n.imageSrcSet?void 0:t,as:e},n),Ve.set(o,t),l.querySelector(r)!==null||e==="style"&&l.querySelector(ol(o))||e==="script"&&l.querySelector(cl(o))||(e=l.createElement("link"),Ft(e,"link",t),Xt(e),l.head.appendChild(e)))}}function H1(t,e){pn.m(t,e);var n=ai;if(n&&t){var l=e&&typeof e.as=="string"?e.as:"script",r='link[rel="modulepreload"][as="'+xe(l)+'"][href="'+xe(t)+'"]',o=r;switch(l){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":o=li(t)}if(!Ve.has(o)&&(t=tt({rel:"modulepreload",href:t},e),Ve.set(o,t),n.querySelector(r)===null)){switch(l){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(n.querySelector(cl(o)))return}l=n.createElement("link"),Ft(l,"link",t),Xt(l),n.head.appendChild(l)}}}function q1(t,e,n){pn.S(t,e,n);var l=ai;if(l&&t){var r=Ma(l).hoistableStyles,o=ii(t);e=e||"default";var y=r.get(o);if(!y){var v={loading:0,preload:null};if(y=l.querySelector(ol(o)))v.loading=5;else{t=tt({rel:"stylesheet",href:t,"data-precedence":e},n),(n=Ve.get(o))&&Uo(t,n);var T=y=l.createElement("link");Xt(T),Ft(T,"link",t),T._p=new Promise(function(D,B){T.onload=D,T.onerror=B}),T.addEventListener("load",function(){v.loading|=1}),T.addEventListener("error",function(){v.loading|=2}),v.loading|=4,Ps(y,e,l)}y={type:"stylesheet",instance:y,count:1,state:v},r.set(o,y)}}}function Y1(t,e){pn.X(t,e);var n=ai;if(n&&t){var l=Ma(n).hoistableScripts,r=li(t),o=l.get(r);o||(o=n.querySelector(cl(r)),o||(t=tt({src:t,async:!0},e),(e=Ve.get(r))&&Bo(t,e),o=n.createElement("script"),Xt(o),Ft(o,"link",t),n.head.appendChild(o)),o={type:"script",instance:o,count:1,state:null},l.set(r,o))}}function G1(t,e){pn.M(t,e);var n=ai;if(n&&t){var l=Ma(n).hoistableScripts,r=li(t),o=l.get(r);o||(o=n.querySelector(cl(r)),o||(t=tt({src:t,async:!0,type:"module"},e),(e=Ve.get(r))&&Bo(t,e),o=n.createElement("script"),Xt(o),Ft(o,"link",t),n.head.appendChild(o)),o={type:"script",instance:o,count:1,state:null},l.set(r,o))}}function Hm(t,e,n,l){var r=(r=bn.current)?ks(r):null;if(!r)throw Error(u(446));switch(t){case"meta":case"title":return null;case"style":return typeof n.precedence=="string"&&typeof n.href=="string"?(e=ii(n.href),n=Ma(r).hoistableStyles,l=n.get(e),l||(l={type:"style",instance:null,count:0,state:null},n.set(e,l)),l):{type:"void",instance:null,count:0,state:null};case"link":if(n.rel==="stylesheet"&&typeof n.href=="string"&&typeof n.precedence=="string"){t=ii(n.href);var o=Ma(r).hoistableStyles,y=o.get(t);if(y||(r=r.ownerDocument||r,y={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},o.set(t,y),(o=r.querySelector(ol(t)))&&!o._p&&(y.instance=o,y.state.loading=5),Ve.has(t)||(n={rel:"preload",as:"style",href:n.href,crossOrigin:n.crossOrigin,integrity:n.integrity,media:n.media,hrefLang:n.hrefLang,referrerPolicy:n.referrerPolicy},Ve.set(t,n),o||X1(r,t,n,y.state))),e&&l===null)throw Error(u(528,""));return y}if(e&&l!==null)throw Error(u(529,""));return null;case"script":return e=n.async,n=n.src,typeof n=="string"&&e&&typeof e!="function"&&typeof e!="symbol"?(e=li(n),n=Ma(r).hoistableScripts,l=n.get(e),l||(l={type:"script",instance:null,count:0,state:null},n.set(e,l)),l):{type:"void",instance:null,count:0,state:null};default:throw Error(u(444,t))}}function ii(t){return'href="'+xe(t)+'"'}function ol(t){return'link[rel="stylesheet"]['+t+"]"}function qm(t){return tt({},t,{"data-precedence":t.precedence,precedence:null})}function X1(t,e,n,l){t.querySelector('link[rel="preload"][as="style"]['+e+"]")?l.loading=1:(e=t.createElement("link"),l.preload=e,e.addEventListener("load",function(){return l.loading|=1}),e.addEventListener("error",function(){return l.loading|=2}),Ft(e,"link",n),Xt(e),t.head.appendChild(e))}function li(t){return'[src="'+xe(t)+'"]'}function cl(t){return"script[async]"+t}function Ym(t,e,n){if(e.count++,e.instance===null)switch(e.type){case"style":var l=t.querySelector('style[data-href~="'+xe(n.href)+'"]');if(l)return e.instance=l,Xt(l),l;var r=tt({},n,{"data-href":n.href,"data-precedence":n.precedence,href:null,precedence:null});return l=(t.ownerDocument||t).createElement("style"),Xt(l),Ft(l,"style",r),Ps(l,n.precedence,t),e.instance=l;case"stylesheet":r=ii(n.href);var o=t.querySelector(ol(r));if(o)return e.state.loading|=4,e.instance=o,Xt(o),o;l=qm(n),(r=Ve.get(r))&&Uo(l,r),o=(t.ownerDocument||t).createElement("link"),Xt(o);var y=o;return y._p=new Promise(function(v,T){y.onload=v,y.onerror=T}),Ft(o,"link",l),e.state.loading|=4,Ps(o,n.precedence,t),e.instance=o;case"script":return o=li(n.src),(r=t.querySelector(cl(o)))?(e.instance=r,Xt(r),r):(l=n,(r=Ve.get(o))&&(l=tt({},n),Bo(l,r)),t=t.ownerDocument||t,r=t.createElement("script"),Xt(r),Ft(r,"link",l),t.head.appendChild(r),e.instance=r);case"void":return null;default:throw Error(u(443,e.type))}else e.type==="stylesheet"&&(e.state.loading&4)===0&&(l=e.instance,e.state.loading|=4,Ps(l,n.precedence,t));return e.instance}function Ps(t,e,n){for(var l=n.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),r=l.length?l[l.length-1]:null,o=r,y=0;y<l.length;y++){var v=l[y];if(v.dataset.precedence===e)o=v;else if(o!==r)break}o?o.parentNode.insertBefore(t,o.nextSibling):(e=n.nodeType===9?n.head:n,e.insertBefore(t,e.firstChild))}function Uo(t,e){t.crossOrigin==null&&(t.crossOrigin=e.crossOrigin),t.referrerPolicy==null&&(t.referrerPolicy=e.referrerPolicy),t.title==null&&(t.title=e.title)}function Bo(t,e){t.crossOrigin==null&&(t.crossOrigin=e.crossOrigin),t.referrerPolicy==null&&(t.referrerPolicy=e.referrerPolicy),t.integrity==null&&(t.integrity=e.integrity)}var Fs=null;function Gm(t,e,n){if(Fs===null){var l=new Map,r=Fs=new Map;r.set(n,l)}else r=Fs,l=r.get(n),l||(l=new Map,r.set(n,l));if(l.has(t))return l;for(l.set(t,null),n=n.getElementsByTagName(t),r=0;r<n.length;r++){var o=n[r];if(!(o[Ai]||o[$t]||t==="link"&&o.getAttribute("rel")==="stylesheet")&&o.namespaceURI!=="http://www.w3.org/2000/svg"){var y=o.getAttribute(e)||"";y=t+y;var v=l.get(y);v?v.push(o):l.set(y,[o])}}return l}function Xm(t,e,n){t=t.ownerDocument||t,t.head.insertBefore(n,e==="title"?t.querySelector("head > title"):null)}function K1(t,e,n){if(n===1||e.itemProp!=null)return!1;switch(t){case"meta":case"title":return!0;case"style":if(typeof e.precedence!="string"||typeof e.href!="string"||e.href==="")break;return!0;case"link":if(typeof e.rel!="string"||typeof e.href!="string"||e.href===""||e.onLoad||e.onError)break;switch(e.rel){case"stylesheet":return t=e.disabled,typeof e.precedence=="string"&&t==null;default:return!0}case"script":if(e.async&&typeof e.async!="function"&&typeof e.async!="symbol"&&!e.onLoad&&!e.onError&&e.src&&typeof e.src=="string")return!0}return!1}function Km(t){return!(t.type==="stylesheet"&&(t.state.loading&3)===0)}var fl=null;function Q1(){}function Z1(t,e,n){if(fl===null)throw Error(u(475));var l=fl;if(e.type==="stylesheet"&&(typeof n.media!="string"||matchMedia(n.media).matches!==!1)&&(e.state.loading&4)===0){if(e.instance===null){var r=ii(n.href),o=t.querySelector(ol(r));if(o){t=o._p,t!==null&&typeof t=="object"&&typeof t.then=="function"&&(l.count++,l=Js.bind(l),t.then(l,l)),e.state.loading|=4,e.instance=o,Xt(o);return}o=t.ownerDocument||t,n=qm(n),(r=Ve.get(r))&&Uo(n,r),o=o.createElement("link"),Xt(o);var y=o;y._p=new Promise(function(v,T){y.onload=v,y.onerror=T}),Ft(o,"link",n),e.instance=o}l.stylesheets===null&&(l.stylesheets=new Map),l.stylesheets.set(e,t),(t=e.state.preload)&&(e.state.loading&3)===0&&(l.count++,e=Js.bind(l),t.addEventListener("load",e),t.addEventListener("error",e))}}function k1(){if(fl===null)throw Error(u(475));var t=fl;return t.stylesheets&&t.count===0&&jo(t,t.stylesheets),0<t.count?function(e){var n=setTimeout(function(){if(t.stylesheets&&jo(t,t.stylesheets),t.unsuspend){var l=t.unsuspend;t.unsuspend=null,l()}},6e4);return t.unsuspend=e,function(){t.unsuspend=null,clearTimeout(n)}}:null}function Js(){if(this.count--,this.count===0){if(this.stylesheets)jo(this,this.stylesheets);else if(this.unsuspend){var t=this.unsuspend;this.unsuspend=null,t()}}}var $s=null;function jo(t,e){t.stylesheets=null,t.unsuspend!==null&&(t.count++,$s=new Map,e.forEach(P1,t),$s=null,Js.call(t))}function P1(t,e){if(!(e.state.loading&4)){var n=$s.get(t);if(n)var l=n.get(null);else{n=new Map,$s.set(t,n);for(var r=t.querySelectorAll("link[data-precedence],style[data-precedence]"),o=0;o<r.length;o++){var y=r[o];(y.nodeName==="LINK"||y.getAttribute("media")!=="not all")&&(n.set(y.dataset.precedence,y),l=y)}l&&n.set(null,l)}r=e.instance,y=r.getAttribute("data-precedence"),o=n.get(y)||l,o===l&&n.set(null,r),n.set(y,r),this.count++,l=Js.bind(this),r.addEventListener("load",l),r.addEventListener("error",l),o?o.parentNode.insertBefore(r,o.nextSibling):(t=t.nodeType===9?t.head:t,t.insertBefore(r,t.firstChild)),e.state.loading|=4}}var hl={$$typeof:x,Provider:null,Consumer:null,_currentValue:dt,_currentValue2:dt,_threadCount:0};function F1(t,e,n,l,r,o,y,v){this.tag=1,this.containerInfo=t,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=Lu(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.finishedLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Lu(0),this.hiddenUpdates=Lu(null),this.identifierPrefix=l,this.onUncaughtError=r,this.onCaughtError=o,this.onRecoverableError=y,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=v,this.incompleteTransitions=new Map}function Qm(t,e,n,l,r,o,y,v,T,D,B,q){return t=new F1(t,e,n,y,v,T,D,q),e=1,o===!0&&(e|=24),o=we(3,null,null,e),t.current=o,o.stateNode=t,e=dr(),e.refCount++,t.pooledCache=e,e.refCount++,o.memoizedState={element:l,isDehydrated:n,cache:e},kr(o),t}function Zm(t){return t?(t=La,t):La}function km(t,e,n,l,r,o){r=Zm(r),l.context===null?l.context=r:l.pendingContext=r,l=_n(e),l.payload={element:n},o=o===void 0?null:o,o!==null&&(l.callback=o),n=Vn(t,l,e),n!==null&&(le(n,t,e),Fi(n,t,e))}function Pm(t,e){if(t=t.memoizedState,t!==null&&t.dehydrated!==null){var n=t.retryLane;t.retryLane=n!==0&&n<e?n:e}}function Lo(t,e){Pm(t,e),(t=t.alternate)&&Pm(t,e)}function Fm(t){if(t.tag===13){var e=An(t,67108864);e!==null&&le(e,t,67108864),Lo(t,67108864)}}var Ws=!0;function J1(t,e,n,l){var r=X.T;X.T=null;var o=Q.p;try{Q.p=2,No(t,e,n,l)}finally{Q.p=o,X.T=r}}function $1(t,e,n,l){var r=X.T;X.T=null;var o=Q.p;try{Q.p=8,No(t,e,n,l)}finally{Q.p=o,X.T=r}}function No(t,e,n,l){if(Ws){var r=Ho(l);if(r===null)Do(t,e,l,Is,n),$m(t,l);else if(I1(r,t,e,n,l))l.stopPropagation();else if($m(t,l),e&4&&-1<W1.indexOf(t)){for(;r!==null;){var o=Da(r);if(o!==null)switch(o.tag){case 3:if(o=o.stateNode,o.current.memoizedState.isDehydrated){var y=Fn(o.pendingLanes);if(y!==0){var v=o;for(v.pendingLanes|=2,v.entangledLanes|=2;y;){var T=1<<31-fe(y);v.entanglements[1]|=T,y&=~T}ke(o),(Mt&6)===0&&(js=Ge()+500,sl(0))}}break;case 13:v=An(o,2),v!==null&&le(v,o,2),Hs(),Lo(o,2)}if(o=Ho(l),o===null&&Do(t,e,l,Is,n),o===r)break;r=o}r!==null&&l.stopPropagation()}else Do(t,e,l,null,n)}}function Ho(t){return t=Ku(t),qo(t)}var Is=null;function qo(t){if(Is=null,t=Jn(t),t!==null){var e=W(t);if(e===null)t=null;else{var n=e.tag;if(n===13){if(t=gt(e),t!==null)return t;t=null}else if(n===3){if(e.stateNode.current.memoizedState.isDehydrated)return e.tag===3?e.stateNode.containerInfo:null;t=null}else e!==t&&(t=null)}}return Is=t,null}function Jm(t){switch(t){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(Lv()){case bf:return 2;case Tf:return 8;case Pl:case Nv:return 32;case xf:return 268435456;default:return 32}default:return 32}}var Yo=!1,Hn=null,qn=null,Yn=null,dl=new Map,ml=new Map,Gn=[],W1="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function $m(t,e){switch(t){case"focusin":case"focusout":Hn=null;break;case"dragenter":case"dragleave":qn=null;break;case"mouseover":case"mouseout":Yn=null;break;case"pointerover":case"pointerout":dl.delete(e.pointerId);break;case"gotpointercapture":case"lostpointercapture":ml.delete(e.pointerId)}}function pl(t,e,n,l,r,o){return t===null||t.nativeEvent!==o?(t={blockedOn:e,domEventName:n,eventSystemFlags:l,nativeEvent:o,targetContainers:[r]},e!==null&&(e=Da(e),e!==null&&Fm(e)),t):(t.eventSystemFlags|=l,e=t.targetContainers,r!==null&&e.indexOf(r)===-1&&e.push(r),t)}function I1(t,e,n,l,r){switch(e){case"focusin":return Hn=pl(Hn,t,e,n,l,r),!0;case"dragenter":return qn=pl(qn,t,e,n,l,r),!0;case"mouseover":return Yn=pl(Yn,t,e,n,l,r),!0;case"pointerover":var o=r.pointerId;return dl.set(o,pl(dl.get(o)||null,t,e,n,l,r)),!0;case"gotpointercapture":return o=r.pointerId,ml.set(o,pl(ml.get(o)||null,t,e,n,l,r)),!0}return!1}function Wm(t){var e=Jn(t.target);if(e!==null){var n=W(e);if(n!==null){if(e=n.tag,e===13){if(e=gt(n),e!==null){t.blockedOn=e,kv(t.priority,function(){if(n.tag===13){var l=ye(),r=An(n,l);r!==null&&le(r,n,l),Lo(n,l)}});return}}else if(e===3&&n.stateNode.current.memoizedState.isDehydrated){t.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}t.blockedOn=null}function tu(t){if(t.blockedOn!==null)return!1;for(var e=t.targetContainers;0<e.length;){var n=Ho(t.nativeEvent);if(n===null){n=t.nativeEvent;var l=new n.constructor(n.type,n);Xu=l,n.target.dispatchEvent(l),Xu=null}else return e=Da(n),e!==null&&Fm(e),t.blockedOn=n,!1;e.shift()}return!0}function Im(t,e,n){tu(t)&&n.delete(e)}function tS(){Yo=!1,Hn!==null&&tu(Hn)&&(Hn=null),qn!==null&&tu(qn)&&(qn=null),Yn!==null&&tu(Yn)&&(Yn=null),dl.forEach(Im),ml.forEach(Im)}function eu(t,e){t.blockedOn===e&&(t.blockedOn=null,Yo||(Yo=!0,a.unstable_scheduleCallback(a.unstable_NormalPriority,tS)))}var nu=null;function tp(t){nu!==t&&(nu=t,a.unstable_scheduleCallback(a.unstable_NormalPriority,function(){nu===t&&(nu=null);for(var e=0;e<t.length;e+=3){var n=t[e],l=t[e+1],r=t[e+2];if(typeof l!="function"){if(qo(l||n)===null)continue;break}var o=Da(n);o!==null&&(t.splice(e,3),e-=3,Or(o,{pending:!0,data:r,method:n.method,action:l},l,r))}}))}function yl(t){function e(T){return eu(T,t)}Hn!==null&&eu(Hn,t),qn!==null&&eu(qn,t),Yn!==null&&eu(Yn,t),dl.forEach(e),ml.forEach(e);for(var n=0;n<Gn.length;n++){var l=Gn[n];l.blockedOn===t&&(l.blockedOn=null)}for(;0<Gn.length&&(n=Gn[0],n.blockedOn===null);)Wm(n),n.blockedOn===null&&Gn.shift();if(n=(t.ownerDocument||t).$$reactFormReplay,n!=null)for(l=0;l<n.length;l+=3){var r=n[l],o=n[l+1],y=r[ue]||null;if(typeof o=="function")y||tp(n);else if(y){var v=null;if(o&&o.hasAttribute("formAction")){if(r=o,y=o[ue]||null)v=y.formAction;else if(qo(r)!==null)continue}else v=y.action;typeof v=="function"?n[l+1]=v:(n.splice(l,3),l-=3),tp(n)}}}function Go(t){this._internalRoot=t}au.prototype.render=Go.prototype.render=function(t){var e=this._internalRoot;if(e===null)throw Error(u(409));var n=e.current,l=ye();km(n,l,t,e,null,null)},au.prototype.unmount=Go.prototype.unmount=function(){var t=this._internalRoot;if(t!==null){this._internalRoot=null;var e=t.containerInfo;t.tag===0&&ti(),km(t.current,2,null,t,null,null),Hs(),e[Ra]=null}};function au(t){this._internalRoot=t}au.prototype.unstable_scheduleHydration=function(t){if(t){var e=Cf();t={blockedOn:null,target:t,priority:e};for(var n=0;n<Gn.length&&e!==0&&e<Gn[n].priority;n++);Gn.splice(n,0,t),n===0&&Wm(t)}};var ep=i.version;if(ep!=="19.0.0")throw Error(u(527,ep,"19.0.0"));Q.findDOMNode=function(t){var e=t._reactInternals;if(e===void 0)throw typeof t.render=="function"?Error(u(188)):(t=Object.keys(t).join(","),Error(u(268,t)));return t=G(e),t=t!==null?it(t):null,t=t===null?null:t.stateNode,t};var eS={bundleType:0,version:"19.0.0",rendererPackageName:"react-dom",currentDispatcherRef:X,findFiberByHostInstance:Jn,reconcilerVersion:"19.0.0"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var iu=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!iu.isDisabled&&iu.supportsFiber)try{Ti=iu.inject(eS),ce=iu}catch{}}return vl.createRoot=function(t,e){if(!c(t))throw Error(u(299));var n=!1,l="",r=vd,o=Sd,y=bd,v=null;return e!=null&&(e.unstable_strictMode===!0&&(n=!0),e.identifierPrefix!==void 0&&(l=e.identifierPrefix),e.onUncaughtError!==void 0&&(r=e.onUncaughtError),e.onCaughtError!==void 0&&(o=e.onCaughtError),e.onRecoverableError!==void 0&&(y=e.onRecoverableError),e.unstable_transitionCallbacks!==void 0&&(v=e.unstable_transitionCallbacks)),e=Qm(t,1,!1,null,null,n,l,r,o,y,v,null),t[Ra]=e.current,Ro(t.nodeType===8?t.parentNode:t),new Go(e)},vl.hydrateRoot=function(t,e,n){if(!c(t))throw Error(u(299));var l=!1,r="",o=vd,y=Sd,v=bd,T=null,D=null;return n!=null&&(n.unstable_strictMode===!0&&(l=!0),n.identifierPrefix!==void 0&&(r=n.identifierPrefix),n.onUncaughtError!==void 0&&(o=n.onUncaughtError),n.onCaughtError!==void 0&&(y=n.onCaughtError),n.onRecoverableError!==void 0&&(v=n.onRecoverableError),n.unstable_transitionCallbacks!==void 0&&(T=n.unstable_transitionCallbacks),n.formState!==void 0&&(D=n.formState)),e=Qm(t,1,!0,e,n??null,l,r,o,y,v,T,D),e.context=Zm(null),n=e.current,l=ye(),r=_n(l),r.callback=null,Vn(n,r,l),e.current.lanes=l,Ei(e,l),ke(e),t[Ra]=e.current,Ro(t),new au(e)},vl.version="19.0.0",vl}var hp;function mS(){if(hp)return Ko.exports;hp=1;function a(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(a)}catch(i){console.error(i)}}return a(),Ko.exports=dS(),Ko.exports}var pS=mS(),R=Oc();const dp=lS(R);var Sl={},mp;function yS(){if(mp)return Sl;mp=1,Object.defineProperty(Sl,"__esModule",{value:!0}),Sl.parse=h,Sl.serialize=m;const a=/^[\u0021-\u003A\u003C\u003E-\u007E]+$/,i=/^[\u0021-\u003A\u003C-\u007E]*$/,s=/^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i,u=/^[\u0020-\u003A\u003D-\u007E]*$/,c=Object.prototype.toString,f=(()=>{const b=function(){};return b.prototype=Object.create(null),b})();function h(b,x){const M=new f,z=b.length;if(z<2)return M;const V=(x==null?void 0:x.decode)||g;let O=0;do{const L=b.indexOf("=",O);if(L===-1)break;const j=b.indexOf(";",O),P=j===-1?z:j;if(L>P){O=b.lastIndexOf(";",L-1)+1;continue}const H=p(b,O,L),k=d(b,L,H),st=b.slice(H,k);if(M[st]===void 0){let J=p(b,L+1,P),X=d(b,P,J);const tt=V(b.slice(J,X));M[st]=tt}O=P+1}while(O<z);return M}function p(b,x,M){do{const z=b.charCodeAt(x);if(z!==32&&z!==9)return x}while(++x<M);return M}function d(b,x,M){for(;x>M;){const z=b.charCodeAt(--x);if(z!==32&&z!==9)return x+1}return M}function m(b,x,M){const z=(M==null?void 0:M.encode)||encodeURIComponent;if(!a.test(b))throw new TypeError(`argument name is invalid: ${b}`);const V=z(x);if(!i.test(V))throw new TypeError(`argument val is invalid: ${x}`);let O=b+"="+V;if(!M)return O;if(M.maxAge!==void 0){if(!Number.isInteger(M.maxAge))throw new TypeError(`option maxAge is invalid: ${M.maxAge}`);O+="; Max-Age="+M.maxAge}if(M.domain){if(!s.test(M.domain))throw new TypeError(`option domain is invalid: ${M.domain}`);O+="; Domain="+M.domain}if(M.path){if(!u.test(M.path))throw new TypeError(`option path is invalid: ${M.path}`);O+="; Path="+M.path}if(M.expires){if(!S(M.expires)||!Number.isFinite(M.expires.valueOf()))throw new TypeError(`option expires is invalid: ${M.expires}`);O+="; Expires="+M.expires.toUTCString()}if(M.httpOnly&&(O+="; HttpOnly"),M.secure&&(O+="; Secure"),M.partitioned&&(O+="; Partitioned"),M.priority)switch(typeof M.priority=="string"?M.priority.toLowerCase():void 0){case"low":O+="; Priority=Low";break;case"medium":O+="; Priority=Medium";break;case"high":O+="; Priority=High";break;default:throw new TypeError(`option priority is invalid: ${M.priority}`)}if(M.sameSite)switch(typeof M.sameSite=="string"?M.sameSite.toLowerCase():M.sameSite){case!0:case"strict":O+="; SameSite=Strict";break;case"lax":O+="; SameSite=Lax";break;case"none":O+="; SameSite=None";break;default:throw new TypeError(`option sameSite is invalid: ${M.sameSite}`)}return O}function g(b){if(b.indexOf("%")===-1)return b;try{return decodeURIComponent(b)}catch{return b}}function S(b){return c.call(b)==="[object Date]"}return Sl}yS();/**
 * react-router v7.1.5
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */var pp="popstate";function gS(a={}){function i(u,c){let{pathname:f,search:h,hash:p}=u.location;return cc("",{pathname:f,search:h,hash:p},c.state&&c.state.usr||null,c.state&&c.state.key||"default")}function s(u,c){return typeof c=="string"?c:Ol(c)}return SS(i,s,null,a)}function Ot(a,i){if(a===!1||a===null||typeof a>"u")throw new Error(i)}function Je(a,i){if(!a){typeof console<"u"&&console.warn(i);try{throw new Error(i)}catch{}}}function vS(){return Math.random().toString(36).substring(2,10)}function yp(a,i){return{usr:a.state,key:a.key,idx:i}}function cc(a,i,s=null,u){return{pathname:typeof a=="string"?a:a.pathname,search:"",hash:"",...typeof i=="string"?yi(i):i,state:s,key:i&&i.key||u||vS()}}function Ol({pathname:a="/",search:i="",hash:s=""}){return i&&i!=="?"&&(a+=i.charAt(0)==="?"?i:"?"+i),s&&s!=="#"&&(a+=s.charAt(0)==="#"?s:"#"+s),a}function yi(a){let i={};if(a){let s=a.indexOf("#");s>=0&&(i.hash=a.substring(s),a=a.substring(0,s));let u=a.indexOf("?");u>=0&&(i.search=a.substring(u),a=a.substring(0,u)),a&&(i.pathname=a)}return i}function SS(a,i,s,u={}){let{window:c=document.defaultView,v5Compat:f=!1}=u,h=c.history,p="POP",d=null,m=g();m==null&&(m=0,h.replaceState({...h.state,idx:m},""));function g(){return(h.state||{idx:null}).idx}function S(){p="POP";let V=g(),O=V==null?null:V-m;m=V,d&&d({action:p,location:z.location,delta:O})}function b(V,O){p="PUSH";let L=cc(z.location,V,O);m=g()+1;let j=yp(L,m),P=z.createHref(L);try{h.pushState(j,"",P)}catch(H){if(H instanceof DOMException&&H.name==="DataCloneError")throw H;c.location.assign(P)}f&&d&&d({action:p,location:z.location,delta:1})}function x(V,O){p="REPLACE";let L=cc(z.location,V,O);m=g();let j=yp(L,m),P=z.createHref(L);h.replaceState(j,"",P),f&&d&&d({action:p,location:z.location,delta:0})}function M(V){let O=c.location.origin!=="null"?c.location.origin:c.location.href,L=typeof V=="string"?V:Ol(V);return L=L.replace(/ $/,"%20"),Ot(O,`No window.location.(origin|href) available to create URL for href: ${L}`),new URL(L,O)}let z={get action(){return p},get location(){return a(c,h)},listen(V){if(d)throw new Error("A history only accepts one active listener");return c.addEventListener(pp,S),d=V,()=>{c.removeEventListener(pp,S),d=null}},createHref(V){return i(c,V)},createURL:M,encodeLocation(V){let O=M(V);return{pathname:O.pathname,search:O.search,hash:O.hash}},push:b,replace:x,go(V){return h.go(V)}};return z}function Hy(a,i,s="/"){return bS(a,i,s,!1)}function bS(a,i,s,u){let c=typeof i=="string"?yi(i):i,f=Qn(c.pathname||"/",s);if(f==null)return null;let h=qy(a);TS(h);let p=null;for(let d=0;p==null&&d<h.length;++d){let m=VS(f);p=wS(h[d],m,u)}return p}function qy(a,i=[],s=[],u=""){let c=(f,h,p)=>{let d={relativePath:p===void 0?f.path||"":p,caseSensitive:f.caseSensitive===!0,childrenIndex:h,route:f};d.relativePath.startsWith("/")&&(Ot(d.relativePath.startsWith(u),`Absolute route path "${d.relativePath}" nested under path "${u}" is not valid. An absolute child route path must start with the combined path of all its parent routes.`),d.relativePath=d.relativePath.slice(u.length));let m=yn([u,d.relativePath]),g=s.concat(d);f.children&&f.children.length>0&&(Ot(f.index!==!0,`Index routes must not have child routes. Please remove all child routes from route path "${m}".`),qy(f.children,i,g,m)),!(f.path==null&&!f.index)&&i.push({path:m,score:CS(m,f.index),routesMeta:g})};return a.forEach((f,h)=>{var p;if(f.path===""||!((p=f.path)!=null&&p.includes("?")))c(f,h);else for(let d of Yy(f.path))c(f,h,d)}),i}function Yy(a){let i=a.split("/");if(i.length===0)return[];let[s,...u]=i,c=s.endsWith("?"),f=s.replace(/\?$/,"");if(u.length===0)return c?[f,""]:[f];let h=Yy(u.join("/")),p=[];return p.push(...h.map(d=>d===""?f:[f,d].join("/"))),c&&p.push(...h),p.map(d=>a.startsWith("/")&&d===""?"/":d)}function TS(a){a.sort((i,s)=>i.score!==s.score?s.score-i.score:OS(i.routesMeta.map(u=>u.childrenIndex),s.routesMeta.map(u=>u.childrenIndex)))}var xS=/^:[\w-]+$/,ES=3,AS=2,RS=1,DS=10,MS=-2,gp=a=>a==="*";function CS(a,i){let s=a.split("/"),u=s.length;return s.some(gp)&&(u+=MS),i&&(u+=AS),s.filter(c=>!gp(c)).reduce((c,f)=>c+(xS.test(f)?ES:f===""?RS:DS),u)}function OS(a,i){return a.length===i.length&&a.slice(0,-1).every((u,c)=>u===i[c])?a[a.length-1]-i[i.length-1]:0}function wS(a,i,s=!1){let{routesMeta:u}=a,c={},f="/",h=[];for(let p=0;p<u.length;++p){let d=u[p],m=p===u.length-1,g=f==="/"?i:i.slice(f.length)||"/",S=yu({path:d.relativePath,caseSensitive:d.caseSensitive,end:m},g),b=d.route;if(!S&&m&&s&&!u[u.length-1].route.index&&(S=yu({path:d.relativePath,caseSensitive:d.caseSensitive,end:!1},g)),!S)return null;Object.assign(c,S.params),h.push({params:c,pathname:yn([f,S.pathname]),pathnameBase:jS(yn([f,S.pathnameBase])),route:b}),S.pathnameBase!=="/"&&(f=yn([f,S.pathnameBase]))}return h}function yu(a,i){typeof a=="string"&&(a={path:a,caseSensitive:!1,end:!0});let[s,u]=_S(a.path,a.caseSensitive,a.end),c=i.match(s);if(!c)return null;let f=c[0],h=f.replace(/(.)\/+$/,"$1"),p=c.slice(1);return{params:u.reduce((m,{paramName:g,isOptional:S},b)=>{if(g==="*"){let M=p[b]||"";h=f.slice(0,f.length-M.length).replace(/(.)\/+$/,"$1")}const x=p[b];return S&&!x?m[g]=void 0:m[g]=(x||"").replace(/%2F/g,"/"),m},{}),pathname:f,pathnameBase:h,pattern:a}}function _S(a,i=!1,s=!0){Je(a==="*"||!a.endsWith("*")||a.endsWith("/*"),`Route path "${a}" will be treated as if it were "${a.replace(/\*$/,"/*")}" because the \`*\` character must always follow a \`/\` in the pattern. To get rid of this warning, please change the route path to "${a.replace(/\*$/,"/*")}".`);let u=[],c="^"+a.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(h,p,d)=>(u.push({paramName:p,isOptional:d!=null}),d?"/?([^\\/]+)?":"/([^\\/]+)"));return a.endsWith("*")?(u.push({paramName:"*"}),c+=a==="*"||a==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):s?c+="\\/*$":a!==""&&a!=="/"&&(c+="(?:(?=\\/|$))"),[new RegExp(c,i?void 0:"i"),u]}function VS(a){try{return a.split("/").map(i=>decodeURIComponent(i).replace(/\//g,"%2F")).join("/")}catch(i){return Je(!1,`The URL path "${a}" could not be decoded because it is a malformed URL segment. This is probably due to a bad percent encoding (${i}).`),a}}function Qn(a,i){if(i==="/")return a;if(!a.toLowerCase().startsWith(i.toLowerCase()))return null;let s=i.endsWith("/")?i.length-1:i.length,u=a.charAt(s);return u&&u!=="/"?null:a.slice(s)||"/"}function zS(a,i="/"){let{pathname:s,search:u="",hash:c=""}=typeof a=="string"?yi(a):a;return{pathname:s?s.startsWith("/")?s:US(s,i):i,search:LS(u),hash:NS(c)}}function US(a,i){let s=i.replace(/\/+$/,"").split("/");return a.split("/").forEach(c=>{c===".."?s.length>1&&s.pop():c!=="."&&s.push(c)}),s.length>1?s.join("/"):"/"}function Fo(a,i,s,u){return`Cannot include a '${a}' character in a manually specified \`to.${i}\` field [${JSON.stringify(u)}].  Please separate it out to the \`to.${s}\` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.`}function BS(a){return a.filter((i,s)=>s===0||i.route.path&&i.route.path.length>0)}function Gy(a){let i=BS(a);return i.map((s,u)=>u===i.length-1?s.pathname:s.pathnameBase)}function Xy(a,i,s,u=!1){let c;typeof a=="string"?c=yi(a):(c={...a},Ot(!c.pathname||!c.pathname.includes("?"),Fo("?","pathname","search",c)),Ot(!c.pathname||!c.pathname.includes("#"),Fo("#","pathname","hash",c)),Ot(!c.search||!c.search.includes("#"),Fo("#","search","hash",c)));let f=a===""||c.pathname==="",h=f?"/":c.pathname,p;if(h==null)p=s;else{let S=i.length-1;if(!u&&h.startsWith("..")){let b=h.split("/");for(;b[0]==="..";)b.shift(),S-=1;c.pathname=b.join("/")}p=S>=0?i[S]:"/"}let d=zS(c,p),m=h&&h!=="/"&&h.endsWith("/"),g=(f||h===".")&&s.endsWith("/");return!d.pathname.endsWith("/")&&(m||g)&&(d.pathname+="/"),d}var yn=a=>a.join("/").replace(/\/\/+/g,"/"),jS=a=>a.replace(/\/+$/,"").replace(/^\/*/,"/"),LS=a=>!a||a==="?"?"":a.startsWith("?")?a:"?"+a,NS=a=>!a||a==="#"?"":a.startsWith("#")?a:"#"+a;function HS(a){return a!=null&&typeof a.status=="number"&&typeof a.statusText=="string"&&typeof a.internal=="boolean"&&"data"in a}var Ky=["POST","PUT","PATCH","DELETE"];new Set(Ky);var qS=["GET",...Ky];new Set(qS);var gi=R.createContext(null);gi.displayName="DataRouter";var Eu=R.createContext(null);Eu.displayName="DataRouterState";var Qy=R.createContext({isTransitioning:!1});Qy.displayName="ViewTransition";var YS=R.createContext(new Map);YS.displayName="Fetchers";var GS=R.createContext(null);GS.displayName="Await";var $e=R.createContext(null);$e.displayName="Navigation";var Nl=R.createContext(null);Nl.displayName="Location";var qe=R.createContext({outlet:null,matches:[],isDataRoute:!1});qe.displayName="Route";var wc=R.createContext(null);wc.displayName="RouteError";function XS(a,{relative:i}={}){Ot(Hl(),"useHref() may be used only in the context of a <Router> component.");let{basename:s,navigator:u}=R.useContext($e),{hash:c,pathname:f,search:h}=ql(a,{relative:i}),p=f;return s!=="/"&&(p=f==="/"?s:yn([s,f])),u.createHref({pathname:p,search:h,hash:c})}function Hl(){return R.useContext(Nl)!=null}function xa(){return Ot(Hl(),"useLocation() may be used only in the context of a <Router> component."),R.useContext(Nl).location}var Zy="You should call navigate() in a React.useEffect(), not when your component is first rendered.";function ky(a){R.useContext($e).static||R.useLayoutEffect(a)}function KS(){let{isDataRoute:a}=R.useContext(qe);return a?lb():QS()}function QS(){Ot(Hl(),"useNavigate() may be used only in the context of a <Router> component.");let a=R.useContext(gi),{basename:i,navigator:s}=R.useContext($e),{matches:u}=R.useContext(qe),{pathname:c}=xa(),f=JSON.stringify(Gy(u)),h=R.useRef(!1);return ky(()=>{h.current=!0}),R.useCallback((d,m={})=>{if(Je(h.current,Zy),!h.current)return;if(typeof d=="number"){s.go(d);return}let g=Xy(d,JSON.parse(f),c,m.relative==="path");a==null&&i!=="/"&&(g.pathname=g.pathname==="/"?i:yn([i,g.pathname])),(m.replace?s.replace:s.push)(g,m.state,m)},[i,s,f,c,a])}var ZS=R.createContext(null);function kS(a){let i=R.useContext(qe).outlet;return i&&R.createElement(ZS.Provider,{value:a},i)}function O2(){let{matches:a}=R.useContext(qe),i=a[a.length-1];return i?i.params:{}}function ql(a,{relative:i}={}){let{matches:s}=R.useContext(qe),{pathname:u}=xa(),c=JSON.stringify(Gy(s));return R.useMemo(()=>Xy(a,JSON.parse(c),u,i==="path"),[a,c,u,i])}function PS(a,i){return Py(a,i)}function Py(a,i,s,u){var L;Ot(Hl(),"useRoutes() may be used only in the context of a <Router> component.");let{navigator:c,static:f}=R.useContext($e),{matches:h}=R.useContext(qe),p=h[h.length-1],d=p?p.params:{},m=p?p.pathname:"/",g=p?p.pathnameBase:"/",S=p&&p.route;{let j=S&&S.path||"";Fy(m,!S||j.endsWith("*")||j.endsWith("*?"),`You rendered descendant <Routes> (or called \`useRoutes()\`) at "${m}" (under <Route path="${j}">) but the parent route path has no trailing "*". This means if you navigate deeper, the parent won't match anymore and therefore the child routes will never render.

Please change the parent <Route path="${j}"> to <Route path="${j==="/"?"*":`${j}/*`}">.`)}let b=xa(),x;if(i){let j=typeof i=="string"?yi(i):i;Ot(g==="/"||((L=j.pathname)==null?void 0:L.startsWith(g)),`When overriding the location using \`<Routes location>\` or \`useRoutes(routes, location)\`, the location pathname must begin with the portion of the URL pathname that was matched by all parent routes. The current pathname base is "${g}" but pathname "${j.pathname}" was given in the \`location\` prop.`),x=j}else x=b;let M=x.pathname||"/",z=M;if(g!=="/"){let j=g.replace(/^\//,"").split("/");z="/"+M.replace(/^\//,"").split("/").slice(j.length).join("/")}let V=!f&&s&&s.matches&&s.matches.length>0?s.matches:Hy(a,{pathname:z});Je(S||V!=null,`No routes matched location "${x.pathname}${x.search}${x.hash}" `),Je(V==null||V[V.length-1].route.element!==void 0||V[V.length-1].route.Component!==void 0||V[V.length-1].route.lazy!==void 0,`Matched leaf route at location "${x.pathname}${x.search}${x.hash}" does not have an element or Component. This means it will render an <Outlet /> with a null value by default resulting in an "empty" page.`);let O=IS(V&&V.map(j=>Object.assign({},j,{params:Object.assign({},d,j.params),pathname:yn([g,c.encodeLocation?c.encodeLocation(j.pathname).pathname:j.pathname]),pathnameBase:j.pathnameBase==="/"?g:yn([g,c.encodeLocation?c.encodeLocation(j.pathnameBase).pathname:j.pathnameBase])})),h,s,u);return i&&O?R.createElement(Nl.Provider,{value:{location:{pathname:"/",search:"",hash:"",state:null,key:"default",...x},navigationType:"POP"}},O):O}function FS(){let a=ib(),i=HS(a)?`${a.status} ${a.statusText}`:a instanceof Error?a.message:JSON.stringify(a),s=a instanceof Error?a.stack:null,u="rgba(200,200,200, 0.5)",c={padding:"0.5rem",backgroundColor:u},f={padding:"2px 4px",backgroundColor:u},h=null;return console.error("Error handled by React Router default ErrorBoundary:",a),h=R.createElement(R.Fragment,null,R.createElement("p",null,"💿 Hey developer 👋"),R.createElement("p",null,"You can provide a way better UX than this when your app throws errors by providing your own ",R.createElement("code",{style:f},"ErrorBoundary")," or"," ",R.createElement("code",{style:f},"errorElement")," prop on your route.")),R.createElement(R.Fragment,null,R.createElement("h2",null,"Unexpected Application Error!"),R.createElement("h3",{style:{fontStyle:"italic"}},i),s?R.createElement("pre",{style:c},s):null,h)}var JS=R.createElement(FS,null),$S=class extends R.Component{constructor(a){super(a),this.state={location:a.location,revalidation:a.revalidation,error:a.error}}static getDerivedStateFromError(a){return{error:a}}static getDerivedStateFromProps(a,i){return i.location!==a.location||i.revalidation!=="idle"&&a.revalidation==="idle"?{error:a.error,location:a.location,revalidation:a.revalidation}:{error:a.error!==void 0?a.error:i.error,location:i.location,revalidation:a.revalidation||i.revalidation}}componentDidCatch(a,i){console.error("React Router caught the following error during render",a,i)}render(){return this.state.error!==void 0?R.createElement(qe.Provider,{value:this.props.routeContext},R.createElement(wc.Provider,{value:this.state.error,children:this.props.component})):this.props.children}};function WS({routeContext:a,match:i,children:s}){let u=R.useContext(gi);return u&&u.static&&u.staticContext&&(i.route.errorElement||i.route.ErrorBoundary)&&(u.staticContext._deepestRenderedBoundaryId=i.route.id),R.createElement(qe.Provider,{value:a},s)}function IS(a,i=[],s=null,u=null){if(a==null){if(!s)return null;if(s.errors)a=s.matches;else if(i.length===0&&!s.initialized&&s.matches.length>0)a=s.matches;else return null}let c=a,f=s==null?void 0:s.errors;if(f!=null){let d=c.findIndex(m=>m.route.id&&(f==null?void 0:f[m.route.id])!==void 0);Ot(d>=0,`Could not find a matching route for errors on route IDs: ${Object.keys(f).join(",")}`),c=c.slice(0,Math.min(c.length,d+1))}let h=!1,p=-1;if(s)for(let d=0;d<c.length;d++){let m=c[d];if((m.route.HydrateFallback||m.route.hydrateFallbackElement)&&(p=d),m.route.id){let{loaderData:g,errors:S}=s,b=m.route.loader&&!g.hasOwnProperty(m.route.id)&&(!S||S[m.route.id]===void 0);if(m.route.lazy||b){h=!0,p>=0?c=c.slice(0,p+1):c=[c[0]];break}}}return c.reduceRight((d,m,g)=>{let S,b=!1,x=null,M=null;s&&(S=f&&m.route.id?f[m.route.id]:void 0,x=m.route.errorElement||JS,h&&(p<0&&g===0?(Fy("route-fallback",!1,"No `HydrateFallback` element provided to render during initial hydration"),b=!0,M=null):p===g&&(b=!0,M=m.route.hydrateFallbackElement||null)));let z=i.concat(c.slice(0,g+1)),V=()=>{let O;return S?O=x:b?O=M:m.route.Component?O=R.createElement(m.route.Component,null):m.route.element?O=m.route.element:O=d,R.createElement(WS,{match:m,routeContext:{outlet:d,matches:z,isDataRoute:s!=null},children:O})};return s&&(m.route.ErrorBoundary||m.route.errorElement||g===0)?R.createElement($S,{location:s.location,revalidation:s.revalidation,component:x,error:S,children:V(),routeContext:{outlet:null,matches:z,isDataRoute:!0}}):V()},null)}function _c(a){return`${a} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function tb(a){let i=R.useContext(gi);return Ot(i,_c(a)),i}function eb(a){let i=R.useContext(Eu);return Ot(i,_c(a)),i}function nb(a){let i=R.useContext(qe);return Ot(i,_c(a)),i}function Vc(a){let i=nb(a),s=i.matches[i.matches.length-1];return Ot(s.route.id,`${a} can only be used on routes that contain a unique "id"`),s.route.id}function ab(){return Vc("useRouteId")}function ib(){var u;let a=R.useContext(wc),i=eb("useRouteError"),s=Vc("useRouteError");return a!==void 0?a:(u=i.errors)==null?void 0:u[s]}function lb(){let{router:a}=tb("useNavigate"),i=Vc("useNavigate"),s=R.useRef(!1);return ky(()=>{s.current=!0}),R.useCallback(async(c,f={})=>{Je(s.current,Zy),s.current&&(typeof c=="number"?a.navigate(c):await a.navigate(c,{fromRouteId:i,...f}))},[a,i])}var vp={};function Fy(a,i,s){!i&&!vp[a]&&(vp[a]=!0,Je(!1,s))}R.memo(sb);function sb({routes:a,future:i,state:s}){return Py(a,void 0,s,i)}function ub(a){return kS(a.context)}function va(a){Ot(!1,"A <Route> is only ever to be used as the child of <Routes> element, never rendered directly. Please wrap your <Route> in a <Routes>.")}function rb({basename:a="/",children:i=null,location:s,navigationType:u="POP",navigator:c,static:f=!1}){Ot(!Hl(),"You cannot render a <Router> inside another <Router>. You should never have more than one in your app.");let h=a.replace(/^\/*/,"/"),p=R.useMemo(()=>({basename:h,navigator:c,static:f,future:{}}),[h,c,f]);typeof s=="string"&&(s=yi(s));let{pathname:d="/",search:m="",hash:g="",state:S=null,key:b="default"}=s,x=R.useMemo(()=>{let M=Qn(d,h);return M==null?null:{location:{pathname:M,search:m,hash:g,state:S,key:b},navigationType:u}},[h,d,m,g,S,b,u]);return Je(x!=null,`<Router basename="${h}"> is not able to match the URL "${d}${m}${g}" because it does not start with the basename, so the <Router> won't render anything.`),x==null?null:R.createElement($e.Provider,{value:p},R.createElement(Nl.Provider,{children:i,value:x}))}function ob({children:a,location:i}){return PS(fc(a),i)}function fc(a,i=[]){let s=[];return R.Children.forEach(a,(u,c)=>{if(!R.isValidElement(u))return;let f=[...i,c];if(u.type===R.Fragment){s.push.apply(s,fc(u.props.children,f));return}Ot(u.type===va,`[${typeof u.type=="string"?u.type:u.type.name}] is not a <Route> component. All component children of <Routes> must be a <Route> or <React.Fragment>`),Ot(!u.props.index||!u.props.children,"An index route cannot have child routes.");let h={id:u.props.id||f.join("-"),caseSensitive:u.props.caseSensitive,element:u.props.element,Component:u.props.Component,index:u.props.index,path:u.props.path,loader:u.props.loader,action:u.props.action,hydrateFallbackElement:u.props.hydrateFallbackElement,HydrateFallback:u.props.HydrateFallback,errorElement:u.props.errorElement,ErrorBoundary:u.props.ErrorBoundary,hasErrorBoundary:u.props.hasErrorBoundary===!0||u.props.ErrorBoundary!=null||u.props.errorElement!=null,shouldRevalidate:u.props.shouldRevalidate,handle:u.props.handle,lazy:u.props.lazy};u.props.children&&(h.children=fc(u.props.children,f)),s.push(h)}),s}var cu="get",fu="application/x-www-form-urlencoded";function Au(a){return a!=null&&typeof a.tagName=="string"}function cb(a){return Au(a)&&a.tagName.toLowerCase()==="button"}function fb(a){return Au(a)&&a.tagName.toLowerCase()==="form"}function hb(a){return Au(a)&&a.tagName.toLowerCase()==="input"}function db(a){return!!(a.metaKey||a.altKey||a.ctrlKey||a.shiftKey)}function mb(a,i){return a.button===0&&(!i||i==="_self")&&!db(a)}var lu=null;function pb(){if(lu===null)try{new FormData(document.createElement("form"),0),lu=!1}catch{lu=!0}return lu}var yb=new Set(["application/x-www-form-urlencoded","multipart/form-data","text/plain"]);function Jo(a){return a!=null&&!yb.has(a)?(Je(!1,`"${a}" is not a valid \`encType\` for \`<Form>\`/\`<fetcher.Form>\` and will default to "${fu}"`),null):a}function gb(a,i){let s,u,c,f,h;if(fb(a)){let p=a.getAttribute("action");u=p?Qn(p,i):null,s=a.getAttribute("method")||cu,c=Jo(a.getAttribute("enctype"))||fu,f=new FormData(a)}else if(cb(a)||hb(a)&&(a.type==="submit"||a.type==="image")){let p=a.form;if(p==null)throw new Error('Cannot submit a <button> or <input type="submit"> without a <form>');let d=a.getAttribute("formaction")||p.getAttribute("action");if(u=d?Qn(d,i):null,s=a.getAttribute("formmethod")||p.getAttribute("method")||cu,c=Jo(a.getAttribute("formenctype"))||Jo(p.getAttribute("enctype"))||fu,f=new FormData(p,a),!pb()){let{name:m,type:g,value:S}=a;if(g==="image"){let b=m?`${m}.`:"";f.append(`${b}x`,"0"),f.append(`${b}y`,"0")}else m&&f.append(m,S)}}else{if(Au(a))throw new Error('Cannot submit element that is not <form>, <button>, or <input type="submit|image">');s=cu,u=null,c=fu,h=a}return f&&c==="text/plain"&&(h=f,f=void 0),{action:u,method:s.toLowerCase(),encType:c,formData:f,body:h}}function zc(a,i){if(a===!1||a===null||typeof a>"u")throw new Error(i)}async function vb(a,i){if(a.id in i)return i[a.id];try{let s=await import(a.module);return i[a.id]=s,s}catch(s){return console.error(`Error loading route module \`${a.module}\`, reloading page...`),console.error(s),window.__reactRouterContext&&window.__reactRouterContext.isSpaMode,window.location.reload(),new Promise(()=>{})}}function Sb(a){return a==null?!1:a.href==null?a.rel==="preload"&&typeof a.imageSrcSet=="string"&&typeof a.imageSizes=="string":typeof a.rel=="string"&&typeof a.href=="string"}async function bb(a,i,s){let u=await Promise.all(a.map(async c=>{let f=i.routes[c.route.id];if(f){let h=await vb(f,s);return h.links?h.links():[]}return[]}));return Ab(u.flat(1).filter(Sb).filter(c=>c.rel==="stylesheet"||c.rel==="preload").map(c=>c.rel==="stylesheet"?{...c,rel:"prefetch",as:"style"}:{...c,rel:"prefetch"}))}function Sp(a,i,s,u,c,f){let h=(d,m)=>s[m]?d.route.id!==s[m].route.id:!0,p=(d,m)=>{var g;return s[m].pathname!==d.pathname||((g=s[m].route.path)==null?void 0:g.endsWith("*"))&&s[m].params["*"]!==d.params["*"]};return f==="assets"?i.filter((d,m)=>h(d,m)||p(d,m)):f==="data"?i.filter((d,m)=>{var S;let g=u.routes[d.route.id];if(!g||!g.hasLoader)return!1;if(h(d,m)||p(d,m))return!0;if(d.route.shouldRevalidate){let b=d.route.shouldRevalidate({currentUrl:new URL(c.pathname+c.search+c.hash,window.origin),currentParams:((S=s[0])==null?void 0:S.params)||{},nextUrl:new URL(a,window.origin),nextParams:d.params,defaultShouldRevalidate:!0});if(typeof b=="boolean")return b}return!0}):[]}function Tb(a,i){return xb(a.map(s=>{let u=i.routes[s.route.id];if(!u)return[];let c=[u.module];return u.imports&&(c=c.concat(u.imports)),c}).flat(1))}function xb(a){return[...new Set(a)]}function Eb(a){let i={},s=Object.keys(a).sort();for(let u of s)i[u]=a[u];return i}function Ab(a,i){let s=new Set;return new Set(i),a.reduce((u,c)=>{let f=JSON.stringify(Eb(c));return s.has(f)||(s.add(f),u.push({key:f,link:c})),u},[])}function Rb(a){let i=typeof a=="string"?new URL(a,typeof window>"u"?"server://singlefetch/":window.location.origin):a;return i.pathname==="/"?i.pathname="_root.data":i.pathname=`${i.pathname.replace(/\/$/,"")}.data`,i}function Db(){let a=R.useContext(gi);return zc(a,"You must render this element inside a <DataRouterContext.Provider> element"),a}function Mb(){let a=R.useContext(Eu);return zc(a,"You must render this element inside a <DataRouterStateContext.Provider> element"),a}var Uc=R.createContext(void 0);Uc.displayName="FrameworkContext";function Jy(){let a=R.useContext(Uc);return zc(a,"You must render this element inside a <HydratedRouter> element"),a}function Cb(a,i){let s=R.useContext(Uc),[u,c]=R.useState(!1),[f,h]=R.useState(!1),{onFocus:p,onBlur:d,onMouseEnter:m,onMouseLeave:g,onTouchStart:S}=i,b=R.useRef(null);R.useEffect(()=>{if(a==="render"&&h(!0),a==="viewport"){let z=O=>{O.forEach(L=>{h(L.isIntersecting)})},V=new IntersectionObserver(z,{threshold:.5});return b.current&&V.observe(b.current),()=>{V.disconnect()}}},[a]),R.useEffect(()=>{if(u){let z=setTimeout(()=>{h(!0)},100);return()=>{clearTimeout(z)}}},[u]);let x=()=>{c(!0)},M=()=>{c(!1),h(!1)};return s?a!=="intent"?[f,b,{}]:[f,b,{onFocus:bl(p,x),onBlur:bl(d,M),onMouseEnter:bl(m,x),onMouseLeave:bl(g,M),onTouchStart:bl(S,x)}]:[!1,b,{}]}function bl(a,i){return s=>{a&&a(s),s.defaultPrevented||i(s)}}function Ob({page:a,...i}){let{router:s}=Db(),u=R.useMemo(()=>Hy(s.routes,a,s.basename),[s.routes,a,s.basename]);return u?R.createElement(_b,{page:a,matches:u,...i}):null}function wb(a){let{manifest:i,routeModules:s}=Jy(),[u,c]=R.useState([]);return R.useEffect(()=>{let f=!1;return bb(a,i,s).then(h=>{f||c(h)}),()=>{f=!0}},[a,i,s]),u}function _b({page:a,matches:i,...s}){let u=xa(),{manifest:c,routeModules:f}=Jy(),{loaderData:h,matches:p}=Mb(),d=R.useMemo(()=>Sp(a,i,p,c,u,"data"),[a,i,p,c,u]),m=R.useMemo(()=>Sp(a,i,p,c,u,"assets"),[a,i,p,c,u]),g=R.useMemo(()=>{if(a===u.pathname+u.search+u.hash)return[];let x=new Set,M=!1;if(i.forEach(V=>{var L;let O=c.routes[V.route.id];!O||!O.hasLoader||(!d.some(j=>j.route.id===V.route.id)&&V.route.id in h&&((L=f[V.route.id])!=null&&L.shouldRevalidate)||O.hasClientLoader?M=!0:x.add(V.route.id))}),x.size===0)return[];let z=Rb(a);return M&&x.size>0&&z.searchParams.set("_routes",i.filter(V=>x.has(V.route.id)).map(V=>V.route.id).join(",")),[z.pathname+z.search]},[h,u,c,d,i,a,f]),S=R.useMemo(()=>Tb(m,c),[m,c]),b=wb(m);return R.createElement(R.Fragment,null,g.map(x=>R.createElement("link",{key:x,rel:"prefetch",as:"fetch",href:x,...s})),S.map(x=>R.createElement("link",{key:x,rel:"modulepreload",href:x,...s})),b.map(({key:x,link:M})=>R.createElement("link",{key:x,...M})))}function Vb(...a){return i=>{a.forEach(s=>{typeof s=="function"?s(i):s!=null&&(s.current=i)})}}var $y=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u";try{$y&&(window.__reactRouterVersion="7.1.5")}catch{}function zb({basename:a,children:i,window:s}){let u=R.useRef();u.current==null&&(u.current=gS({window:s,v5Compat:!0}));let c=u.current,[f,h]=R.useState({action:c.action,location:c.location}),p=R.useCallback(d=>{R.startTransition(()=>h(d))},[h]);return R.useLayoutEffect(()=>c.listen(p),[c,p]),R.createElement(rb,{basename:a,children:i,location:f.location,navigationType:f.action,navigator:c})}var Wy=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,Ru=R.forwardRef(function({onClick:i,discover:s="render",prefetch:u="none",relative:c,reloadDocument:f,replace:h,state:p,target:d,to:m,preventScrollReset:g,viewTransition:S,...b},x){let{basename:M}=R.useContext($e),z=typeof m=="string"&&Wy.test(m),V,O=!1;if(typeof m=="string"&&z&&(V=m,$y))try{let X=new URL(window.location.href),tt=m.startsWith("//")?new URL(X.protocol+m):new URL(m),wt=Qn(tt.pathname,M);tt.origin===X.origin&&wt!=null?m=wt+tt.search+tt.hash:O=!0}catch{Je(!1,`<Link to="${m}"> contains an invalid URL which will probably break when clicked - please update to a valid URL path.`)}let L=XS(m,{relative:c}),[j,P,H]=Cb(u,b),k=jb(m,{replace:h,state:p,target:d,preventScrollReset:g,relative:c,viewTransition:S});function st(X){i&&i(X),X.defaultPrevented||k(X)}let J=R.createElement("a",{...b,...H,href:V||L,onClick:O||f?i:st,ref:Vb(x,P),target:d,"data-discover":!z&&s==="render"?"true":void 0});return j&&!z?R.createElement(R.Fragment,null,J,R.createElement(Ob,{page:L})):J});Ru.displayName="Link";var Bc=R.forwardRef(function({"aria-current":i="page",caseSensitive:s=!1,className:u="",end:c=!1,style:f,to:h,viewTransition:p,children:d,...m},g){let S=ql(h,{relative:m.relative}),b=xa(),x=R.useContext(Eu),{navigator:M,basename:z}=R.useContext($e),V=x!=null&&Yb(S)&&p===!0,O=M.encodeLocation?M.encodeLocation(S).pathname:S.pathname,L=b.pathname,j=x&&x.navigation&&x.navigation.location?x.navigation.location.pathname:null;s||(L=L.toLowerCase(),j=j?j.toLowerCase():null,O=O.toLowerCase()),j&&z&&(j=Qn(j,z)||j);const P=O!=="/"&&O.endsWith("/")?O.length-1:O.length;let H=L===O||!c&&L.startsWith(O)&&L.charAt(P)==="/",k=j!=null&&(j===O||!c&&j.startsWith(O)&&j.charAt(O.length)==="/"),st={isActive:H,isPending:k,isTransitioning:V},J=H?i:void 0,X;typeof u=="function"?X=u(st):X=[u,H?"active":null,k?"pending":null,V?"transitioning":null].filter(Boolean).join(" ");let tt=typeof f=="function"?f(st):f;return R.createElement(Ru,{...m,"aria-current":J,className:X,ref:g,style:tt,to:h,viewTransition:p},typeof d=="function"?d(st):d)});Bc.displayName="NavLink";var Ub=R.forwardRef(({discover:a="render",fetcherKey:i,navigate:s,reloadDocument:u,replace:c,state:f,method:h=cu,action:p,onSubmit:d,relative:m,preventScrollReset:g,viewTransition:S,...b},x)=>{let M=Hb(),z=qb(p,{relative:m}),V=h.toLowerCase()==="get"?"get":"post",O=typeof p=="string"&&Wy.test(p),L=j=>{if(d&&d(j),j.defaultPrevented)return;j.preventDefault();let P=j.nativeEvent.submitter,H=(P==null?void 0:P.getAttribute("formmethod"))||h;M(P||j.currentTarget,{fetcherKey:i,method:H,navigate:s,replace:c,state:f,relative:m,preventScrollReset:g,viewTransition:S})};return R.createElement("form",{ref:x,method:V,action:z,onSubmit:u?d:L,...b,"data-discover":!O&&a==="render"?"true":void 0})});Ub.displayName="Form";function Bb(a){return`${a} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function Iy(a){let i=R.useContext(gi);return Ot(i,Bb(a)),i}function jb(a,{target:i,replace:s,state:u,preventScrollReset:c,relative:f,viewTransition:h}={}){let p=KS(),d=xa(),m=ql(a,{relative:f});return R.useCallback(g=>{if(mb(g,i)){g.preventDefault();let S=s!==void 0?s:Ol(d)===Ol(m);p(a,{replace:S,state:u,preventScrollReset:c,relative:f,viewTransition:h})}},[d,p,m,s,u,i,a,c,f,h])}var Lb=0,Nb=()=>`__${String(++Lb)}__`;function Hb(){let{router:a}=Iy("useSubmit"),{basename:i}=R.useContext($e),s=ab();return R.useCallback(async(u,c={})=>{let{action:f,method:h,encType:p,formData:d,body:m}=gb(u,i);if(c.navigate===!1){let g=c.fetcherKey||Nb();await a.fetch(g,s,c.action||f,{preventScrollReset:c.preventScrollReset,formData:d,body:m,formMethod:c.method||h,formEncType:c.encType||p,flushSync:c.flushSync})}else await a.navigate(c.action||f,{preventScrollReset:c.preventScrollReset,formData:d,body:m,formMethod:c.method||h,formEncType:c.encType||p,replace:c.replace,state:c.state,fromRouteId:s,flushSync:c.flushSync,viewTransition:c.viewTransition})},[a,i,s])}function qb(a,{relative:i}={}){let{basename:s}=R.useContext($e),u=R.useContext(qe);Ot(u,"useFormAction must be used inside a RouteContext");let[c]=u.matches.slice(-1),f={...ql(a||".",{relative:i})},h=xa();if(a==null){f.search=h.search;let p=new URLSearchParams(f.search),d=p.getAll("index");if(d.some(g=>g==="")){p.delete("index"),d.filter(S=>S).forEach(S=>p.append("index",S));let g=p.toString();f.search=g?`?${g}`:""}}return(!a||a===".")&&c.route.index&&(f.search=f.search?f.search.replace(/^\?/,"?index&"):"?index"),s!=="/"&&(f.pathname=f.pathname==="/"?s:yn([s,f.pathname])),Ol(f)}function Yb(a,i={}){let s=R.useContext(Qy);Ot(s!=null,"`useViewTransitionState` must be used within `react-router-dom`'s `RouterProvider`.  Did you accidentally import `RouterProvider` from `react-router`?");let{basename:u}=Iy("useViewTransitionState"),c=ql(a,{relative:i.relative});if(!s.isTransitioning)return!1;let f=Qn(s.currentLocation.pathname,u)||s.currentLocation.pathname,h=Qn(s.nextLocation.pathname,u)||s.nextLocation.pathname;return yu(c.pathname,h)!=null||yu(c.pathname,f)!=null}new TextEncoder;const tg=R.createContext(),Gb=({children:a})=>{const[i,s]=R.useState(0),u=c=>{s(c)};return Y.jsx(tg.Provider,{value:{navBarHeight:i,updateNavBarHeight:u},children:a})},jc=()=>R.useContext(tg),Lc=R.createContext({});function Nc(a){const i=R.useRef(null);return i.current===null&&(i.current=a()),i.current}const Du=R.createContext(null),Hc=R.createContext({transformPagePoint:a=>a,isStatic:!1,reducedMotion:"never"});class Xb extends R.Component{getSnapshotBeforeUpdate(i){const s=this.props.childRef.current;if(s&&i.isPresent&&!this.props.isPresent){const u=s.offsetParent,c=u instanceof HTMLElement&&u.offsetWidth||0,f=this.props.sizeRef.current;f.height=s.offsetHeight||0,f.width=s.offsetWidth||0,f.top=s.offsetTop,f.left=s.offsetLeft,f.right=c-f.width-f.left}return null}componentDidUpdate(){}render(){return this.props.children}}function Kb({children:a,isPresent:i,anchorX:s}){const u=R.useId(),c=R.useRef(null),f=R.useRef({width:0,height:0,top:0,left:0,right:0}),{nonce:h}=R.useContext(Hc);return R.useInsertionEffect(()=>{const{width:p,height:d,top:m,left:g,right:S}=f.current;if(i||!c.current||!p||!d)return;const b=s==="left"?`left: ${g}`:`right: ${S}`;c.current.dataset.motionPopId=u;const x=document.createElement("style");return h&&(x.nonce=h),document.head.appendChild(x),x.sheet&&x.sheet.insertRule(`
          [data-motion-pop-id="${u}"] {
            position: absolute !important;
            width: ${p}px !important;
            height: ${d}px !important;
            ${b}px !important;
            top: ${m}px !important;
          }
        `),()=>{document.head.removeChild(x)}},[i]),Y.jsx(Xb,{isPresent:i,childRef:c,sizeRef:f,children:R.cloneElement(a,{ref:c})})}const Qb=({children:a,initial:i,isPresent:s,onExitComplete:u,custom:c,presenceAffectsLayout:f,mode:h,anchorX:p})=>{const d=Nc(Zb),m=R.useId(),g=R.useCallback(b=>{d.set(b,!0);for(const x of d.values())if(!x)return;u&&u()},[d,u]),S=R.useMemo(()=>({id:m,initial:i,isPresent:s,custom:c,onExitComplete:g,register:b=>(d.set(b,!1),()=>d.delete(b))}),f?[Math.random(),g]:[s,g]);return R.useMemo(()=>{d.forEach((b,x)=>d.set(x,!1))},[s]),R.useEffect(()=>{!s&&!d.size&&u&&u()},[s]),h==="popLayout"&&(a=Y.jsx(Kb,{isPresent:s,anchorX:p,children:a})),Y.jsx(Du.Provider,{value:S,children:a})};function Zb(){return new Map}function eg(a=!0){const i=R.useContext(Du);if(i===null)return[!0,null];const{isPresent:s,onExitComplete:u,register:c}=i,f=R.useId();R.useEffect(()=>{a&&c(f)},[a]);const h=R.useCallback(()=>a&&u&&u(f),[f,u,a]);return!s&&u?[!1,h]:[!0]}const su=a=>a.key||"";function bp(a){const i=[];return R.Children.forEach(a,s=>{R.isValidElement(s)&&i.push(s)}),i}const qc=typeof window<"u",ng=qc?R.useLayoutEffect:R.useEffect,ag=({children:a,custom:i,initial:s=!0,onExitComplete:u,presenceAffectsLayout:c=!0,mode:f="sync",propagate:h=!1,anchorX:p="left"})=>{const[d,m]=eg(h),g=R.useMemo(()=>bp(a),[a]),S=h&&!d?[]:g.map(su),b=R.useRef(!0),x=R.useRef(g),M=Nc(()=>new Map),[z,V]=R.useState(g),[O,L]=R.useState(g);ng(()=>{b.current=!1,x.current=g;for(let H=0;H<O.length;H++){const k=su(O[H]);S.includes(k)?M.delete(k):M.get(k)!==!0&&M.set(k,!1)}},[O,S.length,S.join("-")]);const j=[];if(g!==z){let H=[...g];for(let k=0;k<O.length;k++){const st=O[k],J=su(st);S.includes(J)||(H.splice(k,0,st),j.push(st))}return f==="wait"&&j.length&&(H=j),L(bp(H)),V(g),null}const{forceRender:P}=R.useContext(Lc);return Y.jsx(Y.Fragment,{children:O.map(H=>{const k=su(H),st=h&&!d?!1:g===O||S.includes(k),J=()=>{if(M.has(k))M.set(k,!0);else return;let X=!0;M.forEach(tt=>{tt||(X=!1)}),X&&(P==null||P(),L(x.current),h&&(m==null||m()),u&&u())};return Y.jsx(Qb,{isPresent:st,initial:!b.current||s?void 0:!1,custom:i,presenceAffectsLayout:c,mode:f,onExitComplete:st?void 0:J,anchorX:p,children:H},k)})})},ge=a=>a;let ig=ge;function Yc(a){let i;return()=>(i===void 0&&(i=a()),i)}const di=(a,i,s)=>{const u=i-a;return u===0?1:(s-a)/u},gn=a=>a*1e3,vn=a=>a/1e3,kb={useManualTiming:!1},uu=["read","resolveKeyframes","update","preRender","render","postRender"],Tp={value:null};function Pb(a,i){let s=new Set,u=new Set,c=!1,f=!1;const h=new WeakSet;let p={delta:0,timestamp:0,isProcessing:!1},d=0;function m(S){h.has(S)&&(g.schedule(S),a()),d++,S(p)}const g={schedule:(S,b=!1,x=!1)=>{const z=x&&c?s:u;return b&&h.add(S),z.has(S)||z.add(S),S},cancel:S=>{u.delete(S),h.delete(S)},process:S=>{if(p=S,c){f=!0;return}c=!0,[s,u]=[u,s],s.forEach(m),i&&Tp.value&&Tp.value.frameloop[i].push(d),d=0,s.clear(),c=!1,f&&(f=!1,g.process(S))}};return g}const Fb=40;function lg(a,i){let s=!1,u=!0;const c={delta:0,timestamp:0,isProcessing:!1},f=()=>s=!0,h=uu.reduce((O,L)=>(O[L]=Pb(f,i?L:void 0),O),{}),{read:p,resolveKeyframes:d,update:m,preRender:g,render:S,postRender:b}=h,x=()=>{const O=performance.now();s=!1,c.delta=u?1e3/60:Math.max(Math.min(O-c.timestamp,Fb),1),c.timestamp=O,c.isProcessing=!0,p.process(c),d.process(c),m.process(c),g.process(c),S.process(c),b.process(c),c.isProcessing=!1,s&&i&&(u=!1,a(x))},M=()=>{s=!0,u=!0,c.isProcessing||a(x)};return{schedule:uu.reduce((O,L)=>{const j=h[L];return O[L]=(P,H=!1,k=!1)=>(s||M(),j.schedule(P,H,k)),O},{}),cancel:O=>{for(let L=0;L<uu.length;L++)h[uu[L]].cancel(O)},state:c,steps:h}}const{schedule:At,cancel:Zn,state:Jt,steps:$o}=lg(typeof requestAnimationFrame<"u"?requestAnimationFrame:ge,!0),sg=R.createContext({strict:!1}),xp={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},mi={};for(const a in xp)mi[a]={isEnabled:i=>xp[a].some(s=>!!i[s])};function Jb(a){for(const i in a)mi[i]={...mi[i],...a[i]}}const $b=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function gu(a){return a.startsWith("while")||a.startsWith("drag")&&a!=="draggable"||a.startsWith("layout")||a.startsWith("onTap")||a.startsWith("onPan")||a.startsWith("onLayout")||$b.has(a)}let ug=a=>!gu(a);function Wb(a){a&&(ug=i=>i.startsWith("on")?!gu(i):a(i))}try{Wb(require("@emotion/is-prop-valid").default)}catch{}function Ib(a,i,s){const u={};for(const c in a)c==="values"&&typeof a.values=="object"||(ug(c)||s===!0&&gu(c)||!i&&!gu(c)||a.draggable&&c.startsWith("onDrag"))&&(u[c]=a[c]);return u}function tT(a){if(typeof Proxy>"u")return a;const i=new Map,s=(...u)=>a(...u);return new Proxy(s,{get:(u,c)=>c==="create"?a:(i.has(c)||i.set(c,a(c)),i.get(c))})}const Mu=R.createContext({});function Cu(a){return a!==null&&typeof a=="object"&&typeof a.start=="function"}function wl(a){return typeof a=="string"||Array.isArray(a)}const Gc=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],Xc=["initial",...Gc];function Ou(a){return Cu(a.animate)||Xc.some(i=>wl(a[i]))}function rg(a){return!!(Ou(a)||a.variants)}function eT(a,i){if(Ou(a)){const{initial:s,animate:u}=a;return{initial:s===!1||wl(s)?s:void 0,animate:wl(u)?u:void 0}}return a.inherit!==!1?i:{}}function nT(a){const{initial:i,animate:s}=eT(a,R.useContext(Mu));return R.useMemo(()=>({initial:i,animate:s}),[Ep(i),Ep(s)])}function Ep(a){return Array.isArray(a)?a.join(" "):a}const aT=Symbol.for("motionComponentSymbol");function si(a){return a&&typeof a=="object"&&Object.prototype.hasOwnProperty.call(a,"current")}function iT(a,i,s){return R.useCallback(u=>{u&&a.onMount&&a.onMount(u),i&&(u?i.mount(u):i.unmount()),s&&(typeof s=="function"?s(u):si(s)&&(s.current=u))},[i])}const Kc=a=>a.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),lT="framerAppearId",og="data-"+Kc(lT),{schedule:Qc}=lg(queueMicrotask,!1),cg=R.createContext({});function sT(a,i,s,u,c){var f,h;const{visualElement:p}=R.useContext(Mu),d=R.useContext(sg),m=R.useContext(Du),g=R.useContext(Hc).reducedMotion,S=R.useRef(null);u=u||d.renderer,!S.current&&u&&(S.current=u(a,{visualState:i,parent:p,props:s,presenceContext:m,blockInitialAnimation:m?m.initial===!1:!1,reducedMotionConfig:g}));const b=S.current,x=R.useContext(cg);b&&!b.projection&&c&&(b.type==="html"||b.type==="svg")&&uT(S.current,s,c,x);const M=R.useRef(!1);R.useInsertionEffect(()=>{b&&M.current&&b.update(s,m)});const z=s[og],V=R.useRef(!!z&&!(!((f=window.MotionHandoffIsComplete)===null||f===void 0)&&f.call(window,z))&&((h=window.MotionHasOptimisedAnimation)===null||h===void 0?void 0:h.call(window,z)));return ng(()=>{b&&(M.current=!0,window.MotionIsMounted=!0,b.updateFeatures(),Qc.render(b.render),V.current&&b.animationState&&b.animationState.animateChanges())}),R.useEffect(()=>{b&&(!V.current&&b.animationState&&b.animationState.animateChanges(),V.current&&(queueMicrotask(()=>{var O;(O=window.MotionHandoffMarkAsComplete)===null||O===void 0||O.call(window,z)}),V.current=!1))}),b}function uT(a,i,s,u){const{layoutId:c,layout:f,drag:h,dragConstraints:p,layoutScroll:d,layoutRoot:m}=i;a.projection=new s(a.latestValues,i["data-framer-portal-id"]?void 0:fg(a.parent)),a.projection.setOptions({layoutId:c,layout:f,alwaysMeasureLayout:!!h||p&&si(p),visualElement:a,animationType:typeof f=="string"?f:"both",initialPromotionConfig:u,layoutScroll:d,layoutRoot:m})}function fg(a){if(a)return a.options.allowProjection!==!1?a.projection:fg(a.parent)}function rT({preloadedFeatures:a,createVisualElement:i,useRender:s,useVisualState:u,Component:c}){var f,h;a&&Jb(a);function p(m,g){let S;const b={...R.useContext(Hc),...m,layoutId:oT(m)},{isStatic:x}=b,M=nT(m),z=u(m,x);if(!x&&qc){cT();const V=fT(b);S=V.MeasureLayout,M.visualElement=sT(c,z,b,i,V.ProjectionNode)}return Y.jsxs(Mu.Provider,{value:M,children:[S&&M.visualElement?Y.jsx(S,{visualElement:M.visualElement,...b}):null,s(c,m,iT(z,M.visualElement,g),z,x,M.visualElement)]})}p.displayName=`motion.${typeof c=="string"?c:`create(${(h=(f=c.displayName)!==null&&f!==void 0?f:c.name)!==null&&h!==void 0?h:""})`}`;const d=R.forwardRef(p);return d[aT]=c,d}function oT({layoutId:a}){const i=R.useContext(Lc).id;return i&&a!==void 0?i+"-"+a:a}function cT(a,i){R.useContext(sg).strict}function fT(a){const{drag:i,layout:s}=mi;if(!i&&!s)return{};const u={...i,...s};return{MeasureLayout:i!=null&&i.isEnabled(a)||s!=null&&s.isEnabled(a)?u.MeasureLayout:void 0,ProjectionNode:u.ProjectionNode}}const hg=a=>i=>typeof i=="string"&&i.startsWith(a),Zc=hg("--"),hT=hg("var(--"),kc=a=>hT(a)?dT.test(a.split("/*")[0].trim()):!1,dT=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,_l={};function mT(a){for(const i in a)_l[i]=a[i],Zc(i)&&(_l[i].isCSSVariable=!0)}const vi=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],Ea=new Set(vi);function dg(a,{layout:i,layoutId:s}){return Ea.has(a)||a.startsWith("origin")||(i||s!==void 0)&&(!!_l[a]||a==="opacity")}const ee=a=>!!(a&&a.getVelocity),mg=(a,i)=>i&&typeof a=="number"?i.transform(a):a,Sn=(a,i,s)=>s>i?i:s<a?a:s,Si={test:a=>typeof a=="number",parse:parseFloat,transform:a=>a},Vl={...Si,transform:a=>Sn(0,1,a)},ru={...Si,default:1},Yl=a=>({test:i=>typeof i=="string"&&i.endsWith(a)&&i.split(" ").length===1,parse:parseFloat,transform:i=>`${i}${a}`}),Kn=Yl("deg"),Pe=Yl("%"),at=Yl("px"),pT=Yl("vh"),yT=Yl("vw"),Ap={...Pe,parse:a=>Pe.parse(a)/100,transform:a=>Pe.transform(a*100)},gT={borderWidth:at,borderTopWidth:at,borderRightWidth:at,borderBottomWidth:at,borderLeftWidth:at,borderRadius:at,radius:at,borderTopLeftRadius:at,borderTopRightRadius:at,borderBottomRightRadius:at,borderBottomLeftRadius:at,width:at,maxWidth:at,height:at,maxHeight:at,top:at,right:at,bottom:at,left:at,padding:at,paddingTop:at,paddingRight:at,paddingBottom:at,paddingLeft:at,margin:at,marginTop:at,marginRight:at,marginBottom:at,marginLeft:at,backgroundPositionX:at,backgroundPositionY:at},vT={rotate:Kn,rotateX:Kn,rotateY:Kn,rotateZ:Kn,scale:ru,scaleX:ru,scaleY:ru,scaleZ:ru,skew:Kn,skewX:Kn,skewY:Kn,distance:at,translateX:at,translateY:at,translateZ:at,x:at,y:at,z:at,perspective:at,transformPerspective:at,opacity:Vl,originX:Ap,originY:Ap,originZ:at},Rp={...Si,transform:Math.round},Pc={...gT,...vT,zIndex:Rp,size:at,fillOpacity:Vl,strokeOpacity:Vl,numOctaves:Rp},ST={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},bT=vi.length;function TT(a,i,s){let u="",c=!0;for(let f=0;f<bT;f++){const h=vi[f],p=a[h];if(p===void 0)continue;let d=!0;if(typeof p=="number"?d=p===(h.startsWith("scale")?1:0):d=parseFloat(p)===0,!d||s){const m=mg(p,Pc[h]);if(!d){c=!1;const g=ST[h]||h;u+=`${g}(${m}) `}s&&(i[h]=m)}}return u=u.trim(),s?u=s(i,c?"":u):c&&(u="none"),u}function Fc(a,i,s){const{style:u,vars:c,transformOrigin:f}=a;let h=!1,p=!1;for(const d in i){const m=i[d];if(Ea.has(d)){h=!0;continue}else if(Zc(d)){c[d]=m;continue}else{const g=mg(m,Pc[d]);d.startsWith("origin")?(p=!0,f[d]=g):u[d]=g}}if(i.transform||(h||s?u.transform=TT(i,a.transform,s):u.transform&&(u.transform="none")),p){const{originX:d="50%",originY:m="50%",originZ:g=0}=f;u.transformOrigin=`${d} ${m} ${g}`}}const Jc=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function pg(a,i,s){for(const u in i)!ee(i[u])&&!dg(u,s)&&(a[u]=i[u])}function xT({transformTemplate:a},i){return R.useMemo(()=>{const s=Jc();return Fc(s,i,a),Object.assign({},s.vars,s.style)},[i])}function ET(a,i){const s=a.style||{},u={};return pg(u,s,a),Object.assign(u,xT(a,i)),u}function AT(a,i){const s={},u=ET(a,i);return a.drag&&a.dragListener!==!1&&(s.draggable=!1,u.userSelect=u.WebkitUserSelect=u.WebkitTouchCallout="none",u.touchAction=a.drag===!0?"none":`pan-${a.drag==="x"?"y":"x"}`),a.tabIndex===void 0&&(a.onTap||a.onTapStart||a.whileTap)&&(s.tabIndex=0),s.style=u,s}const RT=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function $c(a){return typeof a!="string"||a.includes("-")?!1:!!(RT.indexOf(a)>-1||/[A-Z]/u.test(a))}const DT={offset:"stroke-dashoffset",array:"stroke-dasharray"},MT={offset:"strokeDashoffset",array:"strokeDasharray"};function CT(a,i,s=1,u=0,c=!0){a.pathLength=1;const f=c?DT:MT;a[f.offset]=at.transform(-u);const h=at.transform(i),p=at.transform(s);a[f.array]=`${h} ${p}`}function Dp(a,i,s){return typeof a=="string"?a:at.transform(i+s*a)}function OT(a,i,s){const u=Dp(i,a.x,a.width),c=Dp(s,a.y,a.height);return`${u} ${c}`}function Wc(a,{attrX:i,attrY:s,attrScale:u,originX:c,originY:f,pathLength:h,pathSpacing:p=1,pathOffset:d=0,...m},g,S){if(Fc(a,m,S),g){a.style.viewBox&&(a.attrs.viewBox=a.style.viewBox);return}a.attrs=a.style,a.style={};const{attrs:b,style:x,dimensions:M}=a;b.transform&&(M&&(x.transform=b.transform),delete b.transform),M&&(c!==void 0||f!==void 0||x.transform)&&(x.transformOrigin=OT(M,c!==void 0?c:.5,f!==void 0?f:.5)),i!==void 0&&(b.x=i),s!==void 0&&(b.y=s),u!==void 0&&(b.scale=u),h!==void 0&&CT(b,h,p,d,!1)}const yg=()=>({...Jc(),attrs:{}}),Ic=a=>typeof a=="string"&&a.toLowerCase()==="svg";function wT(a,i,s,u){const c=R.useMemo(()=>{const f=yg();return Wc(f,i,Ic(u),a.transformTemplate),{...f.attrs,style:{...f.style}}},[i]);if(a.style){const f={};pg(f,a.style,a),c.style={...f,...c.style}}return c}function _T(a=!1){return(s,u,c,{latestValues:f},h)=>{const d=($c(s)?wT:AT)(u,f,h,s),m=Ib(u,typeof s=="string",a),g=s!==R.Fragment?{...m,...d,ref:c}:{},{children:S}=u,b=R.useMemo(()=>ee(S)?S.get():S,[S]);return R.createElement(s,{...g,children:b})}}function Mp(a){const i=[{},{}];return a==null||a.values.forEach((s,u)=>{i[0][u]=s.get(),i[1][u]=s.getVelocity()}),i}function tf(a,i,s,u){if(typeof i=="function"){const[c,f]=Mp(u);i=i(s!==void 0?s:a.custom,c,f)}if(typeof i=="string"&&(i=a.variants&&a.variants[i]),typeof i=="function"){const[c,f]=Mp(u);i=i(s!==void 0?s:a.custom,c,f)}return i}const hc=a=>Array.isArray(a),VT=a=>!!(a&&typeof a=="object"&&a.mix&&a.toValue),zT=a=>hc(a)?a[a.length-1]||0:a;function hu(a){const i=ee(a)?a.get():a;return VT(i)?i.toValue():i}function UT({scrapeMotionValuesFromProps:a,createRenderState:i,onUpdate:s},u,c,f){const h={latestValues:BT(u,c,f,a),renderState:i()};return s&&(h.onMount=p=>s({props:u,current:p,...h}),h.onUpdate=p=>s(p)),h}const gg=a=>(i,s)=>{const u=R.useContext(Mu),c=R.useContext(Du),f=()=>UT(a,i,u,c);return s?f():Nc(f)};function BT(a,i,s,u){const c={},f=u(a,{});for(const b in f)c[b]=hu(f[b]);let{initial:h,animate:p}=a;const d=Ou(a),m=rg(a);i&&m&&!d&&a.inherit!==!1&&(h===void 0&&(h=i.initial),p===void 0&&(p=i.animate));let g=s?s.initial===!1:!1;g=g||h===!1;const S=g?p:h;if(S&&typeof S!="boolean"&&!Cu(S)){const b=Array.isArray(S)?S:[S];for(let x=0;x<b.length;x++){const M=tf(a,b[x]);if(M){const{transitionEnd:z,transition:V,...O}=M;for(const L in O){let j=O[L];if(Array.isArray(j)){const P=g?j.length-1:0;j=j[P]}j!==null&&(c[L]=j)}for(const L in z)c[L]=z[L]}}}return c}function ef(a,i,s){var u;const{style:c}=a,f={};for(const h in c)(ee(c[h])||i.style&&ee(i.style[h])||dg(h,a)||((u=s==null?void 0:s.getValue(h))===null||u===void 0?void 0:u.liveStyle)!==void 0)&&(f[h]=c[h]);return f}const jT={useVisualState:gg({scrapeMotionValuesFromProps:ef,createRenderState:Jc})};function vg(a,i){try{i.dimensions=typeof a.getBBox=="function"?a.getBBox():a.getBoundingClientRect()}catch{i.dimensions={x:0,y:0,width:0,height:0}}}function Sg(a,{style:i,vars:s},u,c){Object.assign(a.style,i,c&&c.getProjectionStyles(u));for(const f in s)a.style.setProperty(f,s[f])}const bg=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function Tg(a,i,s,u){Sg(a,i,void 0,u);for(const c in i.attrs)a.setAttribute(bg.has(c)?c:Kc(c),i.attrs[c])}function xg(a,i,s){const u=ef(a,i,s);for(const c in a)if(ee(a[c])||ee(i[c])){const f=vi.indexOf(c)!==-1?"attr"+c.charAt(0).toUpperCase()+c.substring(1):c;u[f]=a[c]}return u}const Cp=["x","y","width","height","cx","cy","r"],LT={useVisualState:gg({scrapeMotionValuesFromProps:xg,createRenderState:yg,onUpdate:({props:a,prevProps:i,current:s,renderState:u,latestValues:c})=>{if(!s)return;let f=!!a.drag;if(!f){for(const p in c)if(Ea.has(p)){f=!0;break}}if(!f)return;let h=!i;if(i)for(let p=0;p<Cp.length;p++){const d=Cp[p];a[d]!==i[d]&&(h=!0)}h&&At.read(()=>{vg(s,u),At.render(()=>{Wc(u,c,Ic(s.tagName),a.transformTemplate),Tg(s,u)})})}})};function NT(a,i){return function(u,{forwardMotionProps:c}={forwardMotionProps:!1}){const h={...$c(u)?LT:jT,preloadedFeatures:a,useRender:_T(c),createVisualElement:i,Component:u};return rT(h)}}function zl(a,i,s){const u=a.getProps();return tf(u,i,s!==void 0?s:u.custom,a)}const HT=Yc(()=>window.ScrollTimeline!==void 0);class qT{constructor(i){this.stop=()=>this.runAll("stop"),this.animations=i.filter(Boolean)}get finished(){return Promise.all(this.animations.map(i=>"finished"in i?i.finished:i))}getAll(i){return this.animations[0][i]}setAll(i,s){for(let u=0;u<this.animations.length;u++)this.animations[u][i]=s}attachTimeline(i,s){const u=this.animations.map(c=>{if(HT()&&c.attachTimeline)return c.attachTimeline(i);if(typeof s=="function")return s(c)});return()=>{u.forEach((c,f)=>{c&&c(),this.animations[f].stop()})}}get time(){return this.getAll("time")}set time(i){this.setAll("time",i)}get speed(){return this.getAll("speed")}set speed(i){this.setAll("speed",i)}get startTime(){return this.getAll("startTime")}get duration(){let i=0;for(let s=0;s<this.animations.length;s++)i=Math.max(i,this.animations[s].duration);return i}runAll(i){this.animations.forEach(s=>s[i]())}flatten(){this.runAll("flatten")}play(){this.runAll("play")}pause(){this.runAll("pause")}cancel(){this.runAll("cancel")}complete(){this.runAll("complete")}}class YT extends qT{then(i,s){return Promise.all(this.animations).then(i).catch(s)}}function nf(a,i){return a?a[i]||a.default||a:void 0}const dc=2e4;function Eg(a){let i=0;const s=50;let u=a.next(i);for(;!u.done&&i<dc;)i+=s,u=a.next(i);return i>=dc?1/0:i}function af(a){return typeof a=="function"}function Op(a,i){a.timeline=i,a.onfinish=null}const lf=a=>Array.isArray(a)&&typeof a[0]=="number",GT={linearEasing:void 0};function XT(a,i){const s=Yc(a);return()=>{var u;return(u=GT[i])!==null&&u!==void 0?u:s()}}const vu=XT(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch{return!1}return!0},"linearEasing"),Ag=(a,i,s=10)=>{let u="";const c=Math.max(Math.round(i/s),2);for(let f=0;f<c;f++)u+=a(di(0,c-1,f))+", ";return`linear(${u.substring(0,u.length-2)})`};function Rg(a){return!!(typeof a=="function"&&vu()||!a||typeof a=="string"&&(a in mc||vu())||lf(a)||Array.isArray(a)&&a.every(Rg))}const xl=([a,i,s,u])=>`cubic-bezier(${a}, ${i}, ${s}, ${u})`,mc={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:xl([0,.65,.55,1]),circOut:xl([.55,0,1,.45]),backIn:xl([.31,.01,.66,-.59]),backOut:xl([.33,1.53,.69,.99])};function Dg(a,i){if(a)return typeof a=="function"&&vu()?Ag(a,i):lf(a)?xl(a):Array.isArray(a)?a.map(s=>Dg(s,i)||mc.easeOut):mc[a]}const He={x:!1,y:!1};function Mg(){return He.x||He.y}function KT(a,i,s){var u;if(a instanceof Element)return[a];if(typeof a=="string"){let c=document;const f=(u=void 0)!==null&&u!==void 0?u:c.querySelectorAll(a);return f?Array.from(f):[]}return Array.from(a)}function Cg(a,i){const s=KT(a),u=new AbortController,c={passive:!0,...i,signal:u.signal};return[s,c,()=>u.abort()]}function wp(a){return!(a.pointerType==="touch"||Mg())}function QT(a,i,s={}){const[u,c,f]=Cg(a,s),h=p=>{if(!wp(p))return;const{target:d}=p,m=i(d,p);if(typeof m!="function"||!d)return;const g=S=>{wp(S)&&(m(S),d.removeEventListener("pointerleave",g))};d.addEventListener("pointerleave",g,c)};return u.forEach(p=>{p.addEventListener("pointerenter",h,c)}),f}const Og=(a,i)=>i?a===i?!0:Og(a,i.parentElement):!1,sf=a=>a.pointerType==="mouse"?typeof a.button!="number"||a.button<=0:a.isPrimary!==!1,ZT=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]);function kT(a){return ZT.has(a.tagName)||a.tabIndex!==-1}const El=new WeakSet;function _p(a){return i=>{i.key==="Enter"&&a(i)}}function Wo(a,i){a.dispatchEvent(new PointerEvent("pointer"+i,{isPrimary:!0,bubbles:!0}))}const PT=(a,i)=>{const s=a.currentTarget;if(!s)return;const u=_p(()=>{if(El.has(s))return;Wo(s,"down");const c=_p(()=>{Wo(s,"up")}),f=()=>Wo(s,"cancel");s.addEventListener("keyup",c,i),s.addEventListener("blur",f,i)});s.addEventListener("keydown",u,i),s.addEventListener("blur",()=>s.removeEventListener("keydown",u),i)};function Vp(a){return sf(a)&&!Mg()}function FT(a,i,s={}){const[u,c,f]=Cg(a,s),h=p=>{const d=p.currentTarget;if(!Vp(p)||El.has(d))return;El.add(d);const m=i(d,p),g=(x,M)=>{window.removeEventListener("pointerup",S),window.removeEventListener("pointercancel",b),!(!Vp(x)||!El.has(d))&&(El.delete(d),typeof m=="function"&&m(x,{success:M}))},S=x=>{g(x,s.useGlobalTarget||Og(d,x.target))},b=x=>{g(x,!1)};window.addEventListener("pointerup",S,c),window.addEventListener("pointercancel",b,c)};return u.forEach(p=>{!kT(p)&&p.getAttribute("tabindex")===null&&(p.tabIndex=0),(s.useGlobalTarget?window:p).addEventListener("pointerdown",h,c),p.addEventListener("focus",m=>PT(m,c),c)}),f}function JT(a){return a==="x"||a==="y"?He[a]?null:(He[a]=!0,()=>{He[a]=!1}):He.x||He.y?null:(He.x=He.y=!0,()=>{He.x=He.y=!1})}const wg=new Set(["width","height","top","left","right","bottom",...vi]);let du;function $T(){du=void 0}const Fe={now:()=>(du===void 0&&Fe.set(Jt.isProcessing||kb.useManualTiming?Jt.timestamp:performance.now()),du),set:a=>{du=a,queueMicrotask($T)}};function uf(a,i){a.indexOf(i)===-1&&a.push(i)}function rf(a,i){const s=a.indexOf(i);s>-1&&a.splice(s,1)}class of{constructor(){this.subscriptions=[]}add(i){return uf(this.subscriptions,i),()=>rf(this.subscriptions,i)}notify(i,s,u){const c=this.subscriptions.length;if(c)if(c===1)this.subscriptions[0](i,s,u);else for(let f=0;f<c;f++){const h=this.subscriptions[f];h&&h(i,s,u)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}function _g(a,i){return i?a*(1e3/i):0}const zp=30,WT=a=>!isNaN(parseFloat(a));class IT{constructor(i,s={}){this.version="12.4.3",this.canTrackVelocity=null,this.events={},this.updateAndNotify=(u,c=!0)=>{const f=Fe.now();this.updatedAt!==f&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(u),this.current!==this.prev&&this.events.change&&this.events.change.notify(this.current),c&&this.events.renderRequest&&this.events.renderRequest.notify(this.current)},this.hasAnimated=!1,this.setCurrent(i),this.owner=s.owner}setCurrent(i){this.current=i,this.updatedAt=Fe.now(),this.canTrackVelocity===null&&i!==void 0&&(this.canTrackVelocity=WT(this.current))}setPrevFrameValue(i=this.current){this.prevFrameValue=i,this.prevUpdatedAt=this.updatedAt}onChange(i){return this.on("change",i)}on(i,s){this.events[i]||(this.events[i]=new of);const u=this.events[i].add(s);return i==="change"?()=>{u(),At.read(()=>{this.events.change.getSize()||this.stop()})}:u}clearListeners(){for(const i in this.events)this.events[i].clear()}attach(i,s){this.passiveEffect=i,this.stopPassiveEffect=s}set(i,s=!0){!s||!this.passiveEffect?this.updateAndNotify(i,s):this.passiveEffect(i,this.updateAndNotify)}setWithVelocity(i,s,u){this.set(s),this.prev=void 0,this.prevFrameValue=i,this.prevUpdatedAt=this.updatedAt-u}jump(i,s=!0){this.updateAndNotify(i),this.prev=i,this.prevUpdatedAt=this.prevFrameValue=void 0,s&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}get(){return this.current}getPrevious(){return this.prev}getVelocity(){const i=Fe.now();if(!this.canTrackVelocity||this.prevFrameValue===void 0||i-this.updatedAt>zp)return 0;const s=Math.min(this.updatedAt-this.prevUpdatedAt,zp);return _g(parseFloat(this.current)-parseFloat(this.prevFrameValue),s)}start(i){return this.stop(),new Promise(s=>{this.hasAnimated=!0,this.animation=i(s),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function Ul(a,i){return new IT(a,i)}function tx(a,i,s){a.hasValue(i)?a.getValue(i).set(s):a.addValue(i,Ul(s))}function ex(a,i){const s=zl(a,i);let{transitionEnd:u={},transition:c={},...f}=s||{};f={...f,...u};for(const h in f){const p=zT(f[h]);tx(a,h,p)}}function nx(a){return!!(ee(a)&&a.add)}function pc(a,i){const s=a.getValue("willChange");if(nx(s))return s.add(i)}function Vg(a){return a.props[og]}const zg=(a,i,s)=>(((1-3*s+3*i)*a+(3*s-6*i))*a+3*i)*a,ax=1e-7,ix=12;function lx(a,i,s,u,c){let f,h,p=0;do h=i+(s-i)/2,f=zg(h,u,c)-a,f>0?s=h:i=h;while(Math.abs(f)>ax&&++p<ix);return h}function Gl(a,i,s,u){if(a===i&&s===u)return ge;const c=f=>lx(f,0,1,a,s);return f=>f===0||f===1?f:zg(c(f),i,u)}const Ug=a=>i=>i<=.5?a(2*i)/2:(2-a(2*(1-i)))/2,Bg=a=>i=>1-a(1-i),jg=Gl(.33,1.53,.69,.99),cf=Bg(jg),Lg=Ug(cf),Ng=a=>(a*=2)<1?.5*cf(a):.5*(2-Math.pow(2,-10*(a-1))),ff=a=>1-Math.sin(Math.acos(a)),Hg=Bg(ff),qg=Ug(ff),Yg=a=>/^0[^.\s]+$/u.test(a);function sx(a){return typeof a=="number"?a===0:a!==null?a==="none"||a==="0"||Yg(a):!0}const Al=a=>Math.round(a*1e5)/1e5,hf=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu;function ux(a){return a==null}const rx=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,df=(a,i)=>s=>!!(typeof s=="string"&&rx.test(s)&&s.startsWith(a)||i&&!ux(s)&&Object.prototype.hasOwnProperty.call(s,i)),Gg=(a,i,s)=>u=>{if(typeof u!="string")return u;const[c,f,h,p]=u.match(hf);return{[a]:parseFloat(c),[i]:parseFloat(f),[s]:parseFloat(h),alpha:p!==void 0?parseFloat(p):1}},ox=a=>Sn(0,255,a),Io={...Si,transform:a=>Math.round(ox(a))},ba={test:df("rgb","red"),parse:Gg("red","green","blue"),transform:({red:a,green:i,blue:s,alpha:u=1})=>"rgba("+Io.transform(a)+", "+Io.transform(i)+", "+Io.transform(s)+", "+Al(Vl.transform(u))+")"};function cx(a){let i="",s="",u="",c="";return a.length>5?(i=a.substring(1,3),s=a.substring(3,5),u=a.substring(5,7),c=a.substring(7,9)):(i=a.substring(1,2),s=a.substring(2,3),u=a.substring(3,4),c=a.substring(4,5),i+=i,s+=s,u+=u,c+=c),{red:parseInt(i,16),green:parseInt(s,16),blue:parseInt(u,16),alpha:c?parseInt(c,16)/255:1}}const yc={test:df("#"),parse:cx,transform:ba.transform},ui={test:df("hsl","hue"),parse:Gg("hue","saturation","lightness"),transform:({hue:a,saturation:i,lightness:s,alpha:u=1})=>"hsla("+Math.round(a)+", "+Pe.transform(Al(i))+", "+Pe.transform(Al(s))+", "+Al(Vl.transform(u))+")"},te={test:a=>ba.test(a)||yc.test(a)||ui.test(a),parse:a=>ba.test(a)?ba.parse(a):ui.test(a)?ui.parse(a):yc.parse(a),transform:a=>typeof a=="string"?a:a.hasOwnProperty("red")?ba.transform(a):ui.transform(a)},fx=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu;function hx(a){var i,s;return isNaN(a)&&typeof a=="string"&&(((i=a.match(hf))===null||i===void 0?void 0:i.length)||0)+(((s=a.match(fx))===null||s===void 0?void 0:s.length)||0)>0}const Xg="number",Kg="color",dx="var",mx="var(",Up="${}",px=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function Bl(a){const i=a.toString(),s=[],u={color:[],number:[],var:[]},c=[];let f=0;const p=i.replace(px,d=>(te.test(d)?(u.color.push(f),c.push(Kg),s.push(te.parse(d))):d.startsWith(mx)?(u.var.push(f),c.push(dx),s.push(d)):(u.number.push(f),c.push(Xg),s.push(parseFloat(d))),++f,Up)).split(Up);return{values:s,split:p,indexes:u,types:c}}function Qg(a){return Bl(a).values}function Zg(a){const{split:i,types:s}=Bl(a),u=i.length;return c=>{let f="";for(let h=0;h<u;h++)if(f+=i[h],c[h]!==void 0){const p=s[h];p===Xg?f+=Al(c[h]):p===Kg?f+=te.transform(c[h]):f+=c[h]}return f}}const yx=a=>typeof a=="number"?0:a;function gx(a){const i=Qg(a);return Zg(a)(i.map(yx))}const kn={test:hx,parse:Qg,createTransformer:Zg,getAnimatableNone:gx},vx=new Set(["brightness","contrast","saturate","opacity"]);function Sx(a){const[i,s]=a.slice(0,-1).split("(");if(i==="drop-shadow")return a;const[u]=s.match(hf)||[];if(!u)return a;const c=s.replace(u,"");let f=vx.has(i)?1:0;return u!==s&&(f*=100),i+"("+f+c+")"}const bx=/\b([a-z-]*)\(.*?\)/gu,gc={...kn,getAnimatableNone:a=>{const i=a.match(bx);return i?i.map(Sx).join(" "):a}},Tx={...Pc,color:te,backgroundColor:te,outlineColor:te,fill:te,stroke:te,borderColor:te,borderTopColor:te,borderRightColor:te,borderBottomColor:te,borderLeftColor:te,filter:gc,WebkitFilter:gc},mf=a=>Tx[a];function kg(a,i){let s=mf(a);return s!==gc&&(s=kn),s.getAnimatableNone?s.getAnimatableNone(i):void 0}const xx=new Set(["auto","none","0"]);function Ex(a,i,s){let u=0,c;for(;u<a.length&&!c;){const f=a[u];typeof f=="string"&&!xx.has(f)&&Bl(f).values.length&&(c=a[u]),u++}if(c&&s)for(const f of i)a[f]=kg(s,c)}const Bp=a=>a===Si||a===at,jp=(a,i)=>parseFloat(a.split(", ")[i]),Lp=(a,i)=>(s,{transform:u})=>{if(u==="none"||!u)return 0;const c=u.match(/^matrix3d\((.+)\)$/u);if(c)return jp(c[1],i);{const f=u.match(/^matrix\((.+)\)$/u);return f?jp(f[1],a):0}},Ax=new Set(["x","y","z"]),Rx=vi.filter(a=>!Ax.has(a));function Dx(a){const i=[];return Rx.forEach(s=>{const u=a.getValue(s);u!==void 0&&(i.push([s,u.get()]),u.set(s.startsWith("scale")?1:0))}),i}const pi={width:({x:a},{paddingLeft:i="0",paddingRight:s="0"})=>a.max-a.min-parseFloat(i)-parseFloat(s),height:({y:a},{paddingTop:i="0",paddingBottom:s="0"})=>a.max-a.min-parseFloat(i)-parseFloat(s),top:(a,{top:i})=>parseFloat(i),left:(a,{left:i})=>parseFloat(i),bottom:({y:a},{top:i})=>parseFloat(i)+(a.max-a.min),right:({x:a},{left:i})=>parseFloat(i)+(a.max-a.min),x:Lp(4,13),y:Lp(5,14)};pi.translateX=pi.x;pi.translateY=pi.y;const Ta=new Set;let vc=!1,Sc=!1;function Pg(){if(Sc){const a=Array.from(Ta).filter(u=>u.needsMeasurement),i=new Set(a.map(u=>u.element)),s=new Map;i.forEach(u=>{const c=Dx(u);c.length&&(s.set(u,c),u.render())}),a.forEach(u=>u.measureInitialState()),i.forEach(u=>{u.render();const c=s.get(u);c&&c.forEach(([f,h])=>{var p;(p=u.getValue(f))===null||p===void 0||p.set(h)})}),a.forEach(u=>u.measureEndState()),a.forEach(u=>{u.suspendedScrollY!==void 0&&window.scrollTo(0,u.suspendedScrollY)})}Sc=!1,vc=!1,Ta.forEach(a=>a.complete()),Ta.clear()}function Fg(){Ta.forEach(a=>{a.readKeyframes(),a.needsMeasurement&&(Sc=!0)})}function Mx(){Fg(),Pg()}class pf{constructor(i,s,u,c,f,h=!1){this.isComplete=!1,this.isAsync=!1,this.needsMeasurement=!1,this.isScheduled=!1,this.unresolvedKeyframes=[...i],this.onComplete=s,this.name=u,this.motionValue=c,this.element=f,this.isAsync=h}scheduleResolve(){this.isScheduled=!0,this.isAsync?(Ta.add(this),vc||(vc=!0,At.read(Fg),At.resolveKeyframes(Pg))):(this.readKeyframes(),this.complete())}readKeyframes(){const{unresolvedKeyframes:i,name:s,element:u,motionValue:c}=this;for(let f=0;f<i.length;f++)if(i[f]===null)if(f===0){const h=c==null?void 0:c.get(),p=i[i.length-1];if(h!==void 0)i[0]=h;else if(u&&s){const d=u.readValue(s,p);d!=null&&(i[0]=d)}i[0]===void 0&&(i[0]=p),c&&h===void 0&&c.set(i[0])}else i[f]=i[f-1]}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(){this.isComplete=!0,this.onComplete(this.unresolvedKeyframes,this.finalKeyframe),Ta.delete(this)}cancel(){this.isComplete||(this.isScheduled=!1,Ta.delete(this))}resume(){this.isComplete||this.scheduleResolve()}}const Jg=a=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(a),Cx=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u;function Ox(a){const i=Cx.exec(a);if(!i)return[,];const[,s,u,c]=i;return[`--${s??u}`,c]}function $g(a,i,s=1){const[u,c]=Ox(a);if(!u)return;const f=window.getComputedStyle(i).getPropertyValue(u);if(f){const h=f.trim();return Jg(h)?parseFloat(h):h}return kc(c)?$g(c,i,s+1):c}const Wg=a=>i=>i.test(a),wx={test:a=>a==="auto",parse:a=>a},Ig=[Si,at,Pe,Kn,yT,pT,wx],Np=a=>Ig.find(Wg(a));class tv extends pf{constructor(i,s,u,c,f){super(i,s,u,c,f,!0)}readKeyframes(){const{unresolvedKeyframes:i,element:s,name:u}=this;if(!s||!s.current)return;super.readKeyframes();for(let d=0;d<i.length;d++){let m=i[d];if(typeof m=="string"&&(m=m.trim(),kc(m))){const g=$g(m,s.current);g!==void 0&&(i[d]=g),d===i.length-1&&(this.finalKeyframe=m)}}if(this.resolveNoneKeyframes(),!wg.has(u)||i.length!==2)return;const[c,f]=i,h=Np(c),p=Np(f);if(h!==p)if(Bp(h)&&Bp(p))for(let d=0;d<i.length;d++){const m=i[d];typeof m=="string"&&(i[d]=parseFloat(m))}else this.needsMeasurement=!0}resolveNoneKeyframes(){const{unresolvedKeyframes:i,name:s}=this,u=[];for(let c=0;c<i.length;c++)sx(i[c])&&u.push(c);u.length&&Ex(i,u,s)}measureInitialState(){const{element:i,unresolvedKeyframes:s,name:u}=this;if(!i||!i.current)return;u==="height"&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=pi[u](i.measureViewportBox(),window.getComputedStyle(i.current)),s[0]=this.measuredOrigin;const c=s[s.length-1];c!==void 0&&i.getValue(u,c).jump(c,!1)}measureEndState(){var i;const{element:s,name:u,unresolvedKeyframes:c}=this;if(!s||!s.current)return;const f=s.getValue(u);f&&f.jump(this.measuredOrigin,!1);const h=c.length-1,p=c[h];c[h]=pi[u](s.measureViewportBox(),window.getComputedStyle(s.current)),p!==null&&this.finalKeyframe===void 0&&(this.finalKeyframe=p),!((i=this.removedTransforms)===null||i===void 0)&&i.length&&this.removedTransforms.forEach(([d,m])=>{s.getValue(d).set(m)}),this.resolveNoneKeyframes()}}const Hp=(a,i)=>i==="zIndex"?!1:!!(typeof a=="number"||Array.isArray(a)||typeof a=="string"&&(kn.test(a)||a==="0")&&!a.startsWith("url("));function _x(a){const i=a[0];if(a.length===1)return!0;for(let s=0;s<a.length;s++)if(a[s]!==i)return!0}function Vx(a,i,s,u){const c=a[0];if(c===null)return!1;if(i==="display"||i==="visibility")return!0;const f=a[a.length-1],h=Hp(c,i),p=Hp(f,i);return!h||!p?!1:_x(a)||(s==="spring"||af(s))&&u}const zx=a=>a!==null;function wu(a,{repeat:i,repeatType:s="loop"},u){const c=a.filter(zx),f=i&&s!=="loop"&&i%2===1?0:c.length-1;return!f||u===void 0?c[f]:u}const Ux=40;class ev{constructor({autoplay:i=!0,delay:s=0,type:u="keyframes",repeat:c=0,repeatDelay:f=0,repeatType:h="loop",...p}){this.isStopped=!1,this.hasAttemptedResolve=!1,this.createdAt=Fe.now(),this.options={autoplay:i,delay:s,type:u,repeat:c,repeatDelay:f,repeatType:h,...p},this.updateFinishedPromise()}calcStartTime(){return this.resolvedAt?this.resolvedAt-this.createdAt>Ux?this.resolvedAt:this.createdAt:this.createdAt}get resolved(){return!this._resolved&&!this.hasAttemptedResolve&&Mx(),this._resolved}onKeyframesResolved(i,s){this.resolvedAt=Fe.now(),this.hasAttemptedResolve=!0;const{name:u,type:c,velocity:f,delay:h,onComplete:p,onUpdate:d,isGenerator:m}=this.options;if(!m&&!Vx(i,u,c,f))if(h)this.options.duration=0;else{d&&d(wu(i,this.options,s)),p&&p(),this.resolveFinishedPromise();return}const g=this.initPlayback(i,s);g!==!1&&(this._resolved={keyframes:i,finalKeyframe:s,...g},this.onPostResolved())}onPostResolved(){}then(i,s){return this.currentFinishedPromise.then(i,s)}flatten(){this.options.type="keyframes",this.options.ease="linear"}updateFinishedPromise(){this.currentFinishedPromise=new Promise(i=>{this.resolveFinishedPromise=i})}}const Ct=(a,i,s)=>a+(i-a)*s;function tc(a,i,s){return s<0&&(s+=1),s>1&&(s-=1),s<1/6?a+(i-a)*6*s:s<1/2?i:s<2/3?a+(i-a)*(2/3-s)*6:a}function Bx({hue:a,saturation:i,lightness:s,alpha:u}){a/=360,i/=100,s/=100;let c=0,f=0,h=0;if(!i)c=f=h=s;else{const p=s<.5?s*(1+i):s+i-s*i,d=2*s-p;c=tc(d,p,a+1/3),f=tc(d,p,a),h=tc(d,p,a-1/3)}return{red:Math.round(c*255),green:Math.round(f*255),blue:Math.round(h*255),alpha:u}}function Su(a,i){return s=>s>0?i:a}const ec=(a,i,s)=>{const u=a*a,c=s*(i*i-u)+u;return c<0?0:Math.sqrt(c)},jx=[yc,ba,ui],Lx=a=>jx.find(i=>i.test(a));function qp(a){const i=Lx(a);if(!i)return!1;let s=i.parse(a);return i===ui&&(s=Bx(s)),s}const Yp=(a,i)=>{const s=qp(a),u=qp(i);if(!s||!u)return Su(a,i);const c={...s};return f=>(c.red=ec(s.red,u.red,f),c.green=ec(s.green,u.green,f),c.blue=ec(s.blue,u.blue,f),c.alpha=Ct(s.alpha,u.alpha,f),ba.transform(c))},Nx=(a,i)=>s=>i(a(s)),Xl=(...a)=>a.reduce(Nx),bc=new Set(["none","hidden"]);function Hx(a,i){return bc.has(a)?s=>s<=0?a:i:s=>s>=1?i:a}function qx(a,i){return s=>Ct(a,i,s)}function yf(a){return typeof a=="number"?qx:typeof a=="string"?kc(a)?Su:te.test(a)?Yp:Xx:Array.isArray(a)?nv:typeof a=="object"?te.test(a)?Yp:Yx:Su}function nv(a,i){const s=[...a],u=s.length,c=a.map((f,h)=>yf(f)(f,i[h]));return f=>{for(let h=0;h<u;h++)s[h]=c[h](f);return s}}function Yx(a,i){const s={...a,...i},u={};for(const c in s)a[c]!==void 0&&i[c]!==void 0&&(u[c]=yf(a[c])(a[c],i[c]));return c=>{for(const f in u)s[f]=u[f](c);return s}}function Gx(a,i){var s;const u=[],c={color:0,var:0,number:0};for(let f=0;f<i.values.length;f++){const h=i.types[f],p=a.indexes[h][c[h]],d=(s=a.values[p])!==null&&s!==void 0?s:0;u[f]=d,c[h]++}return u}const Xx=(a,i)=>{const s=kn.createTransformer(i),u=Bl(a),c=Bl(i);return u.indexes.var.length===c.indexes.var.length&&u.indexes.color.length===c.indexes.color.length&&u.indexes.number.length>=c.indexes.number.length?bc.has(a)&&!c.values.length||bc.has(i)&&!u.values.length?Hx(a,i):Xl(nv(Gx(u,c),c.values),s):Su(a,i)};function av(a,i,s){return typeof a=="number"&&typeof i=="number"&&typeof s=="number"?Ct(a,i,s):yf(a)(a,i)}const Kx=5;function iv(a,i,s){const u=Math.max(i-Kx,0);return _g(s-a(u),i-u)}const Ut={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1},Gp=.001;function Qx({duration:a=Ut.duration,bounce:i=Ut.bounce,velocity:s=Ut.velocity,mass:u=Ut.mass}){let c,f,h=1-i;h=Sn(Ut.minDamping,Ut.maxDamping,h),a=Sn(Ut.minDuration,Ut.maxDuration,vn(a)),h<1?(c=m=>{const g=m*h,S=g*a,b=g-s,x=Tc(m,h),M=Math.exp(-S);return Gp-b/x*M},f=m=>{const S=m*h*a,b=S*s+s,x=Math.pow(h,2)*Math.pow(m,2)*a,M=Math.exp(-S),z=Tc(Math.pow(m,2),h);return(-c(m)+Gp>0?-1:1)*((b-x)*M)/z}):(c=m=>{const g=Math.exp(-m*a),S=(m-s)*a+1;return-.001+g*S},f=m=>{const g=Math.exp(-m*a),S=(s-m)*(a*a);return g*S});const p=5/a,d=kx(c,f,p);if(a=gn(a),isNaN(d))return{stiffness:Ut.stiffness,damping:Ut.damping,duration:a};{const m=Math.pow(d,2)*u;return{stiffness:m,damping:h*2*Math.sqrt(u*m),duration:a}}}const Zx=12;function kx(a,i,s){let u=s;for(let c=1;c<Zx;c++)u=u-a(u)/i(u);return u}function Tc(a,i){return a*Math.sqrt(1-i*i)}const Px=["duration","bounce"],Fx=["stiffness","damping","mass"];function Xp(a,i){return i.some(s=>a[s]!==void 0)}function Jx(a){let i={velocity:Ut.velocity,stiffness:Ut.stiffness,damping:Ut.damping,mass:Ut.mass,isResolvedFromDuration:!1,...a};if(!Xp(a,Fx)&&Xp(a,Px))if(a.visualDuration){const s=a.visualDuration,u=2*Math.PI/(s*1.2),c=u*u,f=2*Sn(.05,1,1-(a.bounce||0))*Math.sqrt(c);i={...i,mass:Ut.mass,stiffness:c,damping:f}}else{const s=Qx(a);i={...i,...s,mass:Ut.mass},i.isResolvedFromDuration=!0}return i}function lv(a=Ut.visualDuration,i=Ut.bounce){const s=typeof a!="object"?{visualDuration:a,keyframes:[0,1],bounce:i}:a;let{restSpeed:u,restDelta:c}=s;const f=s.keyframes[0],h=s.keyframes[s.keyframes.length-1],p={done:!1,value:f},{stiffness:d,damping:m,mass:g,duration:S,velocity:b,isResolvedFromDuration:x}=Jx({...s,velocity:-vn(s.velocity||0)}),M=b||0,z=m/(2*Math.sqrt(d*g)),V=h-f,O=vn(Math.sqrt(d/g)),L=Math.abs(V)<5;u||(u=L?Ut.restSpeed.granular:Ut.restSpeed.default),c||(c=L?Ut.restDelta.granular:Ut.restDelta.default);let j;if(z<1){const H=Tc(O,z);j=k=>{const st=Math.exp(-z*O*k);return h-st*((M+z*O*V)/H*Math.sin(H*k)+V*Math.cos(H*k))}}else if(z===1)j=H=>h-Math.exp(-O*H)*(V+(M+O*V)*H);else{const H=O*Math.sqrt(z*z-1);j=k=>{const st=Math.exp(-z*O*k),J=Math.min(H*k,300);return h-st*((M+z*O*V)*Math.sinh(J)+H*V*Math.cosh(J))/H}}const P={calculatedDuration:x&&S||null,next:H=>{const k=j(H);if(x)p.done=H>=S;else{let st=0;z<1&&(st=H===0?gn(M):iv(j,H,k));const J=Math.abs(st)<=u,X=Math.abs(h-k)<=c;p.done=J&&X}return p.value=p.done?h:k,p},toString:()=>{const H=Math.min(Eg(P),dc),k=Ag(st=>P.next(H*st).value,H,30);return H+"ms "+k}};return P}function Kp({keyframes:a,velocity:i=0,power:s=.8,timeConstant:u=325,bounceDamping:c=10,bounceStiffness:f=500,modifyTarget:h,min:p,max:d,restDelta:m=.5,restSpeed:g}){const S=a[0],b={done:!1,value:S},x=J=>p!==void 0&&J<p||d!==void 0&&J>d,M=J=>p===void 0?d:d===void 0||Math.abs(p-J)<Math.abs(d-J)?p:d;let z=s*i;const V=S+z,O=h===void 0?V:h(V);O!==V&&(z=O-S);const L=J=>-z*Math.exp(-J/u),j=J=>O+L(J),P=J=>{const X=L(J),tt=j(J);b.done=Math.abs(X)<=m,b.value=b.done?O:tt};let H,k;const st=J=>{x(b.value)&&(H=J,k=lv({keyframes:[b.value,M(b.value)],velocity:iv(j,J,b.value),damping:c,stiffness:f,restDelta:m,restSpeed:g}))};return st(0),{calculatedDuration:null,next:J=>{let X=!1;return!k&&H===void 0&&(X=!0,P(J),st(J)),H!==void 0&&J>=H?k.next(J-H):(!X&&P(J),b)}}}const $x=Gl(.42,0,1,1),Wx=Gl(0,0,.58,1),sv=Gl(.42,0,.58,1),Ix=a=>Array.isArray(a)&&typeof a[0]!="number",tE={linear:ge,easeIn:$x,easeInOut:sv,easeOut:Wx,circIn:ff,circInOut:qg,circOut:Hg,backIn:cf,backInOut:Lg,backOut:jg,anticipate:Ng},Qp=a=>{if(lf(a)){ig(a.length===4);const[i,s,u,c]=a;return Gl(i,s,u,c)}else if(typeof a=="string")return tE[a];return a};function eE(a,i,s){const u=[],c=s||av,f=a.length-1;for(let h=0;h<f;h++){let p=c(a[h],a[h+1]);if(i){const d=Array.isArray(i)?i[h]||ge:i;p=Xl(d,p)}u.push(p)}return u}function nE(a,i,{clamp:s=!0,ease:u,mixer:c}={}){const f=a.length;if(ig(f===i.length),f===1)return()=>i[0];if(f===2&&i[0]===i[1])return()=>i[1];const h=a[0]===a[1];a[0]>a[f-1]&&(a=[...a].reverse(),i=[...i].reverse());const p=eE(i,u,c),d=p.length,m=g=>{if(h&&g<a[0])return i[0];let S=0;if(d>1)for(;S<a.length-2&&!(g<a[S+1]);S++);const b=di(a[S],a[S+1],g);return p[S](b)};return s?g=>m(Sn(a[0],a[f-1],g)):m}function aE(a,i){const s=a[a.length-1];for(let u=1;u<=i;u++){const c=di(0,i,u);a.push(Ct(s,1,c))}}function iE(a){const i=[0];return aE(i,a.length-1),i}function lE(a,i){return a.map(s=>s*i)}function sE(a,i){return a.map(()=>i||sv).splice(0,a.length-1)}function bu({duration:a=300,keyframes:i,times:s,ease:u="easeInOut"}){const c=Ix(u)?u.map(Qp):Qp(u),f={done:!1,value:i[0]},h=lE(s&&s.length===i.length?s:iE(i),a),p=nE(h,i,{ease:Array.isArray(c)?c:sE(i,c)});return{calculatedDuration:a,next:d=>(f.value=p(d),f.done=d>=a,f)}}const uE=a=>{const i=({timestamp:s})=>a(s);return{start:()=>At.update(i,!0),stop:()=>Zn(i),now:()=>Jt.isProcessing?Jt.timestamp:Fe.now()}},rE={decay:Kp,inertia:Kp,tween:bu,keyframes:bu,spring:lv},oE=a=>a/100;class gf extends ev{constructor(i){super(i),this.holdTime=null,this.cancelTime=null,this.currentTime=0,this.playbackSpeed=1,this.pendingPlayState="running",this.startTime=null,this.state="idle",this.stop=()=>{if(this.resolver.cancel(),this.isStopped=!0,this.state==="idle")return;this.teardown();const{onStop:d}=this.options;d&&d()};const{name:s,motionValue:u,element:c,keyframes:f}=this.options,h=(c==null?void 0:c.KeyframeResolver)||pf,p=(d,m)=>this.onKeyframesResolved(d,m);this.resolver=new h(f,p,s,u,c),this.resolver.scheduleResolve()}flatten(){super.flatten(),this._resolved&&Object.assign(this._resolved,this.initPlayback(this._resolved.keyframes))}initPlayback(i){const{type:s="keyframes",repeat:u=0,repeatDelay:c=0,repeatType:f,velocity:h=0}=this.options,p=af(s)?s:rE[s]||bu;let d,m;p!==bu&&typeof i[0]!="number"&&(d=Xl(oE,av(i[0],i[1])),i=[0,100]);const g=p({...this.options,keyframes:i});f==="mirror"&&(m=p({...this.options,keyframes:[...i].reverse(),velocity:-h})),g.calculatedDuration===null&&(g.calculatedDuration=Eg(g));const{calculatedDuration:S}=g,b=S+c,x=b*(u+1)-c;return{generator:g,mirroredGenerator:m,mapPercentToKeyframes:d,calculatedDuration:S,resolvedDuration:b,totalDuration:x}}onPostResolved(){const{autoplay:i=!0}=this.options;this.play(),this.pendingPlayState==="paused"||!i?this.pause():this.state=this.pendingPlayState}tick(i,s=!1){const{resolved:u}=this;if(!u){const{keyframes:J}=this.options;return{done:!0,value:J[J.length-1]}}const{finalKeyframe:c,generator:f,mirroredGenerator:h,mapPercentToKeyframes:p,keyframes:d,calculatedDuration:m,totalDuration:g,resolvedDuration:S}=u;if(this.startTime===null)return f.next(0);const{delay:b,repeat:x,repeatType:M,repeatDelay:z,onUpdate:V}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,i):this.speed<0&&(this.startTime=Math.min(i-g/this.speed,this.startTime)),s?this.currentTime=i:this.holdTime!==null?this.currentTime=this.holdTime:this.currentTime=Math.round(i-this.startTime)*this.speed;const O=this.currentTime-b*(this.speed>=0?1:-1),L=this.speed>=0?O<0:O>g;this.currentTime=Math.max(O,0),this.state==="finished"&&this.holdTime===null&&(this.currentTime=g);let j=this.currentTime,P=f;if(x){const J=Math.min(this.currentTime,g)/S;let X=Math.floor(J),tt=J%1;!tt&&J>=1&&(tt=1),tt===1&&X--,X=Math.min(X,x+1),!!(X%2)&&(M==="reverse"?(tt=1-tt,z&&(tt-=z/S)):M==="mirror"&&(P=h)),j=Sn(0,1,tt)*S}const H=L?{done:!1,value:d[0]}:P.next(j);p&&(H.value=p(H.value));let{done:k}=H;!L&&m!==null&&(k=this.speed>=0?this.currentTime>=g:this.currentTime<=0);const st=this.holdTime===null&&(this.state==="finished"||this.state==="running"&&k);return st&&c!==void 0&&(H.value=wu(d,this.options,c)),V&&V(H.value),st&&this.finish(),H}get duration(){const{resolved:i}=this;return i?vn(i.calculatedDuration):0}get time(){return vn(this.currentTime)}set time(i){i=gn(i),this.currentTime=i,this.holdTime!==null||this.speed===0?this.holdTime=i:this.driver&&(this.startTime=this.driver.now()-i/this.speed)}get speed(){return this.playbackSpeed}set speed(i){const s=this.playbackSpeed!==i;this.playbackSpeed=i,s&&(this.time=vn(this.currentTime))}play(){if(this.resolver.isScheduled||this.resolver.resume(),!this._resolved){this.pendingPlayState="running";return}if(this.isStopped)return;const{driver:i=uE,onPlay:s,startTime:u}=this.options;this.driver||(this.driver=i(f=>this.tick(f))),s&&s();const c=this.driver.now();this.holdTime!==null?this.startTime=c-this.holdTime:this.startTime?this.state==="finished"&&(this.startTime=c):this.startTime=u??this.calcStartTime(),this.state==="finished"&&this.updateFinishedPromise(),this.cancelTime=this.startTime,this.holdTime=null,this.state="running",this.driver.start()}pause(){var i;if(!this._resolved){this.pendingPlayState="paused";return}this.state="paused",this.holdTime=(i=this.currentTime)!==null&&i!==void 0?i:0}complete(){this.state!=="running"&&this.play(),this.pendingPlayState=this.state="finished",this.holdTime=null}finish(){this.teardown(),this.state="finished";const{onComplete:i}=this.options;i&&i()}cancel(){this.cancelTime!==null&&this.tick(this.cancelTime),this.teardown(),this.updateFinishedPromise()}teardown(){this.state="idle",this.stopDriver(),this.resolveFinishedPromise(),this.updateFinishedPromise(),this.startTime=this.cancelTime=null,this.resolver.cancel()}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(i){return this.startTime=0,this.tick(i,!0)}}const cE=new Set(["opacity","clipPath","filter","transform"]);function fE(a,i,s,{delay:u=0,duration:c=300,repeat:f=0,repeatType:h="loop",ease:p="easeInOut",times:d}={}){const m={[i]:s};d&&(m.offset=d);const g=Dg(p,c);return Array.isArray(g)&&(m.easing=g),a.animate(m,{delay:u,duration:c,easing:Array.isArray(g)?"linear":g,fill:"both",iterations:f+1,direction:h==="reverse"?"alternate":"normal"})}const hE=Yc(()=>Object.hasOwnProperty.call(Element.prototype,"animate")),Tu=10,dE=2e4;function mE(a){return af(a.type)||a.type==="spring"||!Rg(a.ease)}function pE(a,i){const s=new gf({...i,keyframes:a,repeat:0,delay:0,isGenerator:!0});let u={done:!1,value:a[0]};const c=[];let f=0;for(;!u.done&&f<dE;)u=s.sample(f),c.push(u.value),f+=Tu;return{times:void 0,keyframes:c,duration:f-Tu,ease:"linear"}}const uv={anticipate:Ng,backInOut:Lg,circInOut:qg};function yE(a){return a in uv}class Zp extends ev{constructor(i){super(i);const{name:s,motionValue:u,element:c,keyframes:f}=this.options;this.resolver=new tv(f,(h,p)=>this.onKeyframesResolved(h,p),s,u,c),this.resolver.scheduleResolve()}initPlayback(i,s){let{duration:u=300,times:c,ease:f,type:h,motionValue:p,name:d,startTime:m}=this.options;if(!p.owner||!p.owner.current)return!1;if(typeof f=="string"&&vu()&&yE(f)&&(f=uv[f]),mE(this.options)){const{onComplete:S,onUpdate:b,motionValue:x,element:M,...z}=this.options,V=pE(i,z);i=V.keyframes,i.length===1&&(i[1]=i[0]),u=V.duration,c=V.times,f=V.ease,h="keyframes"}const g=fE(p.owner.current,d,i,{...this.options,duration:u,times:c,ease:f});return g.startTime=m??this.calcStartTime(),this.pendingTimeline?(Op(g,this.pendingTimeline),this.pendingTimeline=void 0):g.onfinish=()=>{const{onComplete:S}=this.options;p.set(wu(i,this.options,s)),S&&S(),this.cancel(),this.resolveFinishedPromise()},{animation:g,duration:u,times:c,type:h,ease:f,keyframes:i}}get duration(){const{resolved:i}=this;if(!i)return 0;const{duration:s}=i;return vn(s)}get time(){const{resolved:i}=this;if(!i)return 0;const{animation:s}=i;return vn(s.currentTime||0)}set time(i){const{resolved:s}=this;if(!s)return;const{animation:u}=s;u.currentTime=gn(i)}get speed(){const{resolved:i}=this;if(!i)return 1;const{animation:s}=i;return s.playbackRate}set speed(i){const{resolved:s}=this;if(!s)return;const{animation:u}=s;u.playbackRate=i}get state(){const{resolved:i}=this;if(!i)return"idle";const{animation:s}=i;return s.playState}get startTime(){const{resolved:i}=this;if(!i)return null;const{animation:s}=i;return s.startTime}attachTimeline(i){if(!this._resolved)this.pendingTimeline=i;else{const{resolved:s}=this;if(!s)return ge;const{animation:u}=s;Op(u,i)}return ge}play(){if(this.isStopped)return;const{resolved:i}=this;if(!i)return;const{animation:s}=i;s.playState==="finished"&&this.updateFinishedPromise(),s.play()}pause(){const{resolved:i}=this;if(!i)return;const{animation:s}=i;s.pause()}stop(){if(this.resolver.cancel(),this.isStopped=!0,this.state==="idle")return;this.resolveFinishedPromise(),this.updateFinishedPromise();const{resolved:i}=this;if(!i)return;const{animation:s,keyframes:u,duration:c,type:f,ease:h,times:p}=i;if(s.playState==="idle"||s.playState==="finished")return;if(this.time){const{motionValue:m,onUpdate:g,onComplete:S,element:b,...x}=this.options,M=new gf({...x,keyframes:u,duration:c,type:f,ease:h,times:p,isGenerator:!0}),z=gn(this.time);m.setWithVelocity(M.sample(z-Tu).value,M.sample(z).value,Tu)}const{onStop:d}=this.options;d&&d(),this.cancel()}complete(){const{resolved:i}=this;i&&i.animation.finish()}cancel(){const{resolved:i}=this;i&&i.animation.cancel()}static supports(i){const{motionValue:s,name:u,repeatDelay:c,repeatType:f,damping:h,type:p}=i;if(!s||!s.owner||!(s.owner.current instanceof HTMLElement))return!1;const{onUpdate:d,transformTemplate:m}=s.owner.getProps();return hE()&&u&&cE.has(u)&&!d&&!m&&!c&&f!=="mirror"&&h!==0&&p!=="inertia"}}const gE={type:"spring",stiffness:500,damping:25,restSpeed:10},vE=a=>({type:"spring",stiffness:550,damping:a===0?2*Math.sqrt(550):30,restSpeed:10}),SE={type:"keyframes",duration:.8},bE={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},TE=(a,{keyframes:i})=>i.length>2?SE:Ea.has(a)?a.startsWith("scale")?vE(i[1]):gE:bE;function xE({when:a,delay:i,delayChildren:s,staggerChildren:u,staggerDirection:c,repeat:f,repeatType:h,repeatDelay:p,from:d,elapsed:m,...g}){return!!Object.keys(g).length}const vf=(a,i,s,u={},c,f)=>h=>{const p=nf(u,a)||{},d=p.delay||u.delay||0;let{elapsed:m=0}=u;m=m-gn(d);let g={keyframes:Array.isArray(s)?s:[null,s],ease:"easeOut",velocity:i.getVelocity(),...p,delay:-m,onUpdate:b=>{i.set(b),p.onUpdate&&p.onUpdate(b)},onComplete:()=>{h(),p.onComplete&&p.onComplete()},name:a,motionValue:i,element:f?void 0:c};xE(p)||(g={...g,...TE(a,g)}),g.duration&&(g.duration=gn(g.duration)),g.repeatDelay&&(g.repeatDelay=gn(g.repeatDelay)),g.from!==void 0&&(g.keyframes[0]=g.from);let S=!1;if((g.type===!1||g.duration===0&&!g.repeatDelay)&&(g.duration=0,g.delay===0&&(S=!0)),S&&!f&&i.get()!==void 0){const b=wu(g.keyframes,p);if(b!==void 0)return At.update(()=>{g.onUpdate(b),g.onComplete()}),new YT([])}return!f&&Zp.supports(g)?new Zp(g):new gf(g)};function EE({protectedKeys:a,needsAnimating:i},s){const u=a.hasOwnProperty(s)&&i[s]!==!0;return i[s]=!1,u}function rv(a,i,{delay:s=0,transitionOverride:u,type:c}={}){var f;let{transition:h=a.getDefaultTransition(),transitionEnd:p,...d}=i;u&&(h=u);const m=[],g=c&&a.animationState&&a.animationState.getState()[c];for(const S in d){const b=a.getValue(S,(f=a.latestValues[S])!==null&&f!==void 0?f:null),x=d[S];if(x===void 0||g&&EE(g,S))continue;const M={delay:s,...nf(h||{},S)};let z=!1;if(window.MotionHandoffAnimation){const O=Vg(a);if(O){const L=window.MotionHandoffAnimation(O,S,At);L!==null&&(M.startTime=L,z=!0)}}pc(a,S),b.start(vf(S,b,x,a.shouldReduceMotion&&wg.has(S)?{type:!1}:M,a,z));const V=b.animation;V&&m.push(V)}return p&&Promise.all(m).then(()=>{At.update(()=>{p&&ex(a,p)})}),m}function xc(a,i,s={}){var u;const c=zl(a,i,s.type==="exit"?(u=a.presenceContext)===null||u===void 0?void 0:u.custom:void 0);let{transition:f=a.getDefaultTransition()||{}}=c||{};s.transitionOverride&&(f=s.transitionOverride);const h=c?()=>Promise.all(rv(a,c,s)):()=>Promise.resolve(),p=a.variantChildren&&a.variantChildren.size?(m=0)=>{const{delayChildren:g=0,staggerChildren:S,staggerDirection:b}=f;return AE(a,i,g+m,S,b,s)}:()=>Promise.resolve(),{when:d}=f;if(d){const[m,g]=d==="beforeChildren"?[h,p]:[p,h];return m().then(()=>g())}else return Promise.all([h(),p(s.delay)])}function AE(a,i,s=0,u=0,c=1,f){const h=[],p=(a.variantChildren.size-1)*u,d=c===1?(m=0)=>m*u:(m=0)=>p-m*u;return Array.from(a.variantChildren).sort(RE).forEach((m,g)=>{m.notify("AnimationStart",i),h.push(xc(m,i,{...f,delay:s+d(g)}).then(()=>m.notify("AnimationComplete",i)))}),Promise.all(h)}function RE(a,i){return a.sortNodePosition(i)}function DE(a,i,s={}){a.notify("AnimationStart",i);let u;if(Array.isArray(i)){const c=i.map(f=>xc(a,f,s));u=Promise.all(c)}else if(typeof i=="string")u=xc(a,i,s);else{const c=typeof i=="function"?zl(a,i,s.custom):i;u=Promise.all(rv(a,c,s))}return u.then(()=>{a.notify("AnimationComplete",i)})}function ov(a,i){if(!Array.isArray(i))return!1;const s=i.length;if(s!==a.length)return!1;for(let u=0;u<s;u++)if(i[u]!==a[u])return!1;return!0}const ME=Xc.length;function cv(a){if(!a)return;if(!a.isControllingVariants){const s=a.parent?cv(a.parent)||{}:{};return a.props.initial!==void 0&&(s.initial=a.props.initial),s}const i={};for(let s=0;s<ME;s++){const u=Xc[s],c=a.props[u];(wl(c)||c===!1)&&(i[u]=c)}return i}const CE=[...Gc].reverse(),OE=Gc.length;function wE(a){return i=>Promise.all(i.map(({animation:s,options:u})=>DE(a,s,u)))}function _E(a){let i=wE(a),s=kp(),u=!0;const c=d=>(m,g)=>{var S;const b=zl(a,g,d==="exit"?(S=a.presenceContext)===null||S===void 0?void 0:S.custom:void 0);if(b){const{transition:x,transitionEnd:M,...z}=b;m={...m,...z,...M}}return m};function f(d){i=d(a)}function h(d){const{props:m}=a,g=cv(a.parent)||{},S=[],b=new Set;let x={},M=1/0;for(let V=0;V<OE;V++){const O=CE[V],L=s[O],j=m[O]!==void 0?m[O]:g[O],P=wl(j),H=O===d?L.isActive:null;H===!1&&(M=V);let k=j===g[O]&&j!==m[O]&&P;if(k&&u&&a.manuallyAnimateOnMount&&(k=!1),L.protectedKeys={...x},!L.isActive&&H===null||!j&&!L.prevProp||Cu(j)||typeof j=="boolean")continue;const st=VE(L.prevProp,j);let J=st||O===d&&L.isActive&&!k&&P||V>M&&P,X=!1;const tt=Array.isArray(j)?j:[j];let wt=tt.reduce(c(O),{});H===!1&&(wt={});const{prevResolvedValues:ve={}}=L,Se={...ve,...wt},be=$=>{J=!0,b.has($)&&(X=!0,b.delete($)),L.needsAnimating[$]=!0;const W=a.getValue($);W&&(W.liveStyle=!1)};for(const $ in Se){const W=wt[$],gt=ve[$];if(x.hasOwnProperty($))continue;let E=!1;hc(W)&&hc(gt)?E=!ov(W,gt):E=W!==gt,E?W!=null?be($):b.add($):W!==void 0&&b.has($)?be($):L.protectedKeys[$]=!0}L.prevProp=j,L.prevResolvedValues=wt,L.isActive&&(x={...x,...wt}),u&&a.blockInitialAnimation&&(J=!1),J&&(!(k&&st)||X)&&S.push(...tt.map($=>({animation:$,options:{type:O}})))}if(b.size){const V={};if(typeof m.initial!="boolean"){const O=zl(a,Array.isArray(m.initial)?m.initial[0]:m.initial);O&&O.transition&&(V.transition=O.transition)}b.forEach(O=>{const L=a.getBaseTarget(O),j=a.getValue(O);j&&(j.liveStyle=!0),V[O]=L??null}),S.push({animation:V})}let z=!!S.length;return u&&(m.initial===!1||m.initial===m.animate)&&!a.manuallyAnimateOnMount&&(z=!1),u=!1,z?i(S):Promise.resolve()}function p(d,m){var g;if(s[d].isActive===m)return Promise.resolve();(g=a.variantChildren)===null||g===void 0||g.forEach(b=>{var x;return(x=b.animationState)===null||x===void 0?void 0:x.setActive(d,m)}),s[d].isActive=m;const S=h(d);for(const b in s)s[b].protectedKeys={};return S}return{animateChanges:h,setActive:p,setAnimateFunction:f,getState:()=>s,reset:()=>{s=kp(),u=!0}}}function VE(a,i){return typeof i=="string"?i!==a:Array.isArray(i)?!ov(i,a):!1}function ga(a=!1){return{isActive:a,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function kp(){return{animate:ga(!0),whileInView:ga(),whileHover:ga(),whileTap:ga(),whileDrag:ga(),whileFocus:ga(),exit:ga()}}class Pn{constructor(i){this.isMounted=!1,this.node=i}update(){}}class zE extends Pn{constructor(i){super(i),i.animationState||(i.animationState=_E(i))}updateAnimationControlsSubscription(){const{animate:i}=this.node.getProps();Cu(i)&&(this.unmountControls=i.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){const{animate:i}=this.node.getProps(),{animate:s}=this.node.prevProps||{};i!==s&&this.updateAnimationControlsSubscription()}unmount(){var i;this.node.animationState.reset(),(i=this.unmountControls)===null||i===void 0||i.call(this)}}let UE=0;class BE extends Pn{constructor(){super(...arguments),this.id=UE++}update(){if(!this.node.presenceContext)return;const{isPresent:i,onExitComplete:s}=this.node.presenceContext,{isPresent:u}=this.node.prevPresenceContext||{};if(!this.node.animationState||i===u)return;const c=this.node.animationState.setActive("exit",!i);s&&!i&&c.then(()=>{s(this.id)})}mount(){const{register:i,onExitComplete:s}=this.node.presenceContext||{};s&&s(this.id),i&&(this.unmount=i(this.id))}unmount(){}}const jE={animation:{Feature:zE},exit:{Feature:BE}};function jl(a,i,s,u={passive:!0}){return a.addEventListener(i,s,u),()=>a.removeEventListener(i,s)}function Kl(a){return{point:{x:a.pageX,y:a.pageY}}}const LE=a=>i=>sf(i)&&a(i,Kl(i));function Rl(a,i,s,u){return jl(a,i,LE(s),u)}const Pp=(a,i)=>Math.abs(a-i);function NE(a,i){const s=Pp(a.x,i.x),u=Pp(a.y,i.y);return Math.sqrt(s**2+u**2)}class fv{constructor(i,s,{transformPagePoint:u,contextWindow:c,dragSnapToOrigin:f=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const S=ac(this.lastMoveEventInfo,this.history),b=this.startEvent!==null,x=NE(S.offset,{x:0,y:0})>=3;if(!b&&!x)return;const{point:M}=S,{timestamp:z}=Jt;this.history.push({...M,timestamp:z});const{onStart:V,onMove:O}=this.handlers;b||(V&&V(this.lastMoveEvent,S),this.startEvent=this.lastMoveEvent),O&&O(this.lastMoveEvent,S)},this.handlePointerMove=(S,b)=>{this.lastMoveEvent=S,this.lastMoveEventInfo=nc(b,this.transformPagePoint),At.update(this.updatePoint,!0)},this.handlePointerUp=(S,b)=>{this.end();const{onEnd:x,onSessionEnd:M,resumeAnimation:z}=this.handlers;if(this.dragSnapToOrigin&&z&&z(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const V=ac(S.type==="pointercancel"?this.lastMoveEventInfo:nc(b,this.transformPagePoint),this.history);this.startEvent&&x&&x(S,V),M&&M(S,V)},!sf(i))return;this.dragSnapToOrigin=f,this.handlers=s,this.transformPagePoint=u,this.contextWindow=c||window;const h=Kl(i),p=nc(h,this.transformPagePoint),{point:d}=p,{timestamp:m}=Jt;this.history=[{...d,timestamp:m}];const{onSessionStart:g}=s;g&&g(i,ac(p,this.history)),this.removeListeners=Xl(Rl(this.contextWindow,"pointermove",this.handlePointerMove),Rl(this.contextWindow,"pointerup",this.handlePointerUp),Rl(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(i){this.handlers=i}end(){this.removeListeners&&this.removeListeners(),Zn(this.updatePoint)}}function nc(a,i){return i?{point:i(a.point)}:a}function Fp(a,i){return{x:a.x-i.x,y:a.y-i.y}}function ac({point:a},i){return{point:a,delta:Fp(a,hv(i)),offset:Fp(a,HE(i)),velocity:qE(i,.1)}}function HE(a){return a[0]}function hv(a){return a[a.length-1]}function qE(a,i){if(a.length<2)return{x:0,y:0};let s=a.length-1,u=null;const c=hv(a);for(;s>=0&&(u=a[s],!(c.timestamp-u.timestamp>gn(i)));)s--;if(!u)return{x:0,y:0};const f=vn(c.timestamp-u.timestamp);if(f===0)return{x:0,y:0};const h={x:(c.x-u.x)/f,y:(c.y-u.y)/f};return h.x===1/0&&(h.x=0),h.y===1/0&&(h.y=0),h}const dv=1e-4,YE=1-dv,GE=1+dv,mv=.01,XE=0-mv,KE=0+mv;function se(a){return a.max-a.min}function QE(a,i,s){return Math.abs(a-i)<=s}function Jp(a,i,s,u=.5){a.origin=u,a.originPoint=Ct(i.min,i.max,a.origin),a.scale=se(s)/se(i),a.translate=Ct(s.min,s.max,a.origin)-a.originPoint,(a.scale>=YE&&a.scale<=GE||isNaN(a.scale))&&(a.scale=1),(a.translate>=XE&&a.translate<=KE||isNaN(a.translate))&&(a.translate=0)}function Dl(a,i,s,u){Jp(a.x,i.x,s.x,u?u.originX:void 0),Jp(a.y,i.y,s.y,u?u.originY:void 0)}function $p(a,i,s){a.min=s.min+i.min,a.max=a.min+se(i)}function ZE(a,i,s){$p(a.x,i.x,s.x),$p(a.y,i.y,s.y)}function Wp(a,i,s){a.min=i.min-s.min,a.max=a.min+se(i)}function Ml(a,i,s){Wp(a.x,i.x,s.x),Wp(a.y,i.y,s.y)}function kE(a,{min:i,max:s},u){return i!==void 0&&a<i?a=u?Ct(i,a,u.min):Math.max(a,i):s!==void 0&&a>s&&(a=u?Ct(s,a,u.max):Math.min(a,s)),a}function Ip(a,i,s){return{min:i!==void 0?a.min+i:void 0,max:s!==void 0?a.max+s-(a.max-a.min):void 0}}function PE(a,{top:i,left:s,bottom:u,right:c}){return{x:Ip(a.x,s,c),y:Ip(a.y,i,u)}}function ty(a,i){let s=i.min-a.min,u=i.max-a.max;return i.max-i.min<a.max-a.min&&([s,u]=[u,s]),{min:s,max:u}}function FE(a,i){return{x:ty(a.x,i.x),y:ty(a.y,i.y)}}function JE(a,i){let s=.5;const u=se(a),c=se(i);return c>u?s=di(i.min,i.max-u,a.min):u>c&&(s=di(a.min,a.max-c,i.min)),Sn(0,1,s)}function $E(a,i){const s={};return i.min!==void 0&&(s.min=i.min-a.min),i.max!==void 0&&(s.max=i.max-a.min),s}const Ec=.35;function WE(a=Ec){return a===!1?a=0:a===!0&&(a=Ec),{x:ey(a,"left","right"),y:ey(a,"top","bottom")}}function ey(a,i,s){return{min:ny(a,i),max:ny(a,s)}}function ny(a,i){return typeof a=="number"?a:a[i]||0}const ay=()=>({translate:0,scale:1,origin:0,originPoint:0}),ri=()=>({x:ay(),y:ay()}),iy=()=>({min:0,max:0}),jt=()=>({x:iy(),y:iy()});function Ue(a){return[a("x"),a("y")]}function pv({top:a,left:i,right:s,bottom:u}){return{x:{min:i,max:s},y:{min:a,max:u}}}function IE({x:a,y:i}){return{top:i.min,right:a.max,bottom:i.max,left:a.min}}function tA(a,i){if(!i)return a;const s=i({x:a.left,y:a.top}),u=i({x:a.right,y:a.bottom});return{top:s.y,left:s.x,bottom:u.y,right:u.x}}function ic(a){return a===void 0||a===1}function Ac({scale:a,scaleX:i,scaleY:s}){return!ic(a)||!ic(i)||!ic(s)}function Sa(a){return Ac(a)||yv(a)||a.z||a.rotate||a.rotateX||a.rotateY||a.skewX||a.skewY}function yv(a){return ly(a.x)||ly(a.y)}function ly(a){return a&&a!=="0%"}function xu(a,i,s){const u=a-s,c=i*u;return s+c}function sy(a,i,s,u,c){return c!==void 0&&(a=xu(a,c,u)),xu(a,s,u)+i}function Rc(a,i=0,s=1,u,c){a.min=sy(a.min,i,s,u,c),a.max=sy(a.max,i,s,u,c)}function gv(a,{x:i,y:s}){Rc(a.x,i.translate,i.scale,i.originPoint),Rc(a.y,s.translate,s.scale,s.originPoint)}const uy=.999999999999,ry=1.0000000000001;function eA(a,i,s,u=!1){const c=s.length;if(!c)return;i.x=i.y=1;let f,h;for(let p=0;p<c;p++){f=s[p],h=f.projectionDelta;const{visualElement:d}=f.options;d&&d.props.style&&d.props.style.display==="contents"||(u&&f.options.layoutScroll&&f.scroll&&f!==f.root&&ci(a,{x:-f.scroll.offset.x,y:-f.scroll.offset.y}),h&&(i.x*=h.x.scale,i.y*=h.y.scale,gv(a,h)),u&&Sa(f.latestValues)&&ci(a,f.latestValues))}i.x<ry&&i.x>uy&&(i.x=1),i.y<ry&&i.y>uy&&(i.y=1)}function oi(a,i){a.min=a.min+i,a.max=a.max+i}function oy(a,i,s,u,c=.5){const f=Ct(a.min,a.max,c);Rc(a,i,s,f,u)}function ci(a,i){oy(a.x,i.x,i.scaleX,i.scale,i.originX),oy(a.y,i.y,i.scaleY,i.scale,i.originY)}function vv(a,i){return pv(tA(a.getBoundingClientRect(),i))}function nA(a,i,s){const u=vv(a,s),{scroll:c}=i;return c&&(oi(u.x,c.offset.x),oi(u.y,c.offset.y)),u}const Sv=({current:a})=>a?a.ownerDocument.defaultView:null,aA=new WeakMap;class iA{constructor(i){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=jt(),this.visualElement=i}start(i,{snapToCursor:s=!1}={}){const{presenceContext:u}=this.visualElement;if(u&&u.isPresent===!1)return;const c=g=>{const{dragSnapToOrigin:S}=this.getProps();S?this.pauseAnimation():this.stopAnimation(),s&&this.snapToCursor(Kl(g).point)},f=(g,S)=>{const{drag:b,dragPropagation:x,onDragStart:M}=this.getProps();if(b&&!x&&(this.openDragLock&&this.openDragLock(),this.openDragLock=JT(b),!this.openDragLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),Ue(V=>{let O=this.getAxisMotionValue(V).get()||0;if(Pe.test(O)){const{projection:L}=this.visualElement;if(L&&L.layout){const j=L.layout.layoutBox[V];j&&(O=se(j)*(parseFloat(O)/100))}}this.originPoint[V]=O}),M&&At.postRender(()=>M(g,S)),pc(this.visualElement,"transform");const{animationState:z}=this.visualElement;z&&z.setActive("whileDrag",!0)},h=(g,S)=>{const{dragPropagation:b,dragDirectionLock:x,onDirectionLock:M,onDrag:z}=this.getProps();if(!b&&!this.openDragLock)return;const{offset:V}=S;if(x&&this.currentDirection===null){this.currentDirection=lA(V),this.currentDirection!==null&&M&&M(this.currentDirection);return}this.updateAxis("x",S.point,V),this.updateAxis("y",S.point,V),this.visualElement.render(),z&&z(g,S)},p=(g,S)=>this.stop(g,S),d=()=>Ue(g=>{var S;return this.getAnimationState(g)==="paused"&&((S=this.getAxisMotionValue(g).animation)===null||S===void 0?void 0:S.play())}),{dragSnapToOrigin:m}=this.getProps();this.panSession=new fv(i,{onSessionStart:c,onStart:f,onMove:h,onSessionEnd:p,resumeAnimation:d},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:m,contextWindow:Sv(this.visualElement)})}stop(i,s){const u=this.isDragging;if(this.cancel(),!u)return;const{velocity:c}=s;this.startAnimation(c);const{onDragEnd:f}=this.getProps();f&&At.postRender(()=>f(i,s))}cancel(){this.isDragging=!1;const{projection:i,animationState:s}=this.visualElement;i&&(i.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;const{dragPropagation:u}=this.getProps();!u&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),s&&s.setActive("whileDrag",!1)}updateAxis(i,s,u){const{drag:c}=this.getProps();if(!u||!ou(i,c,this.currentDirection))return;const f=this.getAxisMotionValue(i);let h=this.originPoint[i]+u[i];this.constraints&&this.constraints[i]&&(h=kE(h,this.constraints[i],this.elastic[i])),f.set(h)}resolveConstraints(){var i;const{dragConstraints:s,dragElastic:u}=this.getProps(),c=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):(i=this.visualElement.projection)===null||i===void 0?void 0:i.layout,f=this.constraints;s&&si(s)?this.constraints||(this.constraints=this.resolveRefConstraints()):s&&c?this.constraints=PE(c.layoutBox,s):this.constraints=!1,this.elastic=WE(u),f!==this.constraints&&c&&this.constraints&&!this.hasMutatedConstraints&&Ue(h=>{this.constraints!==!1&&this.getAxisMotionValue(h)&&(this.constraints[h]=$E(c.layoutBox[h],this.constraints[h]))})}resolveRefConstraints(){const{dragConstraints:i,onMeasureDragConstraints:s}=this.getProps();if(!i||!si(i))return!1;const u=i.current,{projection:c}=this.visualElement;if(!c||!c.layout)return!1;const f=nA(u,c.root,this.visualElement.getTransformPagePoint());let h=FE(c.layout.layoutBox,f);if(s){const p=s(IE(h));this.hasMutatedConstraints=!!p,p&&(h=pv(p))}return h}startAnimation(i){const{drag:s,dragMomentum:u,dragElastic:c,dragTransition:f,dragSnapToOrigin:h,onDragTransitionEnd:p}=this.getProps(),d=this.constraints||{},m=Ue(g=>{if(!ou(g,s,this.currentDirection))return;let S=d&&d[g]||{};h&&(S={min:0,max:0});const b=c?200:1e6,x=c?40:1e7,M={type:"inertia",velocity:u?i[g]:0,bounceStiffness:b,bounceDamping:x,timeConstant:750,restDelta:1,restSpeed:10,...f,...S};return this.startAxisValueAnimation(g,M)});return Promise.all(m).then(p)}startAxisValueAnimation(i,s){const u=this.getAxisMotionValue(i);return pc(this.visualElement,i),u.start(vf(i,u,0,s,this.visualElement,!1))}stopAnimation(){Ue(i=>this.getAxisMotionValue(i).stop())}pauseAnimation(){Ue(i=>{var s;return(s=this.getAxisMotionValue(i).animation)===null||s===void 0?void 0:s.pause()})}getAnimationState(i){var s;return(s=this.getAxisMotionValue(i).animation)===null||s===void 0?void 0:s.state}getAxisMotionValue(i){const s=`_drag${i.toUpperCase()}`,u=this.visualElement.getProps(),c=u[s];return c||this.visualElement.getValue(i,(u.initial?u.initial[i]:void 0)||0)}snapToCursor(i){Ue(s=>{const{drag:u}=this.getProps();if(!ou(s,u,this.currentDirection))return;const{projection:c}=this.visualElement,f=this.getAxisMotionValue(s);if(c&&c.layout){const{min:h,max:p}=c.layout.layoutBox[s];f.set(i[s]-Ct(h,p,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;const{drag:i,dragConstraints:s}=this.getProps(),{projection:u}=this.visualElement;if(!si(s)||!u||!this.constraints)return;this.stopAnimation();const c={x:0,y:0};Ue(h=>{const p=this.getAxisMotionValue(h);if(p&&this.constraints!==!1){const d=p.get();c[h]=JE({min:d,max:d},this.constraints[h])}});const{transformTemplate:f}=this.visualElement.getProps();this.visualElement.current.style.transform=f?f({},""):"none",u.root&&u.root.updateScroll(),u.updateLayout(),this.resolveConstraints(),Ue(h=>{if(!ou(h,i,null))return;const p=this.getAxisMotionValue(h),{min:d,max:m}=this.constraints[h];p.set(Ct(d,m,c[h]))})}addListeners(){if(!this.visualElement.current)return;aA.set(this.visualElement,this);const i=this.visualElement.current,s=Rl(i,"pointerdown",d=>{const{drag:m,dragListener:g=!0}=this.getProps();m&&g&&this.start(d)}),u=()=>{const{dragConstraints:d}=this.getProps();si(d)&&d.current&&(this.constraints=this.resolveRefConstraints())},{projection:c}=this.visualElement,f=c.addEventListener("measure",u);c&&!c.layout&&(c.root&&c.root.updateScroll(),c.updateLayout()),At.read(u);const h=jl(window,"resize",()=>this.scalePositionWithinConstraints()),p=c.addEventListener("didUpdate",({delta:d,hasLayoutChanged:m})=>{this.isDragging&&m&&(Ue(g=>{const S=this.getAxisMotionValue(g);S&&(this.originPoint[g]+=d[g].translate,S.set(S.get()+d[g].translate))}),this.visualElement.render())});return()=>{h(),s(),f(),p&&p()}}getProps(){const i=this.visualElement.getProps(),{drag:s=!1,dragDirectionLock:u=!1,dragPropagation:c=!1,dragConstraints:f=!1,dragElastic:h=Ec,dragMomentum:p=!0}=i;return{...i,drag:s,dragDirectionLock:u,dragPropagation:c,dragConstraints:f,dragElastic:h,dragMomentum:p}}}function ou(a,i,s){return(i===!0||i===a)&&(s===null||s===a)}function lA(a,i=10){let s=null;return Math.abs(a.y)>i?s="y":Math.abs(a.x)>i&&(s="x"),s}class sA extends Pn{constructor(i){super(i),this.removeGroupControls=ge,this.removeListeners=ge,this.controls=new iA(i)}mount(){const{dragControls:i}=this.node.getProps();i&&(this.removeGroupControls=i.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||ge}unmount(){this.removeGroupControls(),this.removeListeners()}}const cy=a=>(i,s)=>{a&&At.postRender(()=>a(i,s))};class uA extends Pn{constructor(){super(...arguments),this.removePointerDownListener=ge}onPointerDown(i){this.session=new fv(i,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:Sv(this.node)})}createPanHandlers(){const{onPanSessionStart:i,onPanStart:s,onPan:u,onPanEnd:c}=this.node.getProps();return{onSessionStart:cy(i),onStart:cy(s),onMove:u,onEnd:(f,h)=>{delete this.session,c&&At.postRender(()=>c(f,h))}}}mount(){this.removePointerDownListener=Rl(this.node.current,"pointerdown",i=>this.onPointerDown(i))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}const mu={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function fy(a,i){return i.max===i.min?0:a/(i.max-i.min)*100}const Tl={correct:(a,i)=>{if(!i.target)return a;if(typeof a=="string")if(at.test(a))a=parseFloat(a);else return a;const s=fy(a,i.target.x),u=fy(a,i.target.y);return`${s}% ${u}%`}},rA={correct:(a,{treeScale:i,projectionDelta:s})=>{const u=a,c=kn.parse(a);if(c.length>5)return u;const f=kn.createTransformer(a),h=typeof c[0]!="number"?1:0,p=s.x.scale*i.x,d=s.y.scale*i.y;c[0+h]/=p,c[1+h]/=d;const m=Ct(p,d,.5);return typeof c[2+h]=="number"&&(c[2+h]/=m),typeof c[3+h]=="number"&&(c[3+h]/=m),f(c)}};class oA extends R.Component{componentDidMount(){const{visualElement:i,layoutGroup:s,switchLayoutGroup:u,layoutId:c}=this.props,{projection:f}=i;mT(cA),f&&(s.group&&s.group.add(f),u&&u.register&&c&&u.register(f),f.root.didUpdate(),f.addEventListener("animationComplete",()=>{this.safeToRemove()}),f.setOptions({...f.options,onExitComplete:()=>this.safeToRemove()})),mu.hasEverUpdated=!0}getSnapshotBeforeUpdate(i){const{layoutDependency:s,visualElement:u,drag:c,isPresent:f}=this.props,h=u.projection;return h&&(h.isPresent=f,c||i.layoutDependency!==s||s===void 0?h.willUpdate():this.safeToRemove(),i.isPresent!==f&&(f?h.promote():h.relegate()||At.postRender(()=>{const p=h.getStack();(!p||!p.members.length)&&this.safeToRemove()}))),null}componentDidUpdate(){const{projection:i}=this.props.visualElement;i&&(i.root.didUpdate(),Qc.postRender(()=>{!i.currentAnimation&&i.isLead()&&this.safeToRemove()}))}componentWillUnmount(){const{visualElement:i,layoutGroup:s,switchLayoutGroup:u}=this.props,{projection:c}=i;c&&(c.scheduleCheckAfterUnmount(),s&&s.group&&s.group.remove(c),u&&u.deregister&&u.deregister(c))}safeToRemove(){const{safeToRemove:i}=this.props;i&&i()}render(){return null}}function bv(a){const[i,s]=eg(),u=R.useContext(Lc);return Y.jsx(oA,{...a,layoutGroup:u,switchLayoutGroup:R.useContext(cg),isPresent:i,safeToRemove:s})}const cA={borderRadius:{...Tl,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:Tl,borderTopRightRadius:Tl,borderBottomLeftRadius:Tl,borderBottomRightRadius:Tl,boxShadow:rA};function fA(a,i,s){const u=ee(a)?a:Ul(a);return u.start(vf("",u,i,s)),u.animation}function hA(a){return a instanceof SVGElement&&a.tagName!=="svg"}const dA=(a,i)=>a.depth-i.depth;class mA{constructor(){this.children=[],this.isDirty=!1}add(i){uf(this.children,i),this.isDirty=!0}remove(i){rf(this.children,i),this.isDirty=!0}forEach(i){this.isDirty&&this.children.sort(dA),this.isDirty=!1,this.children.forEach(i)}}function pA(a,i){const s=Fe.now(),u=({timestamp:c})=>{const f=c-s;f>=i&&(Zn(u),a(f-i))};return At.read(u,!0),()=>Zn(u)}const Tv=["TopLeft","TopRight","BottomLeft","BottomRight"],yA=Tv.length,hy=a=>typeof a=="string"?parseFloat(a):a,dy=a=>typeof a=="number"||at.test(a);function gA(a,i,s,u,c,f){c?(a.opacity=Ct(0,s.opacity!==void 0?s.opacity:1,vA(u)),a.opacityExit=Ct(i.opacity!==void 0?i.opacity:1,0,SA(u))):f&&(a.opacity=Ct(i.opacity!==void 0?i.opacity:1,s.opacity!==void 0?s.opacity:1,u));for(let h=0;h<yA;h++){const p=`border${Tv[h]}Radius`;let d=my(i,p),m=my(s,p);if(d===void 0&&m===void 0)continue;d||(d=0),m||(m=0),d===0||m===0||dy(d)===dy(m)?(a[p]=Math.max(Ct(hy(d),hy(m),u),0),(Pe.test(m)||Pe.test(d))&&(a[p]+="%")):a[p]=m}(i.rotate||s.rotate)&&(a.rotate=Ct(i.rotate||0,s.rotate||0,u))}function my(a,i){return a[i]!==void 0?a[i]:a.borderRadius}const vA=xv(0,.5,Hg),SA=xv(.5,.95,ge);function xv(a,i,s){return u=>u<a?0:u>i?1:s(di(a,i,u))}function py(a,i){a.min=i.min,a.max=i.max}function ze(a,i){py(a.x,i.x),py(a.y,i.y)}function yy(a,i){a.translate=i.translate,a.scale=i.scale,a.originPoint=i.originPoint,a.origin=i.origin}function gy(a,i,s,u,c){return a-=i,a=xu(a,1/s,u),c!==void 0&&(a=xu(a,1/c,u)),a}function bA(a,i=0,s=1,u=.5,c,f=a,h=a){if(Pe.test(i)&&(i=parseFloat(i),i=Ct(h.min,h.max,i/100)-h.min),typeof i!="number")return;let p=Ct(f.min,f.max,u);a===f&&(p-=i),a.min=gy(a.min,i,s,p,c),a.max=gy(a.max,i,s,p,c)}function vy(a,i,[s,u,c],f,h){bA(a,i[s],i[u],i[c],i.scale,f,h)}const TA=["x","scaleX","originX"],xA=["y","scaleY","originY"];function Sy(a,i,s,u){vy(a.x,i,TA,s?s.x:void 0,u?u.x:void 0),vy(a.y,i,xA,s?s.y:void 0,u?u.y:void 0)}function by(a){return a.translate===0&&a.scale===1}function Ev(a){return by(a.x)&&by(a.y)}function Ty(a,i){return a.min===i.min&&a.max===i.max}function EA(a,i){return Ty(a.x,i.x)&&Ty(a.y,i.y)}function xy(a,i){return Math.round(a.min)===Math.round(i.min)&&Math.round(a.max)===Math.round(i.max)}function Av(a,i){return xy(a.x,i.x)&&xy(a.y,i.y)}function Ey(a){return se(a.x)/se(a.y)}function Ay(a,i){return a.translate===i.translate&&a.scale===i.scale&&a.originPoint===i.originPoint}class AA{constructor(){this.members=[]}add(i){uf(this.members,i),i.scheduleRender()}remove(i){if(rf(this.members,i),i===this.prevLead&&(this.prevLead=void 0),i===this.lead){const s=this.members[this.members.length-1];s&&this.promote(s)}}relegate(i){const s=this.members.findIndex(c=>i===c);if(s===0)return!1;let u;for(let c=s;c>=0;c--){const f=this.members[c];if(f.isPresent!==!1){u=f;break}}return u?(this.promote(u),!0):!1}promote(i,s){const u=this.lead;if(i!==u&&(this.prevLead=u,this.lead=i,i.show(),u)){u.instance&&u.scheduleRender(),i.scheduleRender(),i.resumeFrom=u,s&&(i.resumeFrom.preserveOpacity=!0),u.snapshot&&(i.snapshot=u.snapshot,i.snapshot.latestValues=u.animationValues||u.latestValues),i.root&&i.root.isUpdating&&(i.isLayoutDirty=!0);const{crossfade:c}=i.options;c===!1&&u.hide()}}exitAnimationComplete(){this.members.forEach(i=>{const{options:s,resumingFrom:u}=i;s.onExitComplete&&s.onExitComplete(),u&&u.options.onExitComplete&&u.options.onExitComplete()})}scheduleRender(){this.members.forEach(i=>{i.instance&&i.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}function RA(a,i,s){let u="";const c=a.x.translate/i.x,f=a.y.translate/i.y,h=(s==null?void 0:s.z)||0;if((c||f||h)&&(u=`translate3d(${c}px, ${f}px, ${h}px) `),(i.x!==1||i.y!==1)&&(u+=`scale(${1/i.x}, ${1/i.y}) `),s){const{transformPerspective:m,rotate:g,rotateX:S,rotateY:b,skewX:x,skewY:M}=s;m&&(u=`perspective(${m}px) ${u}`),g&&(u+=`rotate(${g}deg) `),S&&(u+=`rotateX(${S}deg) `),b&&(u+=`rotateY(${b}deg) `),x&&(u+=`skewX(${x}deg) `),M&&(u+=`skewY(${M}deg) `)}const p=a.x.scale*i.x,d=a.y.scale*i.y;return(p!==1||d!==1)&&(u+=`scale(${p}, ${d})`),u||"none"}const lc=["","X","Y","Z"],DA={visibility:"hidden"},Ry=1e3;let MA=0;function sc(a,i,s,u){const{latestValues:c}=i;c[a]&&(s[a]=c[a],i.setStaticValue(a,0),u&&(u[a]=0))}function Rv(a){if(a.hasCheckedOptimisedAppear=!0,a.root===a)return;const{visualElement:i}=a.options;if(!i)return;const s=Vg(i);if(window.MotionHasOptimisedAnimation(s,"transform")){const{layout:c,layoutId:f}=a.options;window.MotionCancelOptimisedAnimation(s,"transform",At,!(c||f))}const{parent:u}=a;u&&!u.hasCheckedOptimisedAppear&&Rv(u)}function Dv({attachResizeListener:a,defaultParent:i,measureScroll:s,checkIsScrollRoot:u,resetTransform:c}){return class{constructor(h={},p=i==null?void 0:i()){this.id=MA++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,this.nodes.forEach(wA),this.nodes.forEach(BA),this.nodes.forEach(jA),this.nodes.forEach(_A)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=h,this.root=p?p.root||p:this,this.path=p?[...p.path,p]:[],this.parent=p,this.depth=p?p.depth+1:0;for(let d=0;d<this.path.length;d++)this.path[d].shouldResetTransform=!0;this.root===this&&(this.nodes=new mA)}addEventListener(h,p){return this.eventHandlers.has(h)||this.eventHandlers.set(h,new of),this.eventHandlers.get(h).add(p)}notifyListeners(h,...p){const d=this.eventHandlers.get(h);d&&d.notify(...p)}hasListeners(h){return this.eventHandlers.has(h)}mount(h,p=this.root.hasTreeAnimated){if(this.instance)return;this.isSVG=hA(h),this.instance=h;const{layoutId:d,layout:m,visualElement:g}=this.options;if(g&&!g.current&&g.mount(h),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),p&&(m||d)&&(this.isLayoutDirty=!0),a){let S;const b=()=>this.root.updateBlockedByResize=!1;a(h,()=>{this.root.updateBlockedByResize=!0,S&&S(),S=pA(b,250),mu.hasAnimatedSinceResize&&(mu.hasAnimatedSinceResize=!1,this.nodes.forEach(My))})}d&&this.root.registerSharedNode(d,this),this.options.animate!==!1&&g&&(d||m)&&this.addEventListener("didUpdate",({delta:S,hasLayoutChanged:b,hasRelativeLayoutChanged:x,layout:M})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}const z=this.options.transition||g.getDefaultTransition()||YA,{onLayoutAnimationStart:V,onLayoutAnimationComplete:O}=g.getProps(),L=!this.targetLayout||!Av(this.targetLayout,M),j=!b&&x;if(this.options.layoutRoot||this.resumeFrom||j||b&&(L||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(S,j);const P={...nf(z,"layout"),onPlay:V,onComplete:O};(g.shouldReduceMotion||this.options.layoutRoot)&&(P.delay=0,P.type=!1),this.startAnimation(P)}else b||My(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=M})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);const h=this.getStack();h&&h.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,Zn(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){this.isUpdateBlocked()||(this.isUpdating=!0,this.nodes&&this.nodes.forEach(LA),this.animationId++)}getTransformTemplate(){const{visualElement:h}=this.options;return h&&h.getProps().transformTemplate}willUpdate(h=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&Rv(this),!this.root.isUpdating&&this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let g=0;g<this.path.length;g++){const S=this.path[g];S.shouldResetTransform=!0,S.updateScroll("snapshot"),S.options.layoutRoot&&S.willUpdate(!1)}const{layoutId:p,layout:d}=this.options;if(p===void 0&&!d)return;const m=this.getTransformTemplate();this.prevTransformTemplateValue=m?m(this.latestValues,""):void 0,this.updateSnapshot(),h&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(Dy);return}this.isUpdating||this.nodes.forEach(zA),this.isUpdating=!1,this.nodes.forEach(UA),this.nodes.forEach(CA),this.nodes.forEach(OA),this.clearAllSnapshots();const p=Fe.now();Jt.delta=Sn(0,1e3/60,p-Jt.timestamp),Jt.timestamp=p,Jt.isProcessing=!0,$o.update.process(Jt),$o.preRender.process(Jt),$o.render.process(Jt),Jt.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,Qc.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(VA),this.sharedNodes.forEach(NA)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,At.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){At.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){this.snapshot||!this.instance||(this.snapshot=this.measure(),this.snapshot&&!se(this.snapshot.measuredBox.x)&&!se(this.snapshot.measuredBox.y)&&(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let d=0;d<this.path.length;d++)this.path[d].updateScroll();const h=this.layout;this.layout=this.measure(!1),this.layoutCorrected=jt(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);const{visualElement:p}=this.options;p&&p.notify("LayoutMeasure",this.layout.layoutBox,h?h.layoutBox:void 0)}updateScroll(h="measure"){let p=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===h&&(p=!1),p){const d=u(this.instance);this.scroll={animationId:this.root.animationId,phase:h,isRoot:d,offset:s(this.instance),wasRoot:this.scroll?this.scroll.isRoot:d}}}resetTransform(){if(!c)return;const h=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,p=this.projectionDelta&&!Ev(this.projectionDelta),d=this.getTransformTemplate(),m=d?d(this.latestValues,""):void 0,g=m!==this.prevTransformTemplateValue;h&&(p||Sa(this.latestValues)||g)&&(c(this.instance,m),this.shouldResetTransform=!1,this.scheduleRender())}measure(h=!0){const p=this.measurePageBox();let d=this.removeElementScroll(p);return h&&(d=this.removeTransform(d)),GA(d),{animationId:this.root.animationId,measuredBox:p,layoutBox:d,latestValues:{},source:this.id}}measurePageBox(){var h;const{visualElement:p}=this.options;if(!p)return jt();const d=p.measureViewportBox();if(!(((h=this.scroll)===null||h===void 0?void 0:h.wasRoot)||this.path.some(XA))){const{scroll:g}=this.root;g&&(oi(d.x,g.offset.x),oi(d.y,g.offset.y))}return d}removeElementScroll(h){var p;const d=jt();if(ze(d,h),!((p=this.scroll)===null||p===void 0)&&p.wasRoot)return d;for(let m=0;m<this.path.length;m++){const g=this.path[m],{scroll:S,options:b}=g;g!==this.root&&S&&b.layoutScroll&&(S.wasRoot&&ze(d,h),oi(d.x,S.offset.x),oi(d.y,S.offset.y))}return d}applyTransform(h,p=!1){const d=jt();ze(d,h);for(let m=0;m<this.path.length;m++){const g=this.path[m];!p&&g.options.layoutScroll&&g.scroll&&g!==g.root&&ci(d,{x:-g.scroll.offset.x,y:-g.scroll.offset.y}),Sa(g.latestValues)&&ci(d,g.latestValues)}return Sa(this.latestValues)&&ci(d,this.latestValues),d}removeTransform(h){const p=jt();ze(p,h);for(let d=0;d<this.path.length;d++){const m=this.path[d];if(!m.instance||!Sa(m.latestValues))continue;Ac(m.latestValues)&&m.updateSnapshot();const g=jt(),S=m.measurePageBox();ze(g,S),Sy(p,m.latestValues,m.snapshot?m.snapshot.layoutBox:void 0,g)}return Sa(this.latestValues)&&Sy(p,this.latestValues),p}setTargetDelta(h){this.targetDelta=h,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(h){this.options={...this.options,...h,crossfade:h.crossfade!==void 0?h.crossfade:!0}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==Jt.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(h=!1){var p;const d=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=d.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=d.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=d.isSharedProjectionDirty);const m=!!this.resumingFrom||this!==d;if(!(h||m&&this.isSharedProjectionDirty||this.isProjectionDirty||!((p=this.parent)===null||p===void 0)&&p.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;const{layout:S,layoutId:b}=this.options;if(!(!this.layout||!(S||b))){if(this.resolvedRelativeTargetAt=Jt.timestamp,!this.targetDelta&&!this.relativeTarget){const x=this.getClosestProjectingParent();x&&x.layout&&this.animationProgress!==1?(this.relativeParent=x,this.forceRelativeParentToResolveTarget(),this.relativeTarget=jt(),this.relativeTargetOrigin=jt(),Ml(this.relativeTargetOrigin,this.layout.layoutBox,x.layout.layoutBox),ze(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(!(!this.relativeTarget&&!this.targetDelta)&&(this.target||(this.target=jt(),this.targetWithTransforms=jt()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target?(this.forceRelativeParentToResolveTarget(),ZE(this.target,this.relativeTarget,this.relativeParent.target)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):ze(this.target,this.layout.layoutBox),gv(this.target,this.targetDelta)):ze(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget)){this.attemptToResolveRelativeTarget=!1;const x=this.getClosestProjectingParent();x&&!!x.resumingFrom==!!this.resumingFrom&&!x.options.layoutScroll&&x.target&&this.animationProgress!==1?(this.relativeParent=x,this.forceRelativeParentToResolveTarget(),this.relativeTarget=jt(),this.relativeTargetOrigin=jt(),Ml(this.relativeTargetOrigin,this.target,x.target),ze(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}}}getClosestProjectingParent(){if(!(!this.parent||Ac(this.parent.latestValues)||yv(this.parent.latestValues)))return this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var h;const p=this.getLead(),d=!!this.resumingFrom||this!==p;let m=!0;if((this.isProjectionDirty||!((h=this.parent)===null||h===void 0)&&h.isProjectionDirty)&&(m=!1),d&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(m=!1),this.resolvedRelativeTargetAt===Jt.timestamp&&(m=!1),m)return;const{layout:g,layoutId:S}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(g||S))return;ze(this.layoutCorrected,this.layout.layoutBox);const b=this.treeScale.x,x=this.treeScale.y;eA(this.layoutCorrected,this.treeScale,this.path,d),p.layout&&!p.target&&(this.treeScale.x!==1||this.treeScale.y!==1)&&(p.target=p.layout.layoutBox,p.targetWithTransforms=jt());const{target:M}=p;if(!M){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}!this.projectionDelta||!this.prevProjectionDelta?this.createProjectionDeltas():(yy(this.prevProjectionDelta.x,this.projectionDelta.x),yy(this.prevProjectionDelta.y,this.projectionDelta.y)),Dl(this.projectionDelta,this.layoutCorrected,M,this.latestValues),(this.treeScale.x!==b||this.treeScale.y!==x||!Ay(this.projectionDelta.x,this.prevProjectionDelta.x)||!Ay(this.projectionDelta.y,this.prevProjectionDelta.y))&&(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",M))}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(h=!0){var p;if((p=this.options.visualElement)===null||p===void 0||p.scheduleRender(),h){const d=this.getStack();d&&d.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=ri(),this.projectionDelta=ri(),this.projectionDeltaWithTransform=ri()}setAnimationOrigin(h,p=!1){const d=this.snapshot,m=d?d.latestValues:{},g={...this.latestValues},S=ri();(!this.relativeParent||!this.relativeParent.options.layoutRoot)&&(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!p;const b=jt(),x=d?d.source:void 0,M=this.layout?this.layout.source:void 0,z=x!==M,V=this.getStack(),O=!V||V.members.length<=1,L=!!(z&&!O&&this.options.crossfade===!0&&!this.path.some(qA));this.animationProgress=0;let j;this.mixTargetDelta=P=>{const H=P/1e3;Cy(S.x,h.x,H),Cy(S.y,h.y,H),this.setTargetDelta(S),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout&&(Ml(b,this.layout.layoutBox,this.relativeParent.layout.layoutBox),HA(this.relativeTarget,this.relativeTargetOrigin,b,H),j&&EA(this.relativeTarget,j)&&(this.isProjectionDirty=!1),j||(j=jt()),ze(j,this.relativeTarget)),z&&(this.animationValues=g,gA(g,m,this.latestValues,H,L,O)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=H},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(h){this.notifyListeners("animationStart"),this.currentAnimation&&this.currentAnimation.stop(),this.resumingFrom&&this.resumingFrom.currentAnimation&&this.resumingFrom.currentAnimation.stop(),this.pendingAnimation&&(Zn(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=At.update(()=>{mu.hasAnimatedSinceResize=!0,this.currentAnimation=fA(0,Ry,{...h,onUpdate:p=>{this.mixTargetDelta(p),h.onUpdate&&h.onUpdate(p)},onStop:()=>{},onComplete:()=>{h.onComplete&&h.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);const h=this.getStack();h&&h.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(Ry),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){const h=this.getLead();let{targetWithTransforms:p,target:d,layout:m,latestValues:g}=h;if(!(!p||!d||!m)){if(this!==h&&this.layout&&m&&Mv(this.options.animationType,this.layout.layoutBox,m.layoutBox)){d=this.target||jt();const S=se(this.layout.layoutBox.x);d.x.min=h.target.x.min,d.x.max=d.x.min+S;const b=se(this.layout.layoutBox.y);d.y.min=h.target.y.min,d.y.max=d.y.min+b}ze(p,d),ci(p,g),Dl(this.projectionDeltaWithTransform,this.layoutCorrected,p,g)}}registerSharedNode(h,p){this.sharedNodes.has(h)||this.sharedNodes.set(h,new AA),this.sharedNodes.get(h).add(p);const m=p.options.initialPromotionConfig;p.promote({transition:m?m.transition:void 0,preserveFollowOpacity:m&&m.shouldPreserveFollowOpacity?m.shouldPreserveFollowOpacity(p):void 0})}isLead(){const h=this.getStack();return h?h.lead===this:!0}getLead(){var h;const{layoutId:p}=this.options;return p?((h=this.getStack())===null||h===void 0?void 0:h.lead)||this:this}getPrevLead(){var h;const{layoutId:p}=this.options;return p?(h=this.getStack())===null||h===void 0?void 0:h.prevLead:void 0}getStack(){const{layoutId:h}=this.options;if(h)return this.root.sharedNodes.get(h)}promote({needsReset:h,transition:p,preserveFollowOpacity:d}={}){const m=this.getStack();m&&m.promote(this,d),h&&(this.projectionDelta=void 0,this.needsReset=!0),p&&this.setOptions({transition:p})}relegate(){const h=this.getStack();return h?h.relegate(this):!1}resetSkewAndRotation(){const{visualElement:h}=this.options;if(!h)return;let p=!1;const{latestValues:d}=h;if((d.z||d.rotate||d.rotateX||d.rotateY||d.rotateZ||d.skewX||d.skewY)&&(p=!0),!p)return;const m={};d.z&&sc("z",h,m,this.animationValues);for(let g=0;g<lc.length;g++)sc(`rotate${lc[g]}`,h,m,this.animationValues),sc(`skew${lc[g]}`,h,m,this.animationValues);h.render();for(const g in m)h.setStaticValue(g,m[g]),this.animationValues&&(this.animationValues[g]=m[g]);h.scheduleRender()}getProjectionStyles(h){var p,d;if(!this.instance||this.isSVG)return;if(!this.isVisible)return DA;const m={visibility:""},g=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,m.opacity="",m.pointerEvents=hu(h==null?void 0:h.pointerEvents)||"",m.transform=g?g(this.latestValues,""):"none",m;const S=this.getLead();if(!this.projectionDelta||!this.layout||!S.target){const z={};return this.options.layoutId&&(z.opacity=this.latestValues.opacity!==void 0?this.latestValues.opacity:1,z.pointerEvents=hu(h==null?void 0:h.pointerEvents)||""),this.hasProjected&&!Sa(this.latestValues)&&(z.transform=g?g({},""):"none",this.hasProjected=!1),z}const b=S.animationValues||S.latestValues;this.applyTransformsToTarget(),m.transform=RA(this.projectionDeltaWithTransform,this.treeScale,b),g&&(m.transform=g(b,m.transform));const{x,y:M}=this.projectionDelta;m.transformOrigin=`${x.origin*100}% ${M.origin*100}% 0`,S.animationValues?m.opacity=S===this?(d=(p=b.opacity)!==null&&p!==void 0?p:this.latestValues.opacity)!==null&&d!==void 0?d:1:this.preserveOpacity?this.latestValues.opacity:b.opacityExit:m.opacity=S===this?b.opacity!==void 0?b.opacity:"":b.opacityExit!==void 0?b.opacityExit:0;for(const z in _l){if(b[z]===void 0)continue;const{correct:V,applyTo:O,isCSSVariable:L}=_l[z],j=m.transform==="none"?b[z]:V(b[z],S);if(O){const P=O.length;for(let H=0;H<P;H++)m[O[H]]=j}else L?this.options.visualElement.renderState.vars[z]=j:m[z]=j}return this.options.layoutId&&(m.pointerEvents=S===this?hu(h==null?void 0:h.pointerEvents)||"":"none"),m}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(h=>{var p;return(p=h.currentAnimation)===null||p===void 0?void 0:p.stop()}),this.root.nodes.forEach(Dy),this.root.sharedNodes.clear()}}}function CA(a){a.updateLayout()}function OA(a){var i;const s=((i=a.resumeFrom)===null||i===void 0?void 0:i.snapshot)||a.snapshot;if(a.isLead()&&a.layout&&s&&a.hasListeners("didUpdate")){const{layoutBox:u,measuredBox:c}=a.layout,{animationType:f}=a.options,h=s.source!==a.layout.source;f==="size"?Ue(S=>{const b=h?s.measuredBox[S]:s.layoutBox[S],x=se(b);b.min=u[S].min,b.max=b.min+x}):Mv(f,s.layoutBox,u)&&Ue(S=>{const b=h?s.measuredBox[S]:s.layoutBox[S],x=se(u[S]);b.max=b.min+x,a.relativeTarget&&!a.currentAnimation&&(a.isProjectionDirty=!0,a.relativeTarget[S].max=a.relativeTarget[S].min+x)});const p=ri();Dl(p,u,s.layoutBox);const d=ri();h?Dl(d,a.applyTransform(c,!0),s.measuredBox):Dl(d,u,s.layoutBox);const m=!Ev(p);let g=!1;if(!a.resumeFrom){const S=a.getClosestProjectingParent();if(S&&!S.resumeFrom){const{snapshot:b,layout:x}=S;if(b&&x){const M=jt();Ml(M,s.layoutBox,b.layoutBox);const z=jt();Ml(z,u,x.layoutBox),Av(M,z)||(g=!0),S.options.layoutRoot&&(a.relativeTarget=z,a.relativeTargetOrigin=M,a.relativeParent=S)}}}a.notifyListeners("didUpdate",{layout:u,snapshot:s,delta:d,layoutDelta:p,hasLayoutChanged:m,hasRelativeLayoutChanged:g})}else if(a.isLead()){const{onExitComplete:u}=a.options;u&&u()}a.options.transition=void 0}function wA(a){a.parent&&(a.isProjecting()||(a.isProjectionDirty=a.parent.isProjectionDirty),a.isSharedProjectionDirty||(a.isSharedProjectionDirty=!!(a.isProjectionDirty||a.parent.isProjectionDirty||a.parent.isSharedProjectionDirty)),a.isTransformDirty||(a.isTransformDirty=a.parent.isTransformDirty))}function _A(a){a.isProjectionDirty=a.isSharedProjectionDirty=a.isTransformDirty=!1}function VA(a){a.clearSnapshot()}function Dy(a){a.clearMeasurements()}function zA(a){a.isLayoutDirty=!1}function UA(a){const{visualElement:i}=a.options;i&&i.getProps().onBeforeLayoutMeasure&&i.notify("BeforeLayoutMeasure"),a.resetTransform()}function My(a){a.finishAnimation(),a.targetDelta=a.relativeTarget=a.target=void 0,a.isProjectionDirty=!0}function BA(a){a.resolveTargetDelta()}function jA(a){a.calcProjection()}function LA(a){a.resetSkewAndRotation()}function NA(a){a.removeLeadSnapshot()}function Cy(a,i,s){a.translate=Ct(i.translate,0,s),a.scale=Ct(i.scale,1,s),a.origin=i.origin,a.originPoint=i.originPoint}function Oy(a,i,s,u){a.min=Ct(i.min,s.min,u),a.max=Ct(i.max,s.max,u)}function HA(a,i,s,u){Oy(a.x,i.x,s.x,u),Oy(a.y,i.y,s.y,u)}function qA(a){return a.animationValues&&a.animationValues.opacityExit!==void 0}const YA={duration:.45,ease:[.4,0,.1,1]},wy=a=>typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(a),_y=wy("applewebkit/")&&!wy("chrome/")?Math.round:ge;function Vy(a){a.min=_y(a.min),a.max=_y(a.max)}function GA(a){Vy(a.x),Vy(a.y)}function Mv(a,i,s){return a==="position"||a==="preserve-aspect"&&!QE(Ey(i),Ey(s),.2)}function XA(a){var i;return a!==a.root&&((i=a.scroll)===null||i===void 0?void 0:i.wasRoot)}const KA=Dv({attachResizeListener:(a,i)=>jl(a,"resize",i),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),uc={current:void 0},Cv=Dv({measureScroll:a=>({x:a.scrollLeft,y:a.scrollTop}),defaultParent:()=>{if(!uc.current){const a=new KA({});a.mount(window),a.setOptions({layoutScroll:!0}),uc.current=a}return uc.current},resetTransform:(a,i)=>{a.style.transform=i!==void 0?i:"none"},checkIsScrollRoot:a=>window.getComputedStyle(a).position==="fixed"}),QA={pan:{Feature:uA},drag:{Feature:sA,ProjectionNode:Cv,MeasureLayout:bv}};function zy(a,i,s){const{props:u}=a;a.animationState&&u.whileHover&&a.animationState.setActive("whileHover",s==="Start");const c="onHover"+s,f=u[c];f&&At.postRender(()=>f(i,Kl(i)))}class ZA extends Pn{mount(){const{current:i}=this.node;i&&(this.unmount=QT(i,(s,u)=>(zy(this.node,u,"Start"),c=>zy(this.node,c,"End"))))}unmount(){}}class kA extends Pn{constructor(){super(...arguments),this.isActive=!1}onFocus(){let i=!1;try{i=this.node.current.matches(":focus-visible")}catch{i=!0}!i||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){!this.isActive||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=Xl(jl(this.node.current,"focus",()=>this.onFocus()),jl(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}function Uy(a,i,s){const{props:u}=a;if(a.current instanceof HTMLButtonElement&&a.current.disabled)return;a.animationState&&u.whileTap&&a.animationState.setActive("whileTap",s==="Start");const c="onTap"+(s==="End"?"":s),f=u[c];f&&At.postRender(()=>f(i,Kl(i)))}class PA extends Pn{mount(){const{current:i}=this.node;i&&(this.unmount=FT(i,(s,u)=>(Uy(this.node,u,"Start"),(c,{success:f})=>Uy(this.node,c,f?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}const Dc=new WeakMap,rc=new WeakMap,FA=a=>{const i=Dc.get(a.target);i&&i(a)},JA=a=>{a.forEach(FA)};function $A({root:a,...i}){const s=a||document;rc.has(s)||rc.set(s,{});const u=rc.get(s),c=JSON.stringify(i);return u[c]||(u[c]=new IntersectionObserver(JA,{root:a,...i})),u[c]}function WA(a,i,s){const u=$A(i);return Dc.set(a,s),u.observe(a),()=>{Dc.delete(a),u.unobserve(a)}}const IA={some:0,all:1};class t2 extends Pn{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();const{viewport:i={}}=this.node.getProps(),{root:s,margin:u,amount:c="some",once:f}=i,h={root:s?s.current:void 0,rootMargin:u,threshold:typeof c=="number"?c:IA[c]},p=d=>{const{isIntersecting:m}=d;if(this.isInView===m||(this.isInView=m,f&&!m&&this.hasEnteredView))return;m&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",m);const{onViewportEnter:g,onViewportLeave:S}=this.node.getProps(),b=m?g:S;b&&b(d)};return WA(this.node.current,h,p)}mount(){this.startObserver()}update(){if(typeof IntersectionObserver>"u")return;const{props:i,prevProps:s}=this.node;["amount","margin","root"].some(e2(i,s))&&this.startObserver()}unmount(){}}function e2({viewport:a={}},{viewport:i={}}={}){return s=>a[s]!==i[s]}const n2={inView:{Feature:t2},tap:{Feature:PA},focus:{Feature:kA},hover:{Feature:ZA}},a2={layout:{ProjectionNode:Cv,MeasureLayout:bv}},Mc={current:null},Ov={current:!1};function i2(){if(Ov.current=!0,!!qc)if(window.matchMedia){const a=window.matchMedia("(prefers-reduced-motion)"),i=()=>Mc.current=a.matches;a.addListener(i),i()}else Mc.current=!1}const l2=[...Ig,te,kn],s2=a=>l2.find(Wg(a)),u2=new WeakMap;function r2(a,i,s){for(const u in i){const c=i[u],f=s[u];if(ee(c))a.addValue(u,c);else if(ee(f))a.addValue(u,Ul(c,{owner:a}));else if(f!==c)if(a.hasValue(u)){const h=a.getValue(u);h.liveStyle===!0?h.jump(c):h.hasAnimated||h.set(c)}else{const h=a.getStaticValue(u);a.addValue(u,Ul(h!==void 0?h:c,{owner:a}))}}for(const u in s)i[u]===void 0&&a.removeValue(u);return i}const By=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class o2{scrapeMotionValuesFromProps(i,s,u){return{}}constructor({parent:i,props:s,presenceContext:u,reducedMotionConfig:c,blockInitialAnimation:f,visualState:h},p={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=pf,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{const x=Fe.now();this.renderScheduledAt<x&&(this.renderScheduledAt=x,At.render(this.render,!1,!0))};const{latestValues:d,renderState:m,onUpdate:g}=h;this.onUpdate=g,this.latestValues=d,this.baseTarget={...d},this.initialValues=s.initial?{...d}:{},this.renderState=m,this.parent=i,this.props=s,this.presenceContext=u,this.depth=i?i.depth+1:0,this.reducedMotionConfig=c,this.options=p,this.blockInitialAnimation=!!f,this.isControllingVariants=Ou(s),this.isVariantNode=rg(s),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(i&&i.current);const{willChange:S,...b}=this.scrapeMotionValuesFromProps(s,{},this);for(const x in b){const M=b[x];d[x]!==void 0&&ee(M)&&M.set(d[x],!1)}}mount(i){this.current=i,u2.set(i,this),this.projection&&!this.projection.instance&&this.projection.mount(i),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((s,u)=>this.bindToMotionValue(u,s)),Ov.current||i2(),this.shouldReduceMotion=this.reducedMotionConfig==="never"?!1:this.reducedMotionConfig==="always"?!0:Mc.current,this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){this.projection&&this.projection.unmount(),Zn(this.notifyUpdate),Zn(this.render),this.valueSubscriptions.forEach(i=>i()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this);for(const i in this.events)this.events[i].clear();for(const i in this.features){const s=this.features[i];s&&(s.unmount(),s.isMounted=!1)}this.current=null}bindToMotionValue(i,s){this.valueSubscriptions.has(i)&&this.valueSubscriptions.get(i)();const u=Ea.has(i);u&&this.onBindTransform&&this.onBindTransform();const c=s.on("change",p=>{this.latestValues[i]=p,this.props.onUpdate&&At.preRender(this.notifyUpdate),u&&this.projection&&(this.projection.isTransformDirty=!0)}),f=s.on("renderRequest",this.scheduleRender);let h;window.MotionCheckAppearSync&&(h=window.MotionCheckAppearSync(this,i,s)),this.valueSubscriptions.set(i,()=>{c(),f(),h&&h(),s.owner&&s.stop()})}sortNodePosition(i){return!this.current||!this.sortInstanceNodePosition||this.type!==i.type?0:this.sortInstanceNodePosition(this.current,i.current)}updateFeatures(){let i="animation";for(i in mi){const s=mi[i];if(!s)continue;const{isEnabled:u,Feature:c}=s;if(!this.features[i]&&c&&u(this.props)&&(this.features[i]=new c(this)),this.features[i]){const f=this.features[i];f.isMounted?f.update():(f.mount(),f.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):jt()}getStaticValue(i){return this.latestValues[i]}setStaticValue(i,s){this.latestValues[i]=s}update(i,s){(i.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=i,this.prevPresenceContext=this.presenceContext,this.presenceContext=s;for(let u=0;u<By.length;u++){const c=By[u];this.propEventSubscriptions[c]&&(this.propEventSubscriptions[c](),delete this.propEventSubscriptions[c]);const f="on"+c,h=i[f];h&&(this.propEventSubscriptions[c]=this.on(c,h))}this.prevMotionValues=r2(this,this.scrapeMotionValuesFromProps(i,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue(),this.onUpdate&&this.onUpdate(this)}getProps(){return this.props}getVariant(i){return this.props.variants?this.props.variants[i]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(i){const s=this.getClosestVariantNode();if(s)return s.variantChildren&&s.variantChildren.add(i),()=>s.variantChildren.delete(i)}addValue(i,s){const u=this.values.get(i);s!==u&&(u&&this.removeValue(i),this.bindToMotionValue(i,s),this.values.set(i,s),this.latestValues[i]=s.get())}removeValue(i){this.values.delete(i);const s=this.valueSubscriptions.get(i);s&&(s(),this.valueSubscriptions.delete(i)),delete this.latestValues[i],this.removeValueFromRenderState(i,this.renderState)}hasValue(i){return this.values.has(i)}getValue(i,s){if(this.props.values&&this.props.values[i])return this.props.values[i];let u=this.values.get(i);return u===void 0&&s!==void 0&&(u=Ul(s===null?void 0:s,{owner:this}),this.addValue(i,u)),u}readValue(i,s){var u;let c=this.latestValues[i]!==void 0||!this.current?this.latestValues[i]:(u=this.getBaseTargetFromProps(this.props,i))!==null&&u!==void 0?u:this.readValueFromInstance(this.current,i,this.options);return c!=null&&(typeof c=="string"&&(Jg(c)||Yg(c))?c=parseFloat(c):!s2(c)&&kn.test(s)&&(c=kg(i,s)),this.setBaseTarget(i,ee(c)?c.get():c)),ee(c)?c.get():c}setBaseTarget(i,s){this.baseTarget[i]=s}getBaseTarget(i){var s;const{initial:u}=this.props;let c;if(typeof u=="string"||typeof u=="object"){const h=tf(this.props,u,(s=this.presenceContext)===null||s===void 0?void 0:s.custom);h&&(c=h[i])}if(u&&c!==void 0)return c;const f=this.getBaseTargetFromProps(this.props,i);return f!==void 0&&!ee(f)?f:this.initialValues[i]!==void 0&&c===void 0?void 0:this.baseTarget[i]}on(i,s){return this.events[i]||(this.events[i]=new of),this.events[i].add(s)}notify(i,...s){this.events[i]&&this.events[i].notify(...s)}}class wv extends o2{constructor(){super(...arguments),this.KeyframeResolver=tv}sortInstanceNodePosition(i,s){return i.compareDocumentPosition(s)&2?1:-1}getBaseTargetFromProps(i,s){return i.style?i.style[s]:void 0}removeValueFromRenderState(i,{vars:s,style:u}){delete s[i],delete u[i]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);const{children:i}=this.props;ee(i)&&(this.childSubscription=i.on("change",s=>{this.current&&(this.current.textContent=`${s}`)}))}}function c2(a){return window.getComputedStyle(a)}class f2 extends wv{constructor(){super(...arguments),this.type="html",this.renderInstance=Sg}readValueFromInstance(i,s){if(Ea.has(s)){const u=mf(s);return u&&u.default||0}else{const u=c2(i),c=(Zc(s)?u.getPropertyValue(s):u[s])||0;return typeof c=="string"?c.trim():c}}measureInstanceViewportBox(i,{transformPagePoint:s}){return vv(i,s)}build(i,s,u){Fc(i,s,u.transformTemplate)}scrapeMotionValuesFromProps(i,s,u){return ef(i,s,u)}}class h2 extends wv{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=jt,this.updateDimensions=()=>{this.current&&!this.renderState.dimensions&&vg(this.current,this.renderState)}}getBaseTargetFromProps(i,s){return i[s]}readValueFromInstance(i,s){if(Ea.has(s)){const u=mf(s);return u&&u.default||0}return s=bg.has(s)?s:Kc(s),i.getAttribute(s)}scrapeMotionValuesFromProps(i,s,u){return xg(i,s,u)}onBindTransform(){this.current&&!this.renderState.dimensions&&At.postRender(this.updateDimensions)}build(i,s,u){Wc(i,s,this.isSVGTag,u.transformTemplate)}renderInstance(i,s,u,c){Tg(i,s,u,c)}mount(i){this.isSVGTag=Ic(i.tagName),super.mount(i)}}const d2=(a,i)=>$c(a)?new h2(i):new f2(i,{allowProjection:a!==R.Fragment}),m2=NT({...jE,...n2,...QA,...a2},d2),_v=tT(m2);/*! *****************************************************************************
Copyright (c) Microsoft Corporation.

Permission to use, copy, modify, and/or distribute this software for any
purpose with or without fee is hereby granted.

THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
PERFORMANCE OF THIS SOFTWARE.
***************************************************************************** */var Cc=function(){return Cc=Object.assign||function(i){for(var s,u=1,c=arguments.length;u<c;u++){s=arguments[u];for(var f in s)Object.prototype.hasOwnProperty.call(s,f)&&(i[f]=s[f])}return i},Cc.apply(this,arguments)};function p2(a,i){var s={};for(var u in a)Object.prototype.hasOwnProperty.call(a,u)&&i.indexOf(u)<0&&(s[u]=a[u]);if(a!=null&&typeof Object.getOwnPropertySymbols=="function")for(var c=0,u=Object.getOwnPropertySymbols(a);c<u.length;c++)i.indexOf(u[c])<0&&Object.prototype.propertyIsEnumerable.call(a,u[c])&&(s[u[c]]=a[u[c]]);return s}var hi="",Cl=null,pu=null,Vv=null;function Sf(){hi="",Cl!==null&&Cl.disconnect(),pu!==null&&(window.clearTimeout(pu),pu=null)}function jy(a){var i=["BUTTON","INPUT","SELECT","TEXTAREA"],s=["A","AREA"];return i.includes(a.tagName)&&!a.hasAttribute("disabled")||s.includes(a.tagName)&&a.hasAttribute("href")}function Ly(){var a=null;if(hi==="#")a=document.body;else{var i=hi.replace("#","");a=document.getElementById(i),a===null&&hi==="#top"&&(a=document.body)}if(a!==null){Vv(a);var s=a.getAttribute("tabindex");return s===null&&!jy(a)&&a.setAttribute("tabindex",-1),a.focus({preventScroll:!0}),s===null&&!jy(a)&&(a.blur(),a.removeAttribute("tabindex")),Sf(),!0}return!1}function y2(a){window.setTimeout(function(){Ly()===!1&&(Cl===null&&(Cl=new MutationObserver(Ly)),Cl.observe(document,{attributes:!0,childList:!0,subtree:!0}),pu=window.setTimeout(function(){Sf()},a||1e4))},0)}function zv(a){return dp.forwardRef(function(i,s){var u="";typeof i.to=="string"&&i.to.includes("#")?u="#"+i.to.split("#").slice(1).join("#"):typeof i.to=="object"&&typeof i.to.hash=="string"&&(u=i.to.hash);var c={};a===Bc&&(c.isActive=function(p,d){return p&&p.isExact&&d.hash===u});function f(p){Sf(),hi=i.elementId?"#"+i.elementId:u,i.onClick&&i.onClick(p),hi!==""&&!p.defaultPrevented&&p.button===0&&(!i.target||i.target==="_self")&&!(p.metaKey||p.altKey||p.ctrlKey||p.shiftKey)&&(Vv=i.scroll||function(d){return i.smooth?d.scrollIntoView({behavior:"smooth"}):d.scrollIntoView()},y2(i.timeout))}var h=p2(i,["scroll","smooth","timeout","elementId"]);return dp.createElement(a,Cc({},c,h,{onClick:f,ref:s}),i.children)})}var fi=zv(Ru);zv(Bc);const Uv=[{id:"home",title:"Home",link:"/"},{id:"magic",title:"Magic",link:"/magic"},{id:"life",title:"Life",link:"/life"},{id:"about",title:"About",link:"/about"}];function g2({currentPage:a}){const[i,s]=R.useState(!1),u=R.useRef(null),{navBarHeight:c}=jc(),f=()=>{s(!i)};return Y.jsxs(Y.Fragment,{children:[Y.jsx("button",{className:"sm:hidden block",onClick:f,children:i?Y.jsx("img",{src:"/img/icons/black_close.svg",alt:"black_close"}):Y.jsx("img",{src:"/img/icons/Hamburger-iphone.svg",alt:"Hamburger-iphone"})}),Y.jsx(ag,{mode:"wait",children:i&&Y.jsx(_v.div,{initial:{opacity:0,x:100},animate:{opacity:1,x:0},exit:{opacity:0,x:100},transition:{duration:.2},className:"absolute w-full top-full left-0 z-50",style:{height:`calc(100vh - ${c}px - 1px)`},children:Y.jsx("div",{className:"bg-black h-full",ref:u,children:Y.jsx("ul",{className:"flex flex-col justify-end items-end gap-[46px] py-[50px] px-[55px]",children:Uv.map(h=>Y.jsx("li",{children:Y.jsx(fi,{onClick:()=>s(!1),to:h.link,className:"inline-block text-white text-[28px]",style:a===h.link?{borderBottomWidth:"2px",marginBottom:"-2px",borderColor:"white"}:{},children:h.title})},h.id))})})})})]})}const v2=()=>window.location.pathname;function S2(){return Y.jsx("div",{className:"bg-black w-full min-h-[1px]"})}const _u={instagram:"https://www.instagram.com/ngoc.vo.illustration/",behance:"https://www.behance.net/ngocillustration",vimeo:"https://vimeo.com/bongbenhcreative"};function Vu({href:a="https://www.example.com",imgSrc:i="",width:s=40}){return Y.jsx("a",{className:"aspect-square",style:{width:`${s}px`},href:a,target:"_blank",rel:"noopener noreferrer",children:Y.jsx("img",{src:i,alt:""})})}const Ny=({width:a})=>Y.jsx(Vu,{width:a,href:_u.instagram,imgSrc:"/img/icons/Insta.png"}),b2=({width:a})=>Y.jsx(Vu,{width:a,href:_u.instagram,imgSrc:"/img/icons/Insta_lite.png"}),oc=({width:a})=>Y.jsx(Vu,{width:a,href:_u.behance,imgSrc:"/img/icons/Behance.png"}),_2=({width:a})=>Y.jsx(Vu,{width:a,href:_u.vimeo,imgSrc:"/img/icons/Vimeo.png"});function T2({currentPage:a}){const[i,s]=R.useState(!1),{navBarHeight:u}=jc(),c=()=>{s(!i)};return Y.jsxs(Y.Fragment,{children:[Y.jsx("button",{className:"lg:hidden sm:block hidden",onClick:c,children:i?Y.jsx("img",{src:"/img/icons/black_close.svg",alt:"black_close"}):Y.jsx("img",{src:"/img/icons/Hamburger-iphone.svg",alt:"Hamburger-iphone"})}),Y.jsx(ag,{mode:"wait",children:i&&Y.jsx(_v.div,{initial:{opacity:0,y:u},animate:{opacity:1},exit:{opacity:0},transition:{duration:.2},className:"absolute w-full h-[300px] top-0 left-0 z-50",children:Y.jsx("div",{className:"bg-black h-full flex items-center justify-center",children:Y.jsx("ul",{className:"flex justify-between gap-[60px]",children:Uv.map(f=>Y.jsx("li",{children:Y.jsx(fi,{onClick:()=>s(!1),to:f.link,className:"inline-block text-white text-[28px] font-light",style:a===f.link?{borderBottomWidth:"2px",marginBottom:"-2px",borderColor:"white"}:{},children:f.title})},f.id))})})})})]})}function x2(){const[a,i]=R.useState(v2()),s=R.useRef(null),{updateNavBarHeight:u}=jc();R.useEffect(()=>{s.current&&u(s.current.offsetHeight)},[u]);const c=h=>{i(h)},f=h=>h==="projects"&&(a==="/magic"||a==="/life")||a===h?{borderBottomWidth:"2px",marginBottom:"-2px",borderColor:"black"}:{};return Y.jsxs("div",{className:"relative",children:[Y.jsx("nav",{ref:s,className:"w-full",children:Y.jsxs("div",{className:"flex justify-between items-center sm:mx-[60px] ml-10 mr-5 sm:h-[112px] h-[56px]",children:[Y.jsxs(Ru,{to:"/",onClick:()=>c("/"),className:"h-full",children:[Y.jsx("img",{src:"/img/logo/<EMAIL>",className:"lg:block hidden h-full w-auto"}),Y.jsx("img",{src:"/img/logo/<EMAIL>",className:"lg:hidden block h-full w-auto"})]}),Y.jsx("div",{className:"hidden lg:block ",children:Y.jsxs("div",{className:"flex justify-between gap-10",children:[Y.jsxs("ul",{className:"flex items-end gap-[30px] font-normal",children:[Y.jsx("li",{onClick:()=>c("/"),children:Y.jsx(fi,{to:"/",className:"flex justify-center text-[17px] px-1 hover:border-b-2 hover:border-black hover:-mb-[2px]",style:f("/"),children:Y.jsx("p",{children:"Home"})})}),Y.jsxs("li",{className:"relative group",children:[Y.jsx("div",{className:"flex justify-center text-[17px] px-1 hover:border-b-2 hover:border-black hover:-mb-[2px] cursor-pointer group-hover:text-gray-400",style:f("projects"),children:"Projects"}),Y.jsxs("ul",{className:"absolute left-[-10px] pl-[10px] pr-[20px] py-[12px] w-fit gap-[10px] bg-white hidden group-hover:flex flex-col z-10",children:[Y.jsx("li",{className:"w-fit whitespace-nowrap",onClick:()=>c("/magic"),children:Y.jsx(fi,{to:"/magic",className:"flex justify-start text-[17px] px-1 hover:border-b-2 hover:border-black hover:-mb-[2px] w-fit whitespace-nowrap",style:f("/magic"),children:"Magic & Fairy"})}),Y.jsx("li",{onClick:()=>c("/life"),children:Y.jsx(fi,{to:"/life",className:"flex justify-start text-[17px] px-1 hover:border-b-2 hover:border-black hover:-mb-[2px] w-fit whitespace-nowrap",style:f("/life"),children:"Life drawings"})})]})]}),Y.jsx("li",{onClick:()=>c("/about"),children:Y.jsx(fi,{to:"/about",className:"flex justify-center text-[17px] px-1 hover:border-b-2 hover:border-black hover:-mb-[2px]",style:f("/about"),children:Y.jsx("p",{children:"About me"})})})]}),Y.jsxs("div",{className:"flex gap-4",children:[Y.jsx(oc,{}),Y.jsx(Ny,{})]})]})}),Y.jsx(T2,{currentPage:a}),Y.jsxs("div",{className:"lg:hidden sm:flex hidden gap-4",children:[Y.jsx(oc,{}),Y.jsx(Ny,{})]}),Y.jsxs("div",{className:"sm:hidden flex items-center gap-5",children:[Y.jsxs("div",{className:"flex items-center gap-[15px]",children:[Y.jsx(oc,{width:29}),Y.jsx(b2,{width:29})]}),Y.jsx(g2,{currentPage:a})]})]})}),Y.jsx(S2,{})]})}function E2(){return Y.jsxs("main",{className:"overflow-x-hidden flex flex-col h-screen",children:[Y.jsx(x2,{}),Y.jsx(ub,{})]})}const A2=R.lazy(()=>Ll(()=>import("./Home-Z-nYTjGW.js"),[])),R2=R.lazy(()=>Ll(()=>import("./Magic-RixGJvXg.js"),__vite__mapDeps([0,1,2,3]))),D2=R.lazy(()=>Ll(()=>import("./Life-j4mYaPCE.js"),__vite__mapDeps([4,5,3]))),M2=R.lazy(()=>Ll(()=>import("./MagicDetail-CzDQd-cn.js"),__vite__mapDeps([6,1,5,3,7]))),C2=R.lazy(()=>Ll(()=>import("./About-DAh8Ipig.js"),__vite__mapDeps([8,2,3])));pS.createRoot(document.getElementById("root")).render(Y.jsx(Gb,{children:Y.jsx(zb,{children:Y.jsx(ob,{children:Y.jsxs(va,{exact:!0,path:"/",element:Y.jsx(E2,{}),children:[Y.jsx(va,{path:"",element:Y.jsx(R.Suspense,{fallback:Y.jsx("div",{children:"loading..."}),children:Y.jsx(A2,{})})}),Y.jsx(va,{path:"magic",element:Y.jsx(R.Suspense,{fallback:Y.jsx("div",{children:"loading..."}),children:Y.jsx(R2,{})})}),Y.jsx(va,{path:"life",element:Y.jsx(R.Suspense,{fallback:Y.jsx("div",{children:"loading..."}),children:Y.jsx(D2,{})})}),Y.jsx(va,{path:"about",element:Y.jsx(R.Suspense,{fallback:Y.jsx("div",{children:"loading..."}),children:Y.jsx(C2,{})})}),Y.jsx(va,{path:"magicDetail/:id",element:Y.jsx(R.Suspense,{fallback:Y.jsx("div",{children:"loading..."}),children:Y.jsx(M2,{})})})]})})})}));export{ag as A,oc as B,Ny as I,Ru as L,dp as R,_2 as V,S2 as a,O2 as b,Y as j,_v as m,R as r,jc as u};
