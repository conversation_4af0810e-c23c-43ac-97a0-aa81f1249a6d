import{r as z,j as C,R as _,b as $e,L as X}from"./index-mQoYb2KJ.js";import{m as D}from"./magicCollection-DQ2xHN7e.js";import{<PERSON> as He}from"./ModalSlider-B_GfrwVy.js";import{F as We}from"./Footer-CuXHR_dz.js";function qe({sliderSrcs:t=[],modalSrcs:e=[],autoSlide:i=!1,autoSlideInterval:s=2e3}){const[r,n]=z.useState(0),[a,o]=z.useState(!1),[l,f]=z.useState(0),d=v=>{f(v),o(!0)},c=()=>{o(!1)},p=()=>{n(v=>v+1===t.length?0:v+1)},u=()=>{n(v=>v-1<0?t.length-1:v-1)},h=v=>{n(v)};return z.useEffect(()=>{if(n(0),!i)return;const v=setInterval(p,s);return()=>{clearInterval(v)}},[t]),C.jsxs("div",{className:"relative max-h-full h-full flex flex-col",children:[C.jsx("div",{className:"h-full aspect-square bg-center bg-cover duration-500 cursor-pointer",style:{backgroundImage:`url(${t[r]})`},onClick:()=>d(r)}),t.length>1&&C.jsxs(C.Fragment,{children:[C.jsx("button",{className:"sm:flex hidden absolute left-0 top-1/2 -translate-y-1/2 -translate-x-[100%] w-[50px] aspect-square bg-black items-center justify-center",onClick:u,children:C.jsx("img",{src:"/img/icons/white_slide_left.svg",alt:"white_slide_left",className:"w-[17px]"})}),C.jsx("button",{className:"sm:flex hidden absolute right-0 top-1/2 -translate-y-1/2 translate-x-[100%] w-[50px] aspect-square bg-black items-center justify-center",onClick:p,children:C.jsx("img",{src:"/img/icons/white_slide_right.svg",alt:"white_slide_right",className:"w-[17px]"})}),C.jsx("div",{className:"absolute left-0 right-0 -bottom-10",children:C.jsx("div",{className:"flex items-center justify-center gap-[10px]",children:t.map((v,E)=>C.jsx("div",{onClick:()=>h(E),className:`transition-all w-5 aspect-square border-black border-[1px] rounded-full ${r===E?"bg-black":"bg-white cursor-pointer"}`},E))})})]}),a&&C.jsx(He,{onClose:c,index:l,images:e})]})}function ve(t){return t!==null&&typeof t=="object"&&"constructor"in t&&t.constructor===Object}function he(t,e){t===void 0&&(t={}),e===void 0&&(e={});const i=["__proto__","constructor","prototype"];Object.keys(e).filter(s=>i.indexOf(s)<0).forEach(s=>{typeof t[s]>"u"?t[s]=e[s]:ve(e[s])&&ve(t[s])&&Object.keys(e[s]).length>0&&he(t[s],e[s])})}const Me={body:{},addEventListener(){},removeEventListener(){},activeElement:{blur(){},nodeName:""},querySelector(){return null},querySelectorAll(){return[]},getElementById(){return null},createEvent(){return{initEvent(){}}},createElement(){return{children:[],childNodes:[],style:{},setAttribute(){},getElementsByTagName(){return[]}}},createElementNS(){return{}},importNode(){return null},location:{hash:"",host:"",hostname:"",href:"",origin:"",pathname:"",protocol:"",search:""}};function q(){const t=typeof document<"u"?document:{};return he(t,Me),t}const Ye={document:Me,navigator:{userAgent:""},location:{hash:"",host:"",hostname:"",href:"",origin:"",pathname:"",protocol:"",search:""},history:{replaceState(){},pushState(){},go(){},back(){}},CustomEvent:function(){return this},addEventListener(){},removeEventListener(){},getComputedStyle(){return{getPropertyValue(){return""}}},Image(){},Date(){},screen:{},setTimeout(){},clearTimeout(){},matchMedia(){return{}},requestAnimationFrame(t){return typeof setTimeout>"u"?(t(),null):setTimeout(t,0)},cancelAnimationFrame(t){typeof setTimeout>"u"||clearTimeout(t)}};function V(){const t=typeof window<"u"?window:{};return he(t,Ye),t}function Xe(t){return t===void 0&&(t=""),t.trim().split(" ").filter(e=>!!e.trim())}function Ke(t){const e=t;Object.keys(e).forEach(i=>{try{e[i]=null}catch{}try{delete e[i]}catch{}})}function fe(t,e){return e===void 0&&(e=0),setTimeout(t,e)}function H(){return Date.now()}function Ue(t){const e=V();let i;return e.getComputedStyle&&(i=e.getComputedStyle(t,null)),!i&&t.currentStyle&&(i=t.currentStyle),i||(i=t.style),i}function Qe(t,e){e===void 0&&(e="x");const i=V();let s,r,n;const a=Ue(t);return i.WebKitCSSMatrix?(r=a.transform||a.webkitTransform,r.split(",").length>6&&(r=r.split(", ").map(o=>o.replace(",",".")).join(", ")),n=new i.WebKitCSSMatrix(r==="none"?"":r)):(n=a.MozTransform||a.OTransform||a.MsTransform||a.msTransform||a.transform||a.getPropertyValue("transform").replace("translate(","matrix(1, 0, 0, 1,"),s=n.toString().split(",")),e==="x"&&(i.WebKitCSSMatrix?r=n.m41:s.length===16?r=parseFloat(s[12]):r=parseFloat(s[4])),e==="y"&&(i.WebKitCSSMatrix?r=n.m42:s.length===16?r=parseFloat(s[13]):r=parseFloat(s[5])),r||0}function Q(t){return typeof t=="object"&&t!==null&&t.constructor&&Object.prototype.toString.call(t).slice(8,-1)==="Object"}function Ze(t){return typeof window<"u"&&typeof window.HTMLElement<"u"?t instanceof HTMLElement:t&&(t.nodeType===1||t.nodeType===11)}function j(){const t=Object(arguments.length<=0?void 0:arguments[0]),e=["__proto__","constructor","prototype"];for(let i=1;i<arguments.length;i+=1){const s=i<0||arguments.length<=i?void 0:arguments[i];if(s!=null&&!Ze(s)){const r=Object.keys(Object(s)).filter(n=>e.indexOf(n)<0);for(let n=0,a=r.length;n<a;n+=1){const o=r[n],l=Object.getOwnPropertyDescriptor(s,o);l!==void 0&&l.enumerable&&(Q(t[o])&&Q(s[o])?s[o].__swiper__?t[o]=s[o]:j(t[o],s[o]):!Q(t[o])&&Q(s[o])?(t[o]={},s[o].__swiper__?t[o]=s[o]:j(t[o],s[o])):t[o]=s[o])}}}return t}function Z(t,e,i){t.style.setProperty(e,i)}function Pe(t){let{swiper:e,targetPosition:i,side:s}=t;const r=V(),n=-e.translate;let a=null,o;const l=e.params.speed;e.wrapperEl.style.scrollSnapType="none",r.cancelAnimationFrame(e.cssModeFrameID);const f=i>n?"next":"prev",d=(p,u)=>f==="next"&&p>=u||f==="prev"&&p<=u,c=()=>{o=new Date().getTime(),a===null&&(a=o);const p=Math.max(Math.min((o-a)/l,1),0),u=.5-Math.cos(p*Math.PI)/2;let h=n+u*(i-n);if(d(h,i)&&(h=i),e.wrapperEl.scrollTo({[s]:h}),d(h,i)){e.wrapperEl.style.overflow="hidden",e.wrapperEl.style.scrollSnapType="",setTimeout(()=>{e.wrapperEl.style.overflow="",e.wrapperEl.scrollTo({[s]:h})}),r.cancelAnimationFrame(e.cssModeFrameID);return}e.cssModeFrameID=r.requestAnimationFrame(c)};c()}function k(t,e){e===void 0&&(e="");const i=V(),s=[...t.children];return i.HTMLSlotElement&&t instanceof HTMLSlotElement&&s.push(...t.assignedElements()),e?s.filter(r=>r.matches(e)):s}function Je(t,e){const i=[e];for(;i.length>0;){const s=i.shift();if(t===s)return!0;i.push(...s.children,...s.shadowRoot?s.shadowRoot.children:[],...s.assignedElements?s.assignedElements():[])}}function et(t,e){const i=V();let s=e.contains(t);return!s&&i.HTMLSlotElement&&e instanceof HTMLSlotElement&&(s=[...e.assignedElements()].includes(t),s||(s=Je(t,e))),s}function ee(t){try{console.warn(t);return}catch{}}function ue(t,e){e===void 0&&(e=[]);const i=document.createElement(t);return i.classList.add(...Array.isArray(e)?e:Xe(e)),i}function tt(t,e){const i=[];for(;t.previousElementSibling;){const s=t.previousElementSibling;e?s.matches(e)&&i.push(s):i.push(s),t=s}return i}function it(t,e){const i=[];for(;t.nextElementSibling;){const s=t.nextElementSibling;e?s.matches(e)&&i.push(s):i.push(s),t=s}return i}function $(t,e){return V().getComputedStyle(t,null).getPropertyValue(e)}function we(t){let e=t,i;if(e){for(i=0;(e=e.previousSibling)!==null;)e.nodeType===1&&(i+=1);return i}}function st(t,e){const i=[];let s=t.parentElement;for(;s;)i.push(s),s=s.parentElement;return i}function se(t,e){function i(s){s.target===t&&(e.call(t,s),t.removeEventListener("transitionend",i))}e&&t.addEventListener("transitionend",i)}function Se(t,e,i){const s=V();return t[e==="width"?"offsetWidth":"offsetHeight"]+parseFloat(s.getComputedStyle(t,null).getPropertyValue(e==="width"?"margin-right":"margin-top"))+parseFloat(s.getComputedStyle(t,null).getPropertyValue(e==="width"?"margin-left":"margin-bottom"))}function rt(t){let{swiper:e,extendParams:i,emit:s,once:r}=t;i({freeMode:{enabled:!1,momentum:!0,momentumRatio:1,momentumBounce:!0,momentumBounceRatio:1,momentumVelocityRatio:1,sticky:!1,minimumVelocity:.02}});function n(){if(e.params.cssMode)return;const l=e.getTranslate();e.setTranslate(l),e.setTransition(0),e.touchEventsData.velocities.length=0,e.freeMode.onTouchEnd({currentPos:e.rtl?e.translate:-e.translate})}function a(){if(e.params.cssMode)return;const{touchEventsData:l,touches:f}=e;l.velocities.length===0&&l.velocities.push({position:f[e.isHorizontal()?"startX":"startY"],time:l.touchStartTime}),l.velocities.push({position:f[e.isHorizontal()?"currentX":"currentY"],time:H()})}function o(l){let{currentPos:f}=l;if(e.params.cssMode)return;const{params:d,wrapperEl:c,rtlTranslate:p,snapGrid:u,touchEventsData:h}=e,E=H()-h.touchStartTime;if(f<-e.minTranslate()){e.slideTo(e.activeIndex);return}if(f>-e.maxTranslate()){e.slides.length<u.length?e.slideTo(u.length-1):e.slideTo(e.slides.length-1);return}if(d.freeMode.momentum){if(h.velocities.length>1){const x=h.velocities.pop(),T=h.velocities.pop(),w=x.position-T.position,M=x.time-T.time;e.velocity=w/M,e.velocity/=2,Math.abs(e.velocity)<d.freeMode.minimumVelocity&&(e.velocity=0),(M>150||H()-x.time>300)&&(e.velocity=0)}else e.velocity=0;e.velocity*=d.freeMode.momentumVelocityRatio,h.velocities.length=0;let m=1e3*d.freeMode.momentumRatio;const S=e.velocity*m;let g=e.translate+S;p&&(g=-g);let y=!1,b;const L=Math.abs(e.velocity)*20*d.freeMode.momentumBounceRatio;let O;if(g<e.maxTranslate())d.freeMode.momentumBounce?(g+e.maxTranslate()<-L&&(g=e.maxTranslate()-L),b=e.maxTranslate(),y=!0,h.allowMomentumBounce=!0):g=e.maxTranslate(),d.loop&&d.centeredSlides&&(O=!0);else if(g>e.minTranslate())d.freeMode.momentumBounce?(g-e.minTranslate()>L&&(g=e.minTranslate()+L),b=e.minTranslate(),y=!0,h.allowMomentumBounce=!0):g=e.minTranslate(),d.loop&&d.centeredSlides&&(O=!0);else if(d.freeMode.sticky){let x;for(let T=0;T<u.length;T+=1)if(u[T]>-g){x=T;break}Math.abs(u[x]-g)<Math.abs(u[x-1]-g)||e.swipeDirection==="next"?g=u[x]:g=u[x-1],g=-g}if(O&&r("transitionEnd",()=>{e.loopFix()}),e.velocity!==0){if(p?m=Math.abs((-g-e.translate)/e.velocity):m=Math.abs((g-e.translate)/e.velocity),d.freeMode.sticky){const x=Math.abs((p?-g:g)-e.translate),T=e.slidesSizesGrid[e.activeIndex];x<T?m=d.speed:x<2*T?m=d.speed*1.5:m=d.speed*2.5}}else if(d.freeMode.sticky){e.slideToClosest();return}d.freeMode.momentumBounce&&y?(e.updateProgress(b),e.setTransition(m),e.setTranslate(g),e.transitionStart(!0,e.swipeDirection),e.animating=!0,se(c,()=>{!e||e.destroyed||!h.allowMomentumBounce||(s("momentumBounce"),e.setTransition(d.speed),setTimeout(()=>{e.setTranslate(b),se(c,()=>{!e||e.destroyed||e.transitionEnd()})},0))})):e.velocity?(s("_freeModeNoMomentumRelease"),e.updateProgress(g),e.setTransition(m),e.setTranslate(g),e.transitionStart(!0,e.swipeDirection),e.animating||(e.animating=!0,se(c,()=>{!e||e.destroyed||e.transitionEnd()}))):e.updateProgress(g),e.updateActiveIndex(),e.updateSlidesClasses()}else if(d.freeMode.sticky){e.slideToClosest();return}else d.freeMode&&s("_freeModeNoMomentumRelease");(!d.freeMode.momentum||E>=d.longSwipesMs)&&(s("_freeModeStaticRelease"),e.updateProgress(),e.updateActiveIndex(),e.updateSlidesClasses())}Object.assign(e,{freeMode:{onTouchStart:n,onTouchMove:a,onTouchEnd:o}})}let re;function nt(){const t=V(),e=q();return{smoothScroll:e.documentElement&&e.documentElement.style&&"scrollBehavior"in e.documentElement.style,touch:!!("ontouchstart"in t||t.DocumentTouch&&e instanceof t.DocumentTouch)}}function Ce(){return re||(re=nt()),re}let ne;function lt(t){let{userAgent:e}=t===void 0?{}:t;const i=Ce(),s=V(),r=s.navigator.platform,n=e||s.navigator.userAgent,a={ios:!1,android:!1},o=s.screen.width,l=s.screen.height,f=n.match(/(Android);?[\s\/]+([\d.]+)?/);let d=n.match(/(iPad).*OS\s([\d_]+)/);const c=n.match(/(iPod)(.*OS\s([\d_]+))?/),p=!d&&n.match(/(iPhone\sOS|iOS)\s([\d_]+)/),u=r==="Win32";let h=r==="MacIntel";const v=["1024x1366","1366x1024","834x1194","1194x834","834x1112","1112x834","768x1024","1024x768","820x1180","1180x820","810x1080","1080x810"];return!d&&h&&i.touch&&v.indexOf(`${o}x${l}`)>=0&&(d=n.match(/(Version)\/([\d.]+)/),d||(d=[0,1,"13_0_0"]),h=!1),f&&!u&&(a.os="android",a.android=!0),(d||p||c)&&(a.os="ios",a.ios=!0),a}function Ie(t){return t===void 0&&(t={}),ne||(ne=lt(t)),ne}let le;function ot(){const t=V(),e=Ie();let i=!1;function s(){const o=t.navigator.userAgent.toLowerCase();return o.indexOf("safari")>=0&&o.indexOf("chrome")<0&&o.indexOf("android")<0}if(s()){const o=String(t.navigator.userAgent);if(o.includes("Version/")){const[l,f]=o.split("Version/")[1].split(" ")[0].split(".").map(d=>Number(d));i=l<16||l===16&&f<2}}const r=/(iPhone|iPod|iPad).*AppleWebKit(?!.*Safari)/i.test(t.navigator.userAgent),n=s(),a=n||r&&e.ios;return{isSafari:i||n,needPerspectiveFix:i,need3dFix:a,isWebView:r}}function Oe(){return le||(le=ot()),le}function at(t){let{swiper:e,on:i,emit:s}=t;const r=V();let n=null,a=null;const o=()=>{!e||e.destroyed||!e.initialized||(s("beforeResize"),s("resize"))},l=()=>{!e||e.destroyed||!e.initialized||(n=new ResizeObserver(c=>{a=r.requestAnimationFrame(()=>{const{width:p,height:u}=e;let h=p,v=u;c.forEach(E=>{let{contentBoxSize:m,contentRect:S,target:g}=E;g&&g!==e.el||(h=S?S.width:(m[0]||m).inlineSize,v=S?S.height:(m[0]||m).blockSize)}),(h!==p||v!==u)&&o()})}),n.observe(e.el))},f=()=>{a&&r.cancelAnimationFrame(a),n&&n.unobserve&&e.el&&(n.unobserve(e.el),n=null)},d=()=>{!e||e.destroyed||!e.initialized||s("orientationchange")};i("init",()=>{if(e.params.resizeObserver&&typeof r.ResizeObserver<"u"){l();return}r.addEventListener("resize",o),r.addEventListener("orientationchange",d)}),i("destroy",()=>{f(),r.removeEventListener("resize",o),r.removeEventListener("orientationchange",d)})}function dt(t){let{swiper:e,extendParams:i,on:s,emit:r}=t;const n=[],a=V(),o=function(d,c){c===void 0&&(c={});const p=a.MutationObserver||a.WebkitMutationObserver,u=new p(h=>{if(e.__preventObserver__)return;if(h.length===1){r("observerUpdate",h[0]);return}const v=function(){r("observerUpdate",h[0])};a.requestAnimationFrame?a.requestAnimationFrame(v):a.setTimeout(v,0)});u.observe(d,{attributes:typeof c.attributes>"u"?!0:c.attributes,childList:e.isElement||(typeof c.childList>"u"?!0:c).childList,characterData:typeof c.characterData>"u"?!0:c.characterData}),n.push(u)},l=()=>{if(e.params.observer){if(e.params.observeParents){const d=st(e.hostEl);for(let c=0;c<d.length;c+=1)o(d[c])}o(e.hostEl,{childList:e.params.observeSlideChildren}),o(e.wrapperEl,{attributes:!1})}},f=()=>{n.forEach(d=>{d.disconnect()}),n.splice(0,n.length)};i({observer:!1,observeParents:!1,observeSlideChildren:!1}),s("init",l),s("destroy",f)}var ct={on(t,e,i){const s=this;if(!s.eventsListeners||s.destroyed||typeof e!="function")return s;const r=i?"unshift":"push";return t.split(" ").forEach(n=>{s.eventsListeners[n]||(s.eventsListeners[n]=[]),s.eventsListeners[n][r](e)}),s},once(t,e,i){const s=this;if(!s.eventsListeners||s.destroyed||typeof e!="function")return s;function r(){s.off(t,r),r.__emitterProxy&&delete r.__emitterProxy;for(var n=arguments.length,a=new Array(n),o=0;o<n;o++)a[o]=arguments[o];e.apply(s,a)}return r.__emitterProxy=e,s.on(t,r,i)},onAny(t,e){const i=this;if(!i.eventsListeners||i.destroyed||typeof t!="function")return i;const s=e?"unshift":"push";return i.eventsAnyListeners.indexOf(t)<0&&i.eventsAnyListeners[s](t),i},offAny(t){const e=this;if(!e.eventsListeners||e.destroyed||!e.eventsAnyListeners)return e;const i=e.eventsAnyListeners.indexOf(t);return i>=0&&e.eventsAnyListeners.splice(i,1),e},off(t,e){const i=this;return!i.eventsListeners||i.destroyed||!i.eventsListeners||t.split(" ").forEach(s=>{typeof e>"u"?i.eventsListeners[s]=[]:i.eventsListeners[s]&&i.eventsListeners[s].forEach((r,n)=>{(r===e||r.__emitterProxy&&r.__emitterProxy===e)&&i.eventsListeners[s].splice(n,1)})}),i},emit(){const t=this;if(!t.eventsListeners||t.destroyed||!t.eventsListeners)return t;let e,i,s;for(var r=arguments.length,n=new Array(r),a=0;a<r;a++)n[a]=arguments[a];return typeof n[0]=="string"||Array.isArray(n[0])?(e=n[0],i=n.slice(1,n.length),s=t):(e=n[0].events,i=n[0].data,s=n[0].context||t),i.unshift(s),(Array.isArray(e)?e:e.split(" ")).forEach(l=>{t.eventsAnyListeners&&t.eventsAnyListeners.length&&t.eventsAnyListeners.forEach(f=>{f.apply(s,[l,...i])}),t.eventsListeners&&t.eventsListeners[l]&&t.eventsListeners[l].forEach(f=>{f.apply(s,i)})}),t}};function ft(){const t=this;let e,i;const s=t.el;typeof t.params.width<"u"&&t.params.width!==null?e=t.params.width:e=s.clientWidth,typeof t.params.height<"u"&&t.params.height!==null?i=t.params.height:i=s.clientHeight,!(e===0&&t.isHorizontal()||i===0&&t.isVertical())&&(e=e-parseInt($(s,"padding-left")||0,10)-parseInt($(s,"padding-right")||0,10),i=i-parseInt($(s,"padding-top")||0,10)-parseInt($(s,"padding-bottom")||0,10),Number.isNaN(e)&&(e=0),Number.isNaN(i)&&(i=0),Object.assign(t,{width:e,height:i,size:t.isHorizontal()?e:i}))}function ut(){const t=this;function e(w,M){return parseFloat(w.getPropertyValue(t.getDirectionLabel(M))||0)}const i=t.params,{wrapperEl:s,slidesEl:r,size:n,rtlTranslate:a,wrongRTL:o}=t,l=t.virtual&&i.virtual.enabled,f=l?t.virtual.slides.length:t.slides.length,d=k(r,`.${t.params.slideClass}, swiper-slide`),c=l?t.virtual.slides.length:d.length;let p=[];const u=[],h=[];let v=i.slidesOffsetBefore;typeof v=="function"&&(v=i.slidesOffsetBefore.call(t));let E=i.slidesOffsetAfter;typeof E=="function"&&(E=i.slidesOffsetAfter.call(t));const m=t.snapGrid.length,S=t.slidesGrid.length;let g=i.spaceBetween,y=-v,b=0,L=0;if(typeof n>"u")return;typeof g=="string"&&g.indexOf("%")>=0?g=parseFloat(g.replace("%",""))/100*n:typeof g=="string"&&(g=parseFloat(g)),t.virtualSize=-g,d.forEach(w=>{a?w.style.marginLeft="":w.style.marginRight="",w.style.marginBottom="",w.style.marginTop=""}),i.centeredSlides&&i.cssMode&&(Z(s,"--swiper-centered-offset-before",""),Z(s,"--swiper-centered-offset-after",""));const O=i.grid&&i.grid.rows>1&&t.grid;O?t.grid.initSlides(d):t.grid&&t.grid.unsetSlides();let x;const T=i.slidesPerView==="auto"&&i.breakpoints&&Object.keys(i.breakpoints).filter(w=>typeof i.breakpoints[w].slidesPerView<"u").length>0;for(let w=0;w<c;w+=1){x=0;let M;if(d[w]&&(M=d[w]),O&&t.grid.updateSlide(w,M,d),!(d[w]&&$(M,"display")==="none")){if(i.slidesPerView==="auto"){T&&(d[w].style[t.getDirectionLabel("width")]="");const I=getComputedStyle(M),P=M.style.transform,A=M.style.webkitTransform;if(P&&(M.style.transform="none"),A&&(M.style.webkitTransform="none"),i.roundLengths)x=t.isHorizontal()?Se(M,"width"):Se(M,"height");else{const N=e(I,"width"),R=e(I,"padding-left"),ie=e(I,"padding-right"),U=e(I,"margin-left"),G=e(I,"margin-right"),B=I.getPropertyValue("box-sizing");if(B&&B==="border-box")x=N+U+G;else{const{clientWidth:Fe,offsetWidth:ke}=M;x=N+R+ie+U+G+(ke-Fe)}}P&&(M.style.transform=P),A&&(M.style.webkitTransform=A),i.roundLengths&&(x=Math.floor(x))}else x=(n-(i.slidesPerView-1)*g)/i.slidesPerView,i.roundLengths&&(x=Math.floor(x)),d[w]&&(d[w].style[t.getDirectionLabel("width")]=`${x}px`);d[w]&&(d[w].swiperSlideSize=x),h.push(x),i.centeredSlides?(y=y+x/2+b/2+g,b===0&&w!==0&&(y=y-n/2-g),w===0&&(y=y-n/2-g),Math.abs(y)<1/1e3&&(y=0),i.roundLengths&&(y=Math.floor(y)),L%i.slidesPerGroup===0&&p.push(y),u.push(y)):(i.roundLengths&&(y=Math.floor(y)),(L-Math.min(t.params.slidesPerGroupSkip,L))%t.params.slidesPerGroup===0&&p.push(y),u.push(y),y=y+x+g),t.virtualSize+=x+g,b=x,L+=1}}if(t.virtualSize=Math.max(t.virtualSize,n)+E,a&&o&&(i.effect==="slide"||i.effect==="coverflow")&&(s.style.width=`${t.virtualSize+g}px`),i.setWrapperSize&&(s.style[t.getDirectionLabel("width")]=`${t.virtualSize+g}px`),O&&t.grid.updateWrapperSize(x,p),!i.centeredSlides){const w=[];for(let M=0;M<p.length;M+=1){let I=p[M];i.roundLengths&&(I=Math.floor(I)),p[M]<=t.virtualSize-n&&w.push(I)}p=w,Math.floor(t.virtualSize-n)-Math.floor(p[p.length-1])>1&&p.push(t.virtualSize-n)}if(l&&i.loop){const w=h[0]+g;if(i.slidesPerGroup>1){const M=Math.ceil((t.virtual.slidesBefore+t.virtual.slidesAfter)/i.slidesPerGroup),I=w*i.slidesPerGroup;for(let P=0;P<M;P+=1)p.push(p[p.length-1]+I)}for(let M=0;M<t.virtual.slidesBefore+t.virtual.slidesAfter;M+=1)i.slidesPerGroup===1&&p.push(p[p.length-1]+w),u.push(u[u.length-1]+w),t.virtualSize+=w}if(p.length===0&&(p=[0]),g!==0){const w=t.isHorizontal()&&a?"marginLeft":t.getDirectionLabel("marginRight");d.filter((M,I)=>!i.cssMode||i.loop?!0:I!==d.length-1).forEach(M=>{M.style[w]=`${g}px`})}if(i.centeredSlides&&i.centeredSlidesBounds){let w=0;h.forEach(I=>{w+=I+(g||0)}),w-=g;const M=w>n?w-n:0;p=p.map(I=>I<=0?-v:I>M?M+E:I)}if(i.centerInsufficientSlides){let w=0;h.forEach(I=>{w+=I+(g||0)}),w-=g;const M=(i.slidesOffsetBefore||0)+(i.slidesOffsetAfter||0);if(w+M<n){const I=(n-w-M)/2;p.forEach((P,A)=>{p[A]=P-I}),u.forEach((P,A)=>{u[A]=P+I})}}if(Object.assign(t,{slides:d,snapGrid:p,slidesGrid:u,slidesSizesGrid:h}),i.centeredSlides&&i.cssMode&&!i.centeredSlidesBounds){Z(s,"--swiper-centered-offset-before",`${-p[0]}px`),Z(s,"--swiper-centered-offset-after",`${t.size/2-h[h.length-1]/2}px`);const w=-t.snapGrid[0],M=-t.slidesGrid[0];t.snapGrid=t.snapGrid.map(I=>I+w),t.slidesGrid=t.slidesGrid.map(I=>I+M)}if(c!==f&&t.emit("slidesLengthChange"),p.length!==m&&(t.params.watchOverflow&&t.checkOverflow(),t.emit("snapGridLengthChange")),u.length!==S&&t.emit("slidesGridLengthChange"),i.watchSlidesProgress&&t.updateSlidesOffset(),t.emit("slidesUpdated"),!l&&!i.cssMode&&(i.effect==="slide"||i.effect==="fade")){const w=`${i.containerModifierClass}backface-hidden`,M=t.el.classList.contains(w);c<=i.maxBackfaceHiddenSlides?M||t.el.classList.add(w):M&&t.el.classList.remove(w)}}function pt(t){const e=this,i=[],s=e.virtual&&e.params.virtual.enabled;let r=0,n;typeof t=="number"?e.setTransition(t):t===!0&&e.setTransition(e.params.speed);const a=o=>s?e.slides[e.getSlideIndexByData(o)]:e.slides[o];if(e.params.slidesPerView!=="auto"&&e.params.slidesPerView>1)if(e.params.centeredSlides)(e.visibleSlides||[]).forEach(o=>{i.push(o)});else for(n=0;n<Math.ceil(e.params.slidesPerView);n+=1){const o=e.activeIndex+n;if(o>e.slides.length&&!s)break;i.push(a(o))}else i.push(a(e.activeIndex));for(n=0;n<i.length;n+=1)if(typeof i[n]<"u"){const o=i[n].offsetHeight;r=o>r?o:r}(r||r===0)&&(e.wrapperEl.style.height=`${r}px`)}function mt(){const t=this,e=t.slides,i=t.isElement?t.isHorizontal()?t.wrapperEl.offsetLeft:t.wrapperEl.offsetTop:0;for(let s=0;s<e.length;s+=1)e[s].swiperSlideOffset=(t.isHorizontal()?e[s].offsetLeft:e[s].offsetTop)-i-t.cssOverflowAdjustment()}const xe=(t,e,i)=>{e&&!t.classList.contains(i)?t.classList.add(i):!e&&t.classList.contains(i)&&t.classList.remove(i)};function ht(t){t===void 0&&(t=this&&this.translate||0);const e=this,i=e.params,{slides:s,rtlTranslate:r,snapGrid:n}=e;if(s.length===0)return;typeof s[0].swiperSlideOffset>"u"&&e.updateSlidesOffset();let a=-t;r&&(a=t),e.visibleSlidesIndexes=[],e.visibleSlides=[];let o=i.spaceBetween;typeof o=="string"&&o.indexOf("%")>=0?o=parseFloat(o.replace("%",""))/100*e.size:typeof o=="string"&&(o=parseFloat(o));for(let l=0;l<s.length;l+=1){const f=s[l];let d=f.swiperSlideOffset;i.cssMode&&i.centeredSlides&&(d-=s[0].swiperSlideOffset);const c=(a+(i.centeredSlides?e.minTranslate():0)-d)/(f.swiperSlideSize+o),p=(a-n[0]+(i.centeredSlides?e.minTranslate():0)-d)/(f.swiperSlideSize+o),u=-(a-d),h=u+e.slidesSizesGrid[l],v=u>=0&&u<=e.size-e.slidesSizesGrid[l],E=u>=0&&u<e.size-1||h>1&&h<=e.size||u<=0&&h>=e.size;E&&(e.visibleSlides.push(f),e.visibleSlidesIndexes.push(l)),xe(f,E,i.slideVisibleClass),xe(f,v,i.slideFullyVisibleClass),f.progress=r?-c:c,f.originalProgress=r?-p:p}}function gt(t){const e=this;if(typeof t>"u"){const d=e.rtlTranslate?-1:1;t=e&&e.translate&&e.translate*d||0}const i=e.params,s=e.maxTranslate()-e.minTranslate();let{progress:r,isBeginning:n,isEnd:a,progressLoop:o}=e;const l=n,f=a;if(s===0)r=0,n=!0,a=!0;else{r=(t-e.minTranslate())/s;const d=Math.abs(t-e.minTranslate())<1,c=Math.abs(t-e.maxTranslate())<1;n=d||r<=0,a=c||r>=1,d&&(r=0),c&&(r=1)}if(i.loop){const d=e.getSlideIndexByData(0),c=e.getSlideIndexByData(e.slides.length-1),p=e.slidesGrid[d],u=e.slidesGrid[c],h=e.slidesGrid[e.slidesGrid.length-1],v=Math.abs(t);v>=p?o=(v-p)/h:o=(v+h-u)/h,o>1&&(o-=1)}Object.assign(e,{progress:r,progressLoop:o,isBeginning:n,isEnd:a}),(i.watchSlidesProgress||i.centeredSlides&&i.autoHeight)&&e.updateSlidesProgress(t),n&&!l&&e.emit("reachBeginning toEdge"),a&&!f&&e.emit("reachEnd toEdge"),(l&&!n||f&&!a)&&e.emit("fromEdge"),e.emit("progress",r)}const oe=(t,e,i)=>{e&&!t.classList.contains(i)?t.classList.add(i):!e&&t.classList.contains(i)&&t.classList.remove(i)};function vt(){const t=this,{slides:e,params:i,slidesEl:s,activeIndex:r}=t,n=t.virtual&&i.virtual.enabled,a=t.grid&&i.grid&&i.grid.rows>1,o=c=>k(s,`.${i.slideClass}${c}, swiper-slide${c}`)[0];let l,f,d;if(n)if(i.loop){let c=r-t.virtual.slidesBefore;c<0&&(c=t.virtual.slides.length+c),c>=t.virtual.slides.length&&(c-=t.virtual.slides.length),l=o(`[data-swiper-slide-index="${c}"]`)}else l=o(`[data-swiper-slide-index="${r}"]`);else a?(l=e.find(c=>c.column===r),d=e.find(c=>c.column===r+1),f=e.find(c=>c.column===r-1)):l=e[r];l&&(a||(d=it(l,`.${i.slideClass}, swiper-slide`)[0],i.loop&&!d&&(d=e[0]),f=tt(l,`.${i.slideClass}, swiper-slide`)[0],i.loop&&!f===0&&(f=e[e.length-1]))),e.forEach(c=>{oe(c,c===l,i.slideActiveClass),oe(c,c===d,i.slideNextClass),oe(c,c===f,i.slidePrevClass)}),t.emitSlidesClasses()}const J=(t,e)=>{if(!t||t.destroyed||!t.params)return;const i=()=>t.isElement?"swiper-slide":`.${t.params.slideClass}`,s=e.closest(i());if(s){let r=s.querySelector(`.${t.params.lazyPreloaderClass}`);!r&&t.isElement&&(s.shadowRoot?r=s.shadowRoot.querySelector(`.${t.params.lazyPreloaderClass}`):requestAnimationFrame(()=>{s.shadowRoot&&(r=s.shadowRoot.querySelector(`.${t.params.lazyPreloaderClass}`),r&&r.remove())})),r&&r.remove()}},ae=(t,e)=>{if(!t.slides[e])return;const i=t.slides[e].querySelector('[loading="lazy"]');i&&i.removeAttribute("loading")},pe=t=>{if(!t||t.destroyed||!t.params)return;let e=t.params.lazyPreloadPrevNext;const i=t.slides.length;if(!i||!e||e<0)return;e=Math.min(e,i);const s=t.params.slidesPerView==="auto"?t.slidesPerViewDynamic():Math.ceil(t.params.slidesPerView),r=t.activeIndex;if(t.params.grid&&t.params.grid.rows>1){const a=r,o=[a-e];o.push(...Array.from({length:e}).map((l,f)=>a+s+f)),t.slides.forEach((l,f)=>{o.includes(l.column)&&ae(t,f)});return}const n=r+s-1;if(t.params.rewind||t.params.loop)for(let a=r-e;a<=n+e;a+=1){const o=(a%i+i)%i;(o<r||o>n)&&ae(t,o)}else for(let a=Math.max(r-e,0);a<=Math.min(n+e,i-1);a+=1)a!==r&&(a>n||a<r)&&ae(t,a)};function wt(t){const{slidesGrid:e,params:i}=t,s=t.rtlTranslate?t.translate:-t.translate;let r;for(let n=0;n<e.length;n+=1)typeof e[n+1]<"u"?s>=e[n]&&s<e[n+1]-(e[n+1]-e[n])/2?r=n:s>=e[n]&&s<e[n+1]&&(r=n+1):s>=e[n]&&(r=n);return i.normalizeSlideIndex&&(r<0||typeof r>"u")&&(r=0),r}function St(t){const e=this,i=e.rtlTranslate?e.translate:-e.translate,{snapGrid:s,params:r,activeIndex:n,realIndex:a,snapIndex:o}=e;let l=t,f;const d=u=>{let h=u-e.virtual.slidesBefore;return h<0&&(h=e.virtual.slides.length+h),h>=e.virtual.slides.length&&(h-=e.virtual.slides.length),h};if(typeof l>"u"&&(l=wt(e)),s.indexOf(i)>=0)f=s.indexOf(i);else{const u=Math.min(r.slidesPerGroupSkip,l);f=u+Math.floor((l-u)/r.slidesPerGroup)}if(f>=s.length&&(f=s.length-1),l===n&&!e.params.loop){f!==o&&(e.snapIndex=f,e.emit("snapIndexChange"));return}if(l===n&&e.params.loop&&e.virtual&&e.params.virtual.enabled){e.realIndex=d(l);return}const c=e.grid&&r.grid&&r.grid.rows>1;let p;if(e.virtual&&r.virtual.enabled&&r.loop)p=d(l);else if(c){const u=e.slides.find(v=>v.column===l);let h=parseInt(u.getAttribute("data-swiper-slide-index"),10);Number.isNaN(h)&&(h=Math.max(e.slides.indexOf(u),0)),p=Math.floor(h/r.grid.rows)}else if(e.slides[l]){const u=e.slides[l].getAttribute("data-swiper-slide-index");u?p=parseInt(u,10):p=l}else p=l;Object.assign(e,{previousSnapIndex:o,snapIndex:f,previousRealIndex:a,realIndex:p,previousIndex:n,activeIndex:l}),e.initialized&&pe(e),e.emit("activeIndexChange"),e.emit("snapIndexChange"),(e.initialized||e.params.runCallbacksOnInit)&&(a!==p&&e.emit("realIndexChange"),e.emit("slideChange"))}function xt(t,e){const i=this,s=i.params;let r=t.closest(`.${s.slideClass}, swiper-slide`);!r&&i.isElement&&e&&e.length>1&&e.includes(t)&&[...e.slice(e.indexOf(t)+1,e.length)].forEach(o=>{!r&&o.matches&&o.matches(`.${s.slideClass}, swiper-slide`)&&(r=o)});let n=!1,a;if(r){for(let o=0;o<i.slides.length;o+=1)if(i.slides[o]===r){n=!0,a=o;break}}if(r&&n)i.clickedSlide=r,i.virtual&&i.params.virtual.enabled?i.clickedIndex=parseInt(r.getAttribute("data-swiper-slide-index"),10):i.clickedIndex=a;else{i.clickedSlide=void 0,i.clickedIndex=void 0;return}s.slideToClickedSlide&&i.clickedIndex!==void 0&&i.clickedIndex!==i.activeIndex&&i.slideToClickedSlide()}var Tt={updateSize:ft,updateSlides:ut,updateAutoHeight:pt,updateSlidesOffset:mt,updateSlidesProgress:ht,updateProgress:gt,updateSlidesClasses:vt,updateActiveIndex:St,updateClickedSlide:xt};function bt(t){t===void 0&&(t=this.isHorizontal()?"x":"y");const e=this,{params:i,rtlTranslate:s,translate:r,wrapperEl:n}=e;if(i.virtualTranslate)return s?-r:r;if(i.cssMode)return r;let a=Qe(n,t);return a+=e.cssOverflowAdjustment(),s&&(a=-a),a||0}function Et(t,e){const i=this,{rtlTranslate:s,params:r,wrapperEl:n,progress:a}=i;let o=0,l=0;const f=0;i.isHorizontal()?o=s?-t:t:l=t,r.roundLengths&&(o=Math.floor(o),l=Math.floor(l)),i.previousTranslate=i.translate,i.translate=i.isHorizontal()?o:l,r.cssMode?n[i.isHorizontal()?"scrollLeft":"scrollTop"]=i.isHorizontal()?-o:-l:r.virtualTranslate||(i.isHorizontal()?o-=i.cssOverflowAdjustment():l-=i.cssOverflowAdjustment(),n.style.transform=`translate3d(${o}px, ${l}px, ${f}px)`);let d;const c=i.maxTranslate()-i.minTranslate();c===0?d=0:d=(t-i.minTranslate())/c,d!==a&&i.updateProgress(t),i.emit("setTranslate",i.translate,e)}function yt(){return-this.snapGrid[0]}function Mt(){return-this.snapGrid[this.snapGrid.length-1]}function Pt(t,e,i,s,r){t===void 0&&(t=0),e===void 0&&(e=this.params.speed),i===void 0&&(i=!0),s===void 0&&(s=!0);const n=this,{params:a,wrapperEl:o}=n;if(n.animating&&a.preventInteractionOnTransition)return!1;const l=n.minTranslate(),f=n.maxTranslate();let d;if(s&&t>l?d=l:s&&t<f?d=f:d=t,n.updateProgress(d),a.cssMode){const c=n.isHorizontal();if(e===0)o[c?"scrollLeft":"scrollTop"]=-d;else{if(!n.support.smoothScroll)return Pe({swiper:n,targetPosition:-d,side:c?"left":"top"}),!0;o.scrollTo({[c?"left":"top"]:-d,behavior:"smooth"})}return!0}return e===0?(n.setTransition(0),n.setTranslate(d),i&&(n.emit("beforeTransitionStart",e,r),n.emit("transitionEnd"))):(n.setTransition(e),n.setTranslate(d),i&&(n.emit("beforeTransitionStart",e,r),n.emit("transitionStart")),n.animating||(n.animating=!0,n.onTranslateToWrapperTransitionEnd||(n.onTranslateToWrapperTransitionEnd=function(p){!n||n.destroyed||p.target===this&&(n.wrapperEl.removeEventListener("transitionend",n.onTranslateToWrapperTransitionEnd),n.onTranslateToWrapperTransitionEnd=null,delete n.onTranslateToWrapperTransitionEnd,n.animating=!1,i&&n.emit("transitionEnd"))}),n.wrapperEl.addEventListener("transitionend",n.onTranslateToWrapperTransitionEnd))),!0}var Ct={getTranslate:bt,setTranslate:Et,minTranslate:yt,maxTranslate:Mt,translateTo:Pt};function It(t,e){const i=this;i.params.cssMode||(i.wrapperEl.style.transitionDuration=`${t}ms`,i.wrapperEl.style.transitionDelay=t===0?"0ms":""),i.emit("setTransition",t,e)}function Le(t){let{swiper:e,runCallbacks:i,direction:s,step:r}=t;const{activeIndex:n,previousIndex:a}=e;let o=s;if(o||(n>a?o="next":n<a?o="prev":o="reset"),e.emit(`transition${r}`),i&&n!==a){if(o==="reset"){e.emit(`slideResetTransition${r}`);return}e.emit(`slideChangeTransition${r}`),o==="next"?e.emit(`slideNextTransition${r}`):e.emit(`slidePrevTransition${r}`)}}function Ot(t,e){t===void 0&&(t=!0);const i=this,{params:s}=i;s.cssMode||(s.autoHeight&&i.updateAutoHeight(),Le({swiper:i,runCallbacks:t,direction:e,step:"Start"}))}function Lt(t,e){t===void 0&&(t=!0);const i=this,{params:s}=i;i.animating=!1,!s.cssMode&&(i.setTransition(0),Le({swiper:i,runCallbacks:t,direction:e,step:"End"}))}var zt={setTransition:It,transitionStart:Ot,transitionEnd:Lt};function At(t,e,i,s,r){t===void 0&&(t=0),i===void 0&&(i=!0),typeof t=="string"&&(t=parseInt(t,10));const n=this;let a=t;a<0&&(a=0);const{params:o,snapGrid:l,slidesGrid:f,previousIndex:d,activeIndex:c,rtlTranslate:p,wrapperEl:u,enabled:h}=n;if(!h&&!s&&!r||n.destroyed||n.animating&&o.preventInteractionOnTransition)return!1;typeof e>"u"&&(e=n.params.speed);const v=Math.min(n.params.slidesPerGroupSkip,a);let E=v+Math.floor((a-v)/n.params.slidesPerGroup);E>=l.length&&(E=l.length-1);const m=-l[E];if(o.normalizeSlideIndex)for(let O=0;O<f.length;O+=1){const x=-Math.floor(m*100),T=Math.floor(f[O]*100),w=Math.floor(f[O+1]*100);typeof f[O+1]<"u"?x>=T&&x<w-(w-T)/2?a=O:x>=T&&x<w&&(a=O+1):x>=T&&(a=O)}if(n.initialized&&a!==c&&(!n.allowSlideNext&&(p?m>n.translate&&m>n.minTranslate():m<n.translate&&m<n.minTranslate())||!n.allowSlidePrev&&m>n.translate&&m>n.maxTranslate()&&(c||0)!==a))return!1;a!==(d||0)&&i&&n.emit("beforeSlideChangeStart"),n.updateProgress(m);let S;a>c?S="next":a<c?S="prev":S="reset";const g=n.virtual&&n.params.virtual.enabled;if(!(g&&r)&&(p&&-m===n.translate||!p&&m===n.translate))return n.updateActiveIndex(a),o.autoHeight&&n.updateAutoHeight(),n.updateSlidesClasses(),o.effect!=="slide"&&n.setTranslate(m),S!=="reset"&&(n.transitionStart(i,S),n.transitionEnd(i,S)),!1;if(o.cssMode){const O=n.isHorizontal(),x=p?m:-m;if(e===0)g&&(n.wrapperEl.style.scrollSnapType="none",n._immediateVirtual=!0),g&&!n._cssModeVirtualInitialSet&&n.params.initialSlide>0?(n._cssModeVirtualInitialSet=!0,requestAnimationFrame(()=>{u[O?"scrollLeft":"scrollTop"]=x})):u[O?"scrollLeft":"scrollTop"]=x,g&&requestAnimationFrame(()=>{n.wrapperEl.style.scrollSnapType="",n._immediateVirtual=!1});else{if(!n.support.smoothScroll)return Pe({swiper:n,targetPosition:x,side:O?"left":"top"}),!0;u.scrollTo({[O?"left":"top"]:x,behavior:"smooth"})}return!0}const L=Oe().isSafari;return g&&!r&&L&&n.isElement&&n.virtual.update(!1,!1,a),n.setTransition(e),n.setTranslate(m),n.updateActiveIndex(a),n.updateSlidesClasses(),n.emit("beforeTransitionStart",e,s),n.transitionStart(i,S),e===0?n.transitionEnd(i,S):n.animating||(n.animating=!0,n.onSlideToWrapperTransitionEnd||(n.onSlideToWrapperTransitionEnd=function(x){!n||n.destroyed||x.target===this&&(n.wrapperEl.removeEventListener("transitionend",n.onSlideToWrapperTransitionEnd),n.onSlideToWrapperTransitionEnd=null,delete n.onSlideToWrapperTransitionEnd,n.transitionEnd(i,S))}),n.wrapperEl.addEventListener("transitionend",n.onSlideToWrapperTransitionEnd)),!0}function _t(t,e,i,s){t===void 0&&(t=0),i===void 0&&(i=!0),typeof t=="string"&&(t=parseInt(t,10));const r=this;if(r.destroyed)return;typeof e>"u"&&(e=r.params.speed);const n=r.grid&&r.params.grid&&r.params.grid.rows>1;let a=t;if(r.params.loop)if(r.virtual&&r.params.virtual.enabled)a=a+r.virtual.slidesBefore;else{let o;if(n){const p=a*r.params.grid.rows;o=r.slides.find(u=>u.getAttribute("data-swiper-slide-index")*1===p).column}else o=r.getSlideIndexByData(a);const l=n?Math.ceil(r.slides.length/r.params.grid.rows):r.slides.length,{centeredSlides:f}=r.params;let d=r.params.slidesPerView;d==="auto"?d=r.slidesPerViewDynamic():(d=Math.ceil(parseFloat(r.params.slidesPerView,10)),f&&d%2===0&&(d=d+1));let c=l-o<d;if(f&&(c=c||o<Math.ceil(d/2)),s&&f&&r.params.slidesPerView!=="auto"&&!n&&(c=!1),c){const p=f?o<r.activeIndex?"prev":"next":o-r.activeIndex-1<r.params.slidesPerView?"next":"prev";r.loopFix({direction:p,slideTo:!0,activeSlideIndex:p==="next"?o+1:o-l+1,slideRealIndex:p==="next"?r.realIndex:void 0})}if(n){const p=a*r.params.grid.rows;a=r.slides.find(u=>u.getAttribute("data-swiper-slide-index")*1===p).column}else a=r.getSlideIndexByData(a)}return requestAnimationFrame(()=>{r.slideTo(a,e,i,s)}),r}function Nt(t,e,i){e===void 0&&(e=!0);const s=this,{enabled:r,params:n,animating:a}=s;if(!r||s.destroyed)return s;typeof t>"u"&&(t=s.params.speed);let o=n.slidesPerGroup;n.slidesPerView==="auto"&&n.slidesPerGroup===1&&n.slidesPerGroupAuto&&(o=Math.max(s.slidesPerViewDynamic("current",!0),1));const l=s.activeIndex<n.slidesPerGroupSkip?1:o,f=s.virtual&&n.virtual.enabled;if(n.loop){if(a&&!f&&n.loopPreventsSliding)return!1;if(s.loopFix({direction:"next"}),s._clientLeft=s.wrapperEl.clientLeft,s.activeIndex===s.slides.length-1&&n.cssMode)return requestAnimationFrame(()=>{s.slideTo(s.activeIndex+l,t,e,i)}),!0}return n.rewind&&s.isEnd?s.slideTo(0,t,e,i):s.slideTo(s.activeIndex+l,t,e,i)}function Gt(t,e,i){e===void 0&&(e=!0);const s=this,{params:r,snapGrid:n,slidesGrid:a,rtlTranslate:o,enabled:l,animating:f}=s;if(!l||s.destroyed)return s;typeof t>"u"&&(t=s.params.speed);const d=s.virtual&&r.virtual.enabled;if(r.loop){if(f&&!d&&r.loopPreventsSliding)return!1;s.loopFix({direction:"prev"}),s._clientLeft=s.wrapperEl.clientLeft}const c=o?s.translate:-s.translate;function p(S){return S<0?-Math.floor(Math.abs(S)):Math.floor(S)}const u=p(c),h=n.map(S=>p(S)),v=r.freeMode&&r.freeMode.enabled;let E=n[h.indexOf(u)-1];if(typeof E>"u"&&(r.cssMode||v)){let S;n.forEach((g,y)=>{u>=g&&(S=y)}),typeof S<"u"&&(E=v?n[S]:n[S>0?S-1:S])}let m=0;if(typeof E<"u"&&(m=a.indexOf(E),m<0&&(m=s.activeIndex-1),r.slidesPerView==="auto"&&r.slidesPerGroup===1&&r.slidesPerGroupAuto&&(m=m-s.slidesPerViewDynamic("previous",!0)+1,m=Math.max(m,0))),r.rewind&&s.isBeginning){const S=s.params.virtual&&s.params.virtual.enabled&&s.virtual?s.virtual.slides.length-1:s.slides.length-1;return s.slideTo(S,t,e,i)}else if(r.loop&&s.activeIndex===0&&r.cssMode)return requestAnimationFrame(()=>{s.slideTo(m,t,e,i)}),!0;return s.slideTo(m,t,e,i)}function Dt(t,e,i){e===void 0&&(e=!0);const s=this;if(!s.destroyed)return typeof t>"u"&&(t=s.params.speed),s.slideTo(s.activeIndex,t,e,i)}function Vt(t,e,i,s){e===void 0&&(e=!0),s===void 0&&(s=.5);const r=this;if(r.destroyed)return;typeof t>"u"&&(t=r.params.speed);let n=r.activeIndex;const a=Math.min(r.params.slidesPerGroupSkip,n),o=a+Math.floor((n-a)/r.params.slidesPerGroup),l=r.rtlTranslate?r.translate:-r.translate;if(l>=r.snapGrid[o]){const f=r.snapGrid[o],d=r.snapGrid[o+1];l-f>(d-f)*s&&(n+=r.params.slidesPerGroup)}else{const f=r.snapGrid[o-1],d=r.snapGrid[o];l-f<=(d-f)*s&&(n-=r.params.slidesPerGroup)}return n=Math.max(n,0),n=Math.min(n,r.slidesGrid.length-1),r.slideTo(n,t,e,i)}function jt(){const t=this;if(t.destroyed)return;const{params:e,slidesEl:i}=t,s=e.slidesPerView==="auto"?t.slidesPerViewDynamic():e.slidesPerView;let r=t.clickedIndex,n;const a=t.isElement?"swiper-slide":`.${e.slideClass}`;if(e.loop){if(t.animating)return;n=parseInt(t.clickedSlide.getAttribute("data-swiper-slide-index"),10),e.centeredSlides?r<t.loopedSlides-s/2||r>t.slides.length-t.loopedSlides+s/2?(t.loopFix(),r=t.getSlideIndex(k(i,`${a}[data-swiper-slide-index="${n}"]`)[0]),fe(()=>{t.slideTo(r)})):t.slideTo(r):r>t.slides.length-s?(t.loopFix(),r=t.getSlideIndex(k(i,`${a}[data-swiper-slide-index="${n}"]`)[0]),fe(()=>{t.slideTo(r)})):t.slideTo(r)}else t.slideTo(r)}var Bt={slideTo:At,slideToLoop:_t,slideNext:Nt,slidePrev:Gt,slideReset:Dt,slideToClosest:Vt,slideToClickedSlide:jt};function Rt(t){const e=this,{params:i,slidesEl:s}=e;if(!i.loop||e.virtual&&e.params.virtual.enabled)return;const r=()=>{k(s,`.${i.slideClass}, swiper-slide`).forEach((c,p)=>{c.setAttribute("data-swiper-slide-index",p)})},n=e.grid&&i.grid&&i.grid.rows>1,a=i.slidesPerGroup*(n?i.grid.rows:1),o=e.slides.length%a!==0,l=n&&e.slides.length%i.grid.rows!==0,f=d=>{for(let c=0;c<d;c+=1){const p=e.isElement?ue("swiper-slide",[i.slideBlankClass]):ue("div",[i.slideClass,i.slideBlankClass]);e.slidesEl.append(p)}};if(o){if(i.loopAddBlankSlides){const d=a-e.slides.length%a;f(d),e.recalcSlides(),e.updateSlides()}else ee("Swiper Loop Warning: The number of slides is not even to slidesPerGroup, loop mode may not function properly. You need to add more slides (or make duplicates, or empty slides)");r()}else if(l){if(i.loopAddBlankSlides){const d=i.grid.rows-e.slides.length%i.grid.rows;f(d),e.recalcSlides(),e.updateSlides()}else ee("Swiper Loop Warning: The number of slides is not even to grid.rows, loop mode may not function properly. You need to add more slides (or make duplicates, or empty slides)");r()}else r();e.loopFix({slideRealIndex:t,direction:i.centeredSlides?void 0:"next"})}function Ft(t){let{slideRealIndex:e,slideTo:i=!0,direction:s,setTranslate:r,activeSlideIndex:n,byController:a,byMousewheel:o}=t===void 0?{}:t;const l=this;if(!l.params.loop)return;l.emit("beforeLoopFix");const{slides:f,allowSlidePrev:d,allowSlideNext:c,slidesEl:p,params:u}=l,{centeredSlides:h}=u;if(l.allowSlidePrev=!0,l.allowSlideNext=!0,l.virtual&&u.virtual.enabled){i&&(!u.centeredSlides&&l.snapIndex===0?l.slideTo(l.virtual.slides.length,0,!1,!0):u.centeredSlides&&l.snapIndex<u.slidesPerView?l.slideTo(l.virtual.slides.length+l.snapIndex,0,!1,!0):l.snapIndex===l.snapGrid.length-1&&l.slideTo(l.virtual.slidesBefore,0,!1,!0)),l.allowSlidePrev=d,l.allowSlideNext=c,l.emit("loopFix");return}let v=u.slidesPerView;v==="auto"?v=l.slidesPerViewDynamic():(v=Math.ceil(parseFloat(u.slidesPerView,10)),h&&v%2===0&&(v=v+1));const E=u.slidesPerGroupAuto?v:u.slidesPerGroup;let m=E;m%E!==0&&(m+=E-m%E),m+=u.loopAdditionalSlides,l.loopedSlides=m;const S=l.grid&&u.grid&&u.grid.rows>1;f.length<v+m?ee("Swiper Loop Warning: The number of slides is not enough for loop mode, it will be disabled and not function properly. You need to add more slides (or make duplicates) or lower the values of slidesPerView and slidesPerGroup parameters"):S&&u.grid.fill==="row"&&ee("Swiper Loop Warning: Loop mode is not compatible with grid.fill = `row`");const g=[],y=[];let b=l.activeIndex;typeof n>"u"?n=l.getSlideIndex(f.find(P=>P.classList.contains(u.slideActiveClass))):b=n;const L=s==="next"||!s,O=s==="prev"||!s;let x=0,T=0;const w=S?Math.ceil(f.length/u.grid.rows):f.length,I=(S?f[n].column:n)+(h&&typeof r>"u"?-v/2+.5:0);if(I<m){x=Math.max(m-I,E);for(let P=0;P<m-I;P+=1){const A=P-Math.floor(P/w)*w;if(S){const N=w-A-1;for(let R=f.length-1;R>=0;R-=1)f[R].column===N&&g.push(R)}else g.push(w-A-1)}}else if(I+v>w-m){T=Math.max(I-(w-m*2),E);for(let P=0;P<T;P+=1){const A=P-Math.floor(P/w)*w;S?f.forEach((N,R)=>{N.column===A&&y.push(R)}):y.push(A)}}if(l.__preventObserver__=!0,requestAnimationFrame(()=>{l.__preventObserver__=!1}),O&&g.forEach(P=>{f[P].swiperLoopMoveDOM=!0,p.prepend(f[P]),f[P].swiperLoopMoveDOM=!1}),L&&y.forEach(P=>{f[P].swiperLoopMoveDOM=!0,p.append(f[P]),f[P].swiperLoopMoveDOM=!1}),l.recalcSlides(),u.slidesPerView==="auto"?l.updateSlides():S&&(g.length>0&&O||y.length>0&&L)&&l.slides.forEach((P,A)=>{l.grid.updateSlide(A,P,l.slides)}),u.watchSlidesProgress&&l.updateSlidesOffset(),i){if(g.length>0&&O){if(typeof e>"u"){const P=l.slidesGrid[b],N=l.slidesGrid[b+x]-P;o?l.setTranslate(l.translate-N):(l.slideTo(b+Math.ceil(x),0,!1,!0),r&&(l.touchEventsData.startTranslate=l.touchEventsData.startTranslate-N,l.touchEventsData.currentTranslate=l.touchEventsData.currentTranslate-N))}else if(r){const P=S?g.length/u.grid.rows:g.length;l.slideTo(l.activeIndex+P,0,!1,!0),l.touchEventsData.currentTranslate=l.translate}}else if(y.length>0&&L)if(typeof e>"u"){const P=l.slidesGrid[b],N=l.slidesGrid[b-T]-P;o?l.setTranslate(l.translate-N):(l.slideTo(b-T,0,!1,!0),r&&(l.touchEventsData.startTranslate=l.touchEventsData.startTranslate-N,l.touchEventsData.currentTranslate=l.touchEventsData.currentTranslate-N))}else{const P=S?y.length/u.grid.rows:y.length;l.slideTo(l.activeIndex-P,0,!1,!0)}}if(l.allowSlidePrev=d,l.allowSlideNext=c,l.controller&&l.controller.control&&!a){const P={slideRealIndex:e,direction:s,setTranslate:r,activeSlideIndex:n,byController:!0};Array.isArray(l.controller.control)?l.controller.control.forEach(A=>{!A.destroyed&&A.params.loop&&A.loopFix({...P,slideTo:A.params.slidesPerView===u.slidesPerView?i:!1})}):l.controller.control instanceof l.constructor&&l.controller.control.params.loop&&l.controller.control.loopFix({...P,slideTo:l.controller.control.params.slidesPerView===u.slidesPerView?i:!1})}l.emit("loopFix")}function kt(){const t=this,{params:e,slidesEl:i}=t;if(!e.loop||!i||t.virtual&&t.params.virtual.enabled)return;t.recalcSlides();const s=[];t.slides.forEach(r=>{const n=typeof r.swiperSlideIndex>"u"?r.getAttribute("data-swiper-slide-index")*1:r.swiperSlideIndex;s[n]=r}),t.slides.forEach(r=>{r.removeAttribute("data-swiper-slide-index")}),s.forEach(r=>{i.append(r)}),t.recalcSlides(),t.slideTo(t.realIndex,0)}var $t={loopCreate:Rt,loopFix:Ft,loopDestroy:kt};function Ht(t){const e=this;if(!e.params.simulateTouch||e.params.watchOverflow&&e.isLocked||e.params.cssMode)return;const i=e.params.touchEventsTarget==="container"?e.el:e.wrapperEl;e.isElement&&(e.__preventObserver__=!0),i.style.cursor="move",i.style.cursor=t?"grabbing":"grab",e.isElement&&requestAnimationFrame(()=>{e.__preventObserver__=!1})}function Wt(){const t=this;t.params.watchOverflow&&t.isLocked||t.params.cssMode||(t.isElement&&(t.__preventObserver__=!0),t[t.params.touchEventsTarget==="container"?"el":"wrapperEl"].style.cursor="",t.isElement&&requestAnimationFrame(()=>{t.__preventObserver__=!1}))}var qt={setGrabCursor:Ht,unsetGrabCursor:Wt};function Yt(t,e){e===void 0&&(e=this);function i(s){if(!s||s===q()||s===V())return null;s.assignedSlot&&(s=s.assignedSlot);const r=s.closest(t);return!r&&!s.getRootNode?null:r||i(s.getRootNode().host)}return i(e)}function Te(t,e,i){const s=V(),{params:r}=t,n=r.edgeSwipeDetection,a=r.edgeSwipeThreshold;return n&&(i<=a||i>=s.innerWidth-a)?n==="prevent"?(e.preventDefault(),!0):!1:!0}function Xt(t){const e=this,i=q();let s=t;s.originalEvent&&(s=s.originalEvent);const r=e.touchEventsData;if(s.type==="pointerdown"){if(r.pointerId!==null&&r.pointerId!==s.pointerId)return;r.pointerId=s.pointerId}else s.type==="touchstart"&&s.targetTouches.length===1&&(r.touchId=s.targetTouches[0].identifier);if(s.type==="touchstart"){Te(e,s,s.targetTouches[0].pageX);return}const{params:n,touches:a,enabled:o}=e;if(!o||!n.simulateTouch&&s.pointerType==="mouse"||e.animating&&n.preventInteractionOnTransition)return;!e.animating&&n.cssMode&&n.loop&&e.loopFix();let l=s.target;if(n.touchEventsTarget==="wrapper"&&!et(l,e.wrapperEl)||"which"in s&&s.which===3||"button"in s&&s.button>0||r.isTouched&&r.isMoved)return;const f=!!n.noSwipingClass&&n.noSwipingClass!=="",d=s.composedPath?s.composedPath():s.path;f&&s.target&&s.target.shadowRoot&&d&&(l=d[0]);const c=n.noSwipingSelector?n.noSwipingSelector:`.${n.noSwipingClass}`,p=!!(s.target&&s.target.shadowRoot);if(n.noSwiping&&(p?Yt(c,l):l.closest(c))){e.allowClick=!0;return}if(n.swipeHandler&&!l.closest(n.swipeHandler))return;a.currentX=s.pageX,a.currentY=s.pageY;const u=a.currentX,h=a.currentY;if(!Te(e,s,u))return;Object.assign(r,{isTouched:!0,isMoved:!1,allowTouchCallbacks:!0,isScrolling:void 0,startMoving:void 0}),a.startX=u,a.startY=h,r.touchStartTime=H(),e.allowClick=!0,e.updateSize(),e.swipeDirection=void 0,n.threshold>0&&(r.allowThresholdMove=!1);let v=!0;l.matches(r.focusableElements)&&(v=!1,l.nodeName==="SELECT"&&(r.isTouched=!1)),i.activeElement&&i.activeElement.matches(r.focusableElements)&&i.activeElement!==l&&(s.pointerType==="mouse"||s.pointerType!=="mouse"&&!l.matches(r.focusableElements))&&i.activeElement.blur();const E=v&&e.allowTouchMove&&n.touchStartPreventDefault;(n.touchStartForcePreventDefault||E)&&!l.isContentEditable&&s.preventDefault(),n.freeMode&&n.freeMode.enabled&&e.freeMode&&e.animating&&!n.cssMode&&e.freeMode.onTouchStart(),e.emit("touchStart",s)}function Kt(t){const e=q(),i=this,s=i.touchEventsData,{params:r,touches:n,rtlTranslate:a,enabled:o}=i;if(!o||!r.simulateTouch&&t.pointerType==="mouse")return;let l=t;if(l.originalEvent&&(l=l.originalEvent),l.type==="pointermove"&&(s.touchId!==null||l.pointerId!==s.pointerId))return;let f;if(l.type==="touchmove"){if(f=[...l.changedTouches].find(b=>b.identifier===s.touchId),!f||f.identifier!==s.touchId)return}else f=l;if(!s.isTouched){s.startMoving&&s.isScrolling&&i.emit("touchMoveOpposite",l);return}const d=f.pageX,c=f.pageY;if(l.preventedByNestedSwiper){n.startX=d,n.startY=c;return}if(!i.allowTouchMove){l.target.matches(s.focusableElements)||(i.allowClick=!1),s.isTouched&&(Object.assign(n,{startX:d,startY:c,currentX:d,currentY:c}),s.touchStartTime=H());return}if(r.touchReleaseOnEdges&&!r.loop){if(i.isVertical()){if(c<n.startY&&i.translate<=i.maxTranslate()||c>n.startY&&i.translate>=i.minTranslate()){s.isTouched=!1,s.isMoved=!1;return}}else if(d<n.startX&&i.translate<=i.maxTranslate()||d>n.startX&&i.translate>=i.minTranslate())return}if(e.activeElement&&e.activeElement.matches(s.focusableElements)&&e.activeElement!==l.target&&l.pointerType!=="mouse"&&e.activeElement.blur(),e.activeElement&&l.target===e.activeElement&&l.target.matches(s.focusableElements)){s.isMoved=!0,i.allowClick=!1;return}s.allowTouchCallbacks&&i.emit("touchMove",l),n.previousX=n.currentX,n.previousY=n.currentY,n.currentX=d,n.currentY=c;const p=n.currentX-n.startX,u=n.currentY-n.startY;if(i.params.threshold&&Math.sqrt(p**2+u**2)<i.params.threshold)return;if(typeof s.isScrolling>"u"){let b;i.isHorizontal()&&n.currentY===n.startY||i.isVertical()&&n.currentX===n.startX?s.isScrolling=!1:p*p+u*u>=25&&(b=Math.atan2(Math.abs(u),Math.abs(p))*180/Math.PI,s.isScrolling=i.isHorizontal()?b>r.touchAngle:90-b>r.touchAngle)}if(s.isScrolling&&i.emit("touchMoveOpposite",l),typeof s.startMoving>"u"&&(n.currentX!==n.startX||n.currentY!==n.startY)&&(s.startMoving=!0),s.isScrolling||l.type==="touchmove"&&s.preventTouchMoveFromPointerMove){s.isTouched=!1;return}if(!s.startMoving)return;i.allowClick=!1,!r.cssMode&&l.cancelable&&l.preventDefault(),r.touchMoveStopPropagation&&!r.nested&&l.stopPropagation();let h=i.isHorizontal()?p:u,v=i.isHorizontal()?n.currentX-n.previousX:n.currentY-n.previousY;r.oneWayMovement&&(h=Math.abs(h)*(a?1:-1),v=Math.abs(v)*(a?1:-1)),n.diff=h,h*=r.touchRatio,a&&(h=-h,v=-v);const E=i.touchesDirection;i.swipeDirection=h>0?"prev":"next",i.touchesDirection=v>0?"prev":"next";const m=i.params.loop&&!r.cssMode,S=i.touchesDirection==="next"&&i.allowSlideNext||i.touchesDirection==="prev"&&i.allowSlidePrev;if(!s.isMoved){if(m&&S&&i.loopFix({direction:i.swipeDirection}),s.startTranslate=i.getTranslate(),i.setTransition(0),i.animating){const b=new window.CustomEvent("transitionend",{bubbles:!0,cancelable:!0,detail:{bySwiperTouchMove:!0}});i.wrapperEl.dispatchEvent(b)}s.allowMomentumBounce=!1,r.grabCursor&&(i.allowSlideNext===!0||i.allowSlidePrev===!0)&&i.setGrabCursor(!0),i.emit("sliderFirstMove",l)}if(new Date().getTime(),r._loopSwapReset!==!1&&s.isMoved&&s.allowThresholdMove&&E!==i.touchesDirection&&m&&S&&Math.abs(h)>=1){Object.assign(n,{startX:d,startY:c,currentX:d,currentY:c,startTranslate:s.currentTranslate}),s.loopSwapReset=!0,s.startTranslate=s.currentTranslate;return}i.emit("sliderMove",l),s.isMoved=!0,s.currentTranslate=h+s.startTranslate;let g=!0,y=r.resistanceRatio;if(r.touchReleaseOnEdges&&(y=0),h>0?(m&&S&&s.allowThresholdMove&&s.currentTranslate>(r.centeredSlides?i.minTranslate()-i.slidesSizesGrid[i.activeIndex+1]-(r.slidesPerView!=="auto"&&i.slides.length-r.slidesPerView>=2?i.slidesSizesGrid[i.activeIndex+1]+i.params.spaceBetween:0)-i.params.spaceBetween:i.minTranslate())&&i.loopFix({direction:"prev",setTranslate:!0,activeSlideIndex:0}),s.currentTranslate>i.minTranslate()&&(g=!1,r.resistance&&(s.currentTranslate=i.minTranslate()-1+(-i.minTranslate()+s.startTranslate+h)**y))):h<0&&(m&&S&&s.allowThresholdMove&&s.currentTranslate<(r.centeredSlides?i.maxTranslate()+i.slidesSizesGrid[i.slidesSizesGrid.length-1]+i.params.spaceBetween+(r.slidesPerView!=="auto"&&i.slides.length-r.slidesPerView>=2?i.slidesSizesGrid[i.slidesSizesGrid.length-1]+i.params.spaceBetween:0):i.maxTranslate())&&i.loopFix({direction:"next",setTranslate:!0,activeSlideIndex:i.slides.length-(r.slidesPerView==="auto"?i.slidesPerViewDynamic():Math.ceil(parseFloat(r.slidesPerView,10)))}),s.currentTranslate<i.maxTranslate()&&(g=!1,r.resistance&&(s.currentTranslate=i.maxTranslate()+1-(i.maxTranslate()-s.startTranslate-h)**y))),g&&(l.preventedByNestedSwiper=!0),!i.allowSlideNext&&i.swipeDirection==="next"&&s.currentTranslate<s.startTranslate&&(s.currentTranslate=s.startTranslate),!i.allowSlidePrev&&i.swipeDirection==="prev"&&s.currentTranslate>s.startTranslate&&(s.currentTranslate=s.startTranslate),!i.allowSlidePrev&&!i.allowSlideNext&&(s.currentTranslate=s.startTranslate),r.threshold>0)if(Math.abs(h)>r.threshold||s.allowThresholdMove){if(!s.allowThresholdMove){s.allowThresholdMove=!0,n.startX=n.currentX,n.startY=n.currentY,s.currentTranslate=s.startTranslate,n.diff=i.isHorizontal()?n.currentX-n.startX:n.currentY-n.startY;return}}else{s.currentTranslate=s.startTranslate;return}!r.followFinger||r.cssMode||((r.freeMode&&r.freeMode.enabled&&i.freeMode||r.watchSlidesProgress)&&(i.updateActiveIndex(),i.updateSlidesClasses()),r.freeMode&&r.freeMode.enabled&&i.freeMode&&i.freeMode.onTouchMove(),i.updateProgress(s.currentTranslate),i.setTranslate(s.currentTranslate))}function Ut(t){const e=this,i=e.touchEventsData;let s=t;s.originalEvent&&(s=s.originalEvent);let r;if(s.type==="touchend"||s.type==="touchcancel"){if(r=[...s.changedTouches].find(b=>b.identifier===i.touchId),!r||r.identifier!==i.touchId)return}else{if(i.touchId!==null||s.pointerId!==i.pointerId)return;r=s}if(["pointercancel","pointerout","pointerleave","contextmenu"].includes(s.type)&&!(["pointercancel","contextmenu"].includes(s.type)&&(e.browser.isSafari||e.browser.isWebView)))return;i.pointerId=null,i.touchId=null;const{params:a,touches:o,rtlTranslate:l,slidesGrid:f,enabled:d}=e;if(!d||!a.simulateTouch&&s.pointerType==="mouse")return;if(i.allowTouchCallbacks&&e.emit("touchEnd",s),i.allowTouchCallbacks=!1,!i.isTouched){i.isMoved&&a.grabCursor&&e.setGrabCursor(!1),i.isMoved=!1,i.startMoving=!1;return}a.grabCursor&&i.isMoved&&i.isTouched&&(e.allowSlideNext===!0||e.allowSlidePrev===!0)&&e.setGrabCursor(!1);const c=H(),p=c-i.touchStartTime;if(e.allowClick){const b=s.path||s.composedPath&&s.composedPath();e.updateClickedSlide(b&&b[0]||s.target,b),e.emit("tap click",s),p<300&&c-i.lastClickTime<300&&e.emit("doubleTap doubleClick",s)}if(i.lastClickTime=H(),fe(()=>{e.destroyed||(e.allowClick=!0)}),!i.isTouched||!i.isMoved||!e.swipeDirection||o.diff===0&&!i.loopSwapReset||i.currentTranslate===i.startTranslate&&!i.loopSwapReset){i.isTouched=!1,i.isMoved=!1,i.startMoving=!1;return}i.isTouched=!1,i.isMoved=!1,i.startMoving=!1;let u;if(a.followFinger?u=l?e.translate:-e.translate:u=-i.currentTranslate,a.cssMode)return;if(a.freeMode&&a.freeMode.enabled){e.freeMode.onTouchEnd({currentPos:u});return}const h=u>=-e.maxTranslate()&&!e.params.loop;let v=0,E=e.slidesSizesGrid[0];for(let b=0;b<f.length;b+=b<a.slidesPerGroupSkip?1:a.slidesPerGroup){const L=b<a.slidesPerGroupSkip-1?1:a.slidesPerGroup;typeof f[b+L]<"u"?(h||u>=f[b]&&u<f[b+L])&&(v=b,E=f[b+L]-f[b]):(h||u>=f[b])&&(v=b,E=f[f.length-1]-f[f.length-2])}let m=null,S=null;a.rewind&&(e.isBeginning?S=a.virtual&&a.virtual.enabled&&e.virtual?e.virtual.slides.length-1:e.slides.length-1:e.isEnd&&(m=0));const g=(u-f[v])/E,y=v<a.slidesPerGroupSkip-1?1:a.slidesPerGroup;if(p>a.longSwipesMs){if(!a.longSwipes){e.slideTo(e.activeIndex);return}e.swipeDirection==="next"&&(g>=a.longSwipesRatio?e.slideTo(a.rewind&&e.isEnd?m:v+y):e.slideTo(v)),e.swipeDirection==="prev"&&(g>1-a.longSwipesRatio?e.slideTo(v+y):S!==null&&g<0&&Math.abs(g)>a.longSwipesRatio?e.slideTo(S):e.slideTo(v))}else{if(!a.shortSwipes){e.slideTo(e.activeIndex);return}e.navigation&&(s.target===e.navigation.nextEl||s.target===e.navigation.prevEl)?s.target===e.navigation.nextEl?e.slideTo(v+y):e.slideTo(v):(e.swipeDirection==="next"&&e.slideTo(m!==null?m:v+y),e.swipeDirection==="prev"&&e.slideTo(S!==null?S:v))}}function be(){const t=this,{params:e,el:i}=t;if(i&&i.offsetWidth===0)return;e.breakpoints&&t.setBreakpoint();const{allowSlideNext:s,allowSlidePrev:r,snapGrid:n}=t,a=t.virtual&&t.params.virtual.enabled;t.allowSlideNext=!0,t.allowSlidePrev=!0,t.updateSize(),t.updateSlides(),t.updateSlidesClasses();const o=a&&e.loop;(e.slidesPerView==="auto"||e.slidesPerView>1)&&t.isEnd&&!t.isBeginning&&!t.params.centeredSlides&&!o?t.slideTo(t.slides.length-1,0,!1,!0):t.params.loop&&!a?t.slideToLoop(t.realIndex,0,!1,!0):t.slideTo(t.activeIndex,0,!1,!0),t.autoplay&&t.autoplay.running&&t.autoplay.paused&&(clearTimeout(t.autoplay.resizeTimeout),t.autoplay.resizeTimeout=setTimeout(()=>{t.autoplay&&t.autoplay.running&&t.autoplay.paused&&t.autoplay.resume()},500)),t.allowSlidePrev=r,t.allowSlideNext=s,t.params.watchOverflow&&n!==t.snapGrid&&t.checkOverflow()}function Qt(t){const e=this;e.enabled&&(e.allowClick||(e.params.preventClicks&&t.preventDefault(),e.params.preventClicksPropagation&&e.animating&&(t.stopPropagation(),t.stopImmediatePropagation())))}function Zt(){const t=this,{wrapperEl:e,rtlTranslate:i,enabled:s}=t;if(!s)return;t.previousTranslate=t.translate,t.isHorizontal()?t.translate=-e.scrollLeft:t.translate=-e.scrollTop,t.translate===0&&(t.translate=0),t.updateActiveIndex(),t.updateSlidesClasses();let r;const n=t.maxTranslate()-t.minTranslate();n===0?r=0:r=(t.translate-t.minTranslate())/n,r!==t.progress&&t.updateProgress(i?-t.translate:t.translate),t.emit("setTranslate",t.translate,!1)}function Jt(t){const e=this;J(e,t.target),!(e.params.cssMode||e.params.slidesPerView!=="auto"&&!e.params.autoHeight)&&e.update()}function ei(){const t=this;t.documentTouchHandlerProceeded||(t.documentTouchHandlerProceeded=!0,t.params.touchReleaseOnEdges&&(t.el.style.touchAction="auto"))}const ze=(t,e)=>{const i=q(),{params:s,el:r,wrapperEl:n,device:a}=t,o=!!s.nested,l=e==="on"?"addEventListener":"removeEventListener",f=e;!r||typeof r=="string"||(i[l]("touchstart",t.onDocumentTouchStart,{passive:!1,capture:o}),r[l]("touchstart",t.onTouchStart,{passive:!1}),r[l]("pointerdown",t.onTouchStart,{passive:!1}),i[l]("touchmove",t.onTouchMove,{passive:!1,capture:o}),i[l]("pointermove",t.onTouchMove,{passive:!1,capture:o}),i[l]("touchend",t.onTouchEnd,{passive:!0}),i[l]("pointerup",t.onTouchEnd,{passive:!0}),i[l]("pointercancel",t.onTouchEnd,{passive:!0}),i[l]("touchcancel",t.onTouchEnd,{passive:!0}),i[l]("pointerout",t.onTouchEnd,{passive:!0}),i[l]("pointerleave",t.onTouchEnd,{passive:!0}),i[l]("contextmenu",t.onTouchEnd,{passive:!0}),(s.preventClicks||s.preventClicksPropagation)&&r[l]("click",t.onClick,!0),s.cssMode&&n[l]("scroll",t.onScroll),s.updateOnWindowResize?t[f](a.ios||a.android?"resize orientationchange observerUpdate":"resize observerUpdate",be,!0):t[f]("observerUpdate",be,!0),r[l]("load",t.onLoad,{capture:!0}))};function ti(){const t=this,{params:e}=t;t.onTouchStart=Xt.bind(t),t.onTouchMove=Kt.bind(t),t.onTouchEnd=Ut.bind(t),t.onDocumentTouchStart=ei.bind(t),e.cssMode&&(t.onScroll=Zt.bind(t)),t.onClick=Qt.bind(t),t.onLoad=Jt.bind(t),ze(t,"on")}function ii(){ze(this,"off")}var si={attachEvents:ti,detachEvents:ii};const Ee=(t,e)=>t.grid&&e.grid&&e.grid.rows>1;function ri(){const t=this,{realIndex:e,initialized:i,params:s,el:r}=t,n=s.breakpoints;if(!n||n&&Object.keys(n).length===0)return;const a=q(),o=s.breakpointsBase==="window"||!s.breakpointsBase?s.breakpointsBase:"container",l=["window","container"].includes(s.breakpointsBase)||!s.breakpointsBase?t.el:a.querySelector(s.breakpointsBase),f=t.getBreakpoint(n,o,l);if(!f||t.currentBreakpoint===f)return;const c=(f in n?n[f]:void 0)||t.originalParams,p=Ee(t,s),u=Ee(t,c),h=t.params.grabCursor,v=c.grabCursor,E=s.enabled;p&&!u?(r.classList.remove(`${s.containerModifierClass}grid`,`${s.containerModifierClass}grid-column`),t.emitContainerClasses()):!p&&u&&(r.classList.add(`${s.containerModifierClass}grid`),(c.grid.fill&&c.grid.fill==="column"||!c.grid.fill&&s.grid.fill==="column")&&r.classList.add(`${s.containerModifierClass}grid-column`),t.emitContainerClasses()),h&&!v?t.unsetGrabCursor():!h&&v&&t.setGrabCursor(),["navigation","pagination","scrollbar"].forEach(L=>{if(typeof c[L]>"u")return;const O=s[L]&&s[L].enabled,x=c[L]&&c[L].enabled;O&&!x&&t[L].disable(),!O&&x&&t[L].enable()});const m=c.direction&&c.direction!==s.direction,S=s.loop&&(c.slidesPerView!==s.slidesPerView||m),g=s.loop;m&&i&&t.changeDirection(),j(t.params,c);const y=t.params.enabled,b=t.params.loop;Object.assign(t,{allowTouchMove:t.params.allowTouchMove,allowSlideNext:t.params.allowSlideNext,allowSlidePrev:t.params.allowSlidePrev}),E&&!y?t.disable():!E&&y&&t.enable(),t.currentBreakpoint=f,t.emit("_beforeBreakpoint",c),i&&(S?(t.loopDestroy(),t.loopCreate(e),t.updateSlides()):!g&&b?(t.loopCreate(e),t.updateSlides()):g&&!b&&t.loopDestroy()),t.emit("breakpoint",c)}function ni(t,e,i){if(e===void 0&&(e="window"),!t||e==="container"&&!i)return;let s=!1;const r=V(),n=e==="window"?r.innerHeight:i.clientHeight,a=Object.keys(t).map(o=>{if(typeof o=="string"&&o.indexOf("@")===0){const l=parseFloat(o.substr(1));return{value:n*l,point:o}}return{value:o,point:o}});a.sort((o,l)=>parseInt(o.value,10)-parseInt(l.value,10));for(let o=0;o<a.length;o+=1){const{point:l,value:f}=a[o];e==="window"?r.matchMedia(`(min-width: ${f}px)`).matches&&(s=l):f<=i.clientWidth&&(s=l)}return s||"max"}var li={setBreakpoint:ri,getBreakpoint:ni};function oi(t,e){const i=[];return t.forEach(s=>{typeof s=="object"?Object.keys(s).forEach(r=>{s[r]&&i.push(e+r)}):typeof s=="string"&&i.push(e+s)}),i}function ai(){const t=this,{classNames:e,params:i,rtl:s,el:r,device:n}=t,a=oi(["initialized",i.direction,{"free-mode":t.params.freeMode&&i.freeMode.enabled},{autoheight:i.autoHeight},{rtl:s},{grid:i.grid&&i.grid.rows>1},{"grid-column":i.grid&&i.grid.rows>1&&i.grid.fill==="column"},{android:n.android},{ios:n.ios},{"css-mode":i.cssMode},{centered:i.cssMode&&i.centeredSlides},{"watch-progress":i.watchSlidesProgress}],i.containerModifierClass);e.push(...a),r.classList.add(...e),t.emitContainerClasses()}function di(){const t=this,{el:e,classNames:i}=t;!e||typeof e=="string"||(e.classList.remove(...i),t.emitContainerClasses())}var ci={addClasses:ai,removeClasses:di};function fi(){const t=this,{isLocked:e,params:i}=t,{slidesOffsetBefore:s}=i;if(s){const r=t.slides.length-1,n=t.slidesGrid[r]+t.slidesSizesGrid[r]+s*2;t.isLocked=t.size>n}else t.isLocked=t.snapGrid.length===1;i.allowSlideNext===!0&&(t.allowSlideNext=!t.isLocked),i.allowSlidePrev===!0&&(t.allowSlidePrev=!t.isLocked),e&&e!==t.isLocked&&(t.isEnd=!1),e!==t.isLocked&&t.emit(t.isLocked?"lock":"unlock")}var ui={checkOverflow:fi},me={init:!0,direction:"horizontal",oneWayMovement:!1,swiperElementNodeName:"SWIPER-CONTAINER",touchEventsTarget:"wrapper",initialSlide:0,speed:300,cssMode:!1,updateOnWindowResize:!0,resizeObserver:!0,nested:!1,createElements:!1,eventsPrefix:"swiper",enabled:!0,focusableElements:"input, select, option, textarea, button, video, label",width:null,height:null,preventInteractionOnTransition:!1,userAgent:null,url:null,edgeSwipeDetection:!1,edgeSwipeThreshold:20,autoHeight:!1,setWrapperSize:!1,virtualTranslate:!1,effect:"slide",breakpoints:void 0,breakpointsBase:"window",spaceBetween:0,slidesPerView:1,slidesPerGroup:1,slidesPerGroupSkip:0,slidesPerGroupAuto:!1,centeredSlides:!1,centeredSlidesBounds:!1,slidesOffsetBefore:0,slidesOffsetAfter:0,normalizeSlideIndex:!0,centerInsufficientSlides:!1,watchOverflow:!0,roundLengths:!1,touchRatio:1,touchAngle:45,simulateTouch:!0,shortSwipes:!0,longSwipes:!0,longSwipesRatio:.5,longSwipesMs:300,followFinger:!0,allowTouchMove:!0,threshold:5,touchMoveStopPropagation:!1,touchStartPreventDefault:!0,touchStartForcePreventDefault:!1,touchReleaseOnEdges:!1,uniqueNavElements:!0,resistance:!0,resistanceRatio:.85,watchSlidesProgress:!1,grabCursor:!1,preventClicks:!0,preventClicksPropagation:!0,slideToClickedSlide:!1,loop:!1,loopAddBlankSlides:!0,loopAdditionalSlides:0,loopPreventsSliding:!0,rewind:!1,allowSlidePrev:!0,allowSlideNext:!0,swipeHandler:null,noSwiping:!0,noSwipingClass:"swiper-no-swiping",noSwipingSelector:null,passiveListeners:!0,maxBackfaceHiddenSlides:10,containerModifierClass:"swiper-",slideClass:"swiper-slide",slideBlankClass:"swiper-slide-blank",slideActiveClass:"swiper-slide-active",slideVisibleClass:"swiper-slide-visible",slideFullyVisibleClass:"swiper-slide-fully-visible",slideNextClass:"swiper-slide-next",slidePrevClass:"swiper-slide-prev",wrapperClass:"swiper-wrapper",lazyPreloaderClass:"swiper-lazy-preloader",lazyPreloadPrevNext:0,runCallbacksOnInit:!0,_emitClasses:!1};function pi(t,e){return function(s){s===void 0&&(s={});const r=Object.keys(s)[0],n=s[r];if(typeof n!="object"||n===null){j(e,s);return}if(t[r]===!0&&(t[r]={enabled:!0}),r==="navigation"&&t[r]&&t[r].enabled&&!t[r].prevEl&&!t[r].nextEl&&(t[r].auto=!0),["pagination","scrollbar"].indexOf(r)>=0&&t[r]&&t[r].enabled&&!t[r].el&&(t[r].auto=!0),!(r in t&&"enabled"in n)){j(e,s);return}typeof t[r]=="object"&&!("enabled"in t[r])&&(t[r].enabled=!0),t[r]||(t[r]={enabled:!1}),j(e,s)}}const de={eventsEmitter:ct,update:Tt,translate:Ct,transition:zt,slide:Bt,loop:$t,grabCursor:qt,events:si,breakpoints:li,checkOverflow:ui,classes:ci},ce={};let ge=class F{constructor(){let e,i;for(var s=arguments.length,r=new Array(s),n=0;n<s;n++)r[n]=arguments[n];r.length===1&&r[0].constructor&&Object.prototype.toString.call(r[0]).slice(8,-1)==="Object"?i=r[0]:[e,i]=r,i||(i={}),i=j({},i),e&&!i.el&&(i.el=e);const a=q();if(i.el&&typeof i.el=="string"&&a.querySelectorAll(i.el).length>1){const d=[];return a.querySelectorAll(i.el).forEach(c=>{const p=j({},i,{el:c});d.push(new F(p))}),d}const o=this;o.__swiper__=!0,o.support=Ce(),o.device=Ie({userAgent:i.userAgent}),o.browser=Oe(),o.eventsListeners={},o.eventsAnyListeners=[],o.modules=[...o.__modules__],i.modules&&Array.isArray(i.modules)&&o.modules.push(...i.modules);const l={};o.modules.forEach(d=>{d({params:i,swiper:o,extendParams:pi(i,l),on:o.on.bind(o),once:o.once.bind(o),off:o.off.bind(o),emit:o.emit.bind(o)})});const f=j({},me,l);return o.params=j({},f,ce,i),o.originalParams=j({},o.params),o.passedParams=j({},i),o.params&&o.params.on&&Object.keys(o.params.on).forEach(d=>{o.on(d,o.params.on[d])}),o.params&&o.params.onAny&&o.onAny(o.params.onAny),Object.assign(o,{enabled:o.params.enabled,el:e,classNames:[],slides:[],slidesGrid:[],snapGrid:[],slidesSizesGrid:[],isHorizontal(){return o.params.direction==="horizontal"},isVertical(){return o.params.direction==="vertical"},activeIndex:0,realIndex:0,isBeginning:!0,isEnd:!1,translate:0,previousTranslate:0,progress:0,velocity:0,animating:!1,cssOverflowAdjustment(){return Math.trunc(this.translate/2**23)*2**23},allowSlideNext:o.params.allowSlideNext,allowSlidePrev:o.params.allowSlidePrev,touchEventsData:{isTouched:void 0,isMoved:void 0,allowTouchCallbacks:void 0,touchStartTime:void 0,isScrolling:void 0,currentTranslate:void 0,startTranslate:void 0,allowThresholdMove:void 0,focusableElements:o.params.focusableElements,lastClickTime:0,clickTimeout:void 0,velocities:[],allowMomentumBounce:void 0,startMoving:void 0,pointerId:null,touchId:null},allowClick:!0,allowTouchMove:o.params.allowTouchMove,touches:{startX:0,startY:0,currentX:0,currentY:0,diff:0},imagesToLoad:[],imagesLoaded:0}),o.emit("_swiper"),o.params.init&&o.init(),o}getDirectionLabel(e){return this.isHorizontal()?e:{width:"height","margin-top":"margin-left","margin-bottom ":"margin-right","margin-left":"margin-top","margin-right":"margin-bottom","padding-left":"padding-top","padding-right":"padding-bottom",marginRight:"marginBottom"}[e]}getSlideIndex(e){const{slidesEl:i,params:s}=this,r=k(i,`.${s.slideClass}, swiper-slide`),n=we(r[0]);return we(e)-n}getSlideIndexByData(e){return this.getSlideIndex(this.slides.find(i=>i.getAttribute("data-swiper-slide-index")*1===e))}recalcSlides(){const e=this,{slidesEl:i,params:s}=e;e.slides=k(i,`.${s.slideClass}, swiper-slide`)}enable(){const e=this;e.enabled||(e.enabled=!0,e.params.grabCursor&&e.setGrabCursor(),e.emit("enable"))}disable(){const e=this;e.enabled&&(e.enabled=!1,e.params.grabCursor&&e.unsetGrabCursor(),e.emit("disable"))}setProgress(e,i){const s=this;e=Math.min(Math.max(e,0),1);const r=s.minTranslate(),a=(s.maxTranslate()-r)*e+r;s.translateTo(a,typeof i>"u"?0:i),s.updateActiveIndex(),s.updateSlidesClasses()}emitContainerClasses(){const e=this;if(!e.params._emitClasses||!e.el)return;const i=e.el.className.split(" ").filter(s=>s.indexOf("swiper")===0||s.indexOf(e.params.containerModifierClass)===0);e.emit("_containerClasses",i.join(" "))}getSlideClasses(e){const i=this;return i.destroyed?"":e.className.split(" ").filter(s=>s.indexOf("swiper-slide")===0||s.indexOf(i.params.slideClass)===0).join(" ")}emitSlidesClasses(){const e=this;if(!e.params._emitClasses||!e.el)return;const i=[];e.slides.forEach(s=>{const r=e.getSlideClasses(s);i.push({slideEl:s,classNames:r}),e.emit("_slideClass",s,r)}),e.emit("_slideClasses",i)}slidesPerViewDynamic(e,i){e===void 0&&(e="current"),i===void 0&&(i=!1);const s=this,{params:r,slides:n,slidesGrid:a,slidesSizesGrid:o,size:l,activeIndex:f}=s;let d=1;if(typeof r.slidesPerView=="number")return r.slidesPerView;if(r.centeredSlides){let c=n[f]?Math.ceil(n[f].swiperSlideSize):0,p;for(let u=f+1;u<n.length;u+=1)n[u]&&!p&&(c+=Math.ceil(n[u].swiperSlideSize),d+=1,c>l&&(p=!0));for(let u=f-1;u>=0;u-=1)n[u]&&!p&&(c+=n[u].swiperSlideSize,d+=1,c>l&&(p=!0))}else if(e==="current")for(let c=f+1;c<n.length;c+=1)(i?a[c]+o[c]-a[f]<l:a[c]-a[f]<l)&&(d+=1);else for(let c=f-1;c>=0;c-=1)a[f]-a[c]<l&&(d+=1);return d}update(){const e=this;if(!e||e.destroyed)return;const{snapGrid:i,params:s}=e;s.breakpoints&&e.setBreakpoint(),[...e.el.querySelectorAll('[loading="lazy"]')].forEach(a=>{a.complete&&J(e,a)}),e.updateSize(),e.updateSlides(),e.updateProgress(),e.updateSlidesClasses();function r(){const a=e.rtlTranslate?e.translate*-1:e.translate,o=Math.min(Math.max(a,e.maxTranslate()),e.minTranslate());e.setTranslate(o),e.updateActiveIndex(),e.updateSlidesClasses()}let n;if(s.freeMode&&s.freeMode.enabled&&!s.cssMode)r(),s.autoHeight&&e.updateAutoHeight();else{if((s.slidesPerView==="auto"||s.slidesPerView>1)&&e.isEnd&&!s.centeredSlides){const a=e.virtual&&s.virtual.enabled?e.virtual.slides:e.slides;n=e.slideTo(a.length-1,0,!1,!0)}else n=e.slideTo(e.activeIndex,0,!1,!0);n||r()}s.watchOverflow&&i!==e.snapGrid&&e.checkOverflow(),e.emit("update")}changeDirection(e,i){i===void 0&&(i=!0);const s=this,r=s.params.direction;return e||(e=r==="horizontal"?"vertical":"horizontal"),e===r||e!=="horizontal"&&e!=="vertical"||(s.el.classList.remove(`${s.params.containerModifierClass}${r}`),s.el.classList.add(`${s.params.containerModifierClass}${e}`),s.emitContainerClasses(),s.params.direction=e,s.slides.forEach(n=>{e==="vertical"?n.style.width="":n.style.height=""}),s.emit("changeDirection"),i&&s.update()),s}changeLanguageDirection(e){const i=this;i.rtl&&e==="rtl"||!i.rtl&&e==="ltr"||(i.rtl=e==="rtl",i.rtlTranslate=i.params.direction==="horizontal"&&i.rtl,i.rtl?(i.el.classList.add(`${i.params.containerModifierClass}rtl`),i.el.dir="rtl"):(i.el.classList.remove(`${i.params.containerModifierClass}rtl`),i.el.dir="ltr"),i.update())}mount(e){const i=this;if(i.mounted)return!0;let s=e||i.params.el;if(typeof s=="string"&&(s=document.querySelector(s)),!s)return!1;s.swiper=i,s.parentNode&&s.parentNode.host&&s.parentNode.host.nodeName===i.params.swiperElementNodeName.toUpperCase()&&(i.isElement=!0);const r=()=>`.${(i.params.wrapperClass||"").trim().split(" ").join(".")}`;let a=s&&s.shadowRoot&&s.shadowRoot.querySelector?s.shadowRoot.querySelector(r()):k(s,r())[0];return!a&&i.params.createElements&&(a=ue("div",i.params.wrapperClass),s.append(a),k(s,`.${i.params.slideClass}`).forEach(o=>{a.append(o)})),Object.assign(i,{el:s,wrapperEl:a,slidesEl:i.isElement&&!s.parentNode.host.slideSlots?s.parentNode.host:a,hostEl:i.isElement?s.parentNode.host:s,mounted:!0,rtl:s.dir.toLowerCase()==="rtl"||$(s,"direction")==="rtl",rtlTranslate:i.params.direction==="horizontal"&&(s.dir.toLowerCase()==="rtl"||$(s,"direction")==="rtl"),wrongRTL:$(a,"display")==="-webkit-box"}),!0}init(e){const i=this;if(i.initialized||i.mount(e)===!1)return i;i.emit("beforeInit"),i.params.breakpoints&&i.setBreakpoint(),i.addClasses(),i.updateSize(),i.updateSlides(),i.params.watchOverflow&&i.checkOverflow(),i.params.grabCursor&&i.enabled&&i.setGrabCursor(),i.params.loop&&i.virtual&&i.params.virtual.enabled?i.slideTo(i.params.initialSlide+i.virtual.slidesBefore,0,i.params.runCallbacksOnInit,!1,!0):i.slideTo(i.params.initialSlide,0,i.params.runCallbacksOnInit,!1,!0),i.params.loop&&i.loopCreate(),i.attachEvents();const r=[...i.el.querySelectorAll('[loading="lazy"]')];return i.isElement&&r.push(...i.hostEl.querySelectorAll('[loading="lazy"]')),r.forEach(n=>{n.complete?J(i,n):n.addEventListener("load",a=>{J(i,a.target)})}),pe(i),i.initialized=!0,pe(i),i.emit("init"),i.emit("afterInit"),i}destroy(e,i){e===void 0&&(e=!0),i===void 0&&(i=!0);const s=this,{params:r,el:n,wrapperEl:a,slides:o}=s;return typeof s.params>"u"||s.destroyed||(s.emit("beforeDestroy"),s.initialized=!1,s.detachEvents(),r.loop&&s.loopDestroy(),i&&(s.removeClasses(),n&&typeof n!="string"&&n.removeAttribute("style"),a&&a.removeAttribute("style"),o&&o.length&&o.forEach(l=>{l.classList.remove(r.slideVisibleClass,r.slideFullyVisibleClass,r.slideActiveClass,r.slideNextClass,r.slidePrevClass),l.removeAttribute("style"),l.removeAttribute("data-swiper-slide-index")})),s.emit("destroy"),Object.keys(s.eventsListeners).forEach(l=>{s.off(l)}),e!==!1&&(s.el&&typeof s.el!="string"&&(s.el.swiper=null),Ke(s)),s.destroyed=!0),null}static extendDefaults(e){j(ce,e)}static get extendedDefaults(){return ce}static get defaults(){return me}static installModule(e){F.prototype.__modules__||(F.prototype.__modules__=[]);const i=F.prototype.__modules__;typeof e=="function"&&i.indexOf(e)<0&&i.push(e)}static use(e){return Array.isArray(e)?(e.forEach(i=>F.installModule(i)),F):(F.installModule(e),F)}};Object.keys(de).forEach(t=>{Object.keys(de[t]).forEach(e=>{ge.prototype[e]=de[t][e]})});ge.use([at,dt]);const Ae=["eventsPrefix","injectStyles","injectStylesUrls","modules","init","_direction","oneWayMovement","swiperElementNodeName","touchEventsTarget","initialSlide","_speed","cssMode","updateOnWindowResize","resizeObserver","nested","focusableElements","_enabled","_width","_height","preventInteractionOnTransition","userAgent","url","_edgeSwipeDetection","_edgeSwipeThreshold","_freeMode","_autoHeight","setWrapperSize","virtualTranslate","_effect","breakpoints","breakpointsBase","_spaceBetween","_slidesPerView","maxBackfaceHiddenSlides","_grid","_slidesPerGroup","_slidesPerGroupSkip","_slidesPerGroupAuto","_centeredSlides","_centeredSlidesBounds","_slidesOffsetBefore","_slidesOffsetAfter","normalizeSlideIndex","_centerInsufficientSlides","_watchOverflow","roundLengths","touchRatio","touchAngle","simulateTouch","_shortSwipes","_longSwipes","longSwipesRatio","longSwipesMs","_followFinger","allowTouchMove","_threshold","touchMoveStopPropagation","touchStartPreventDefault","touchStartForcePreventDefault","touchReleaseOnEdges","uniqueNavElements","_resistance","_resistanceRatio","_watchSlidesProgress","_grabCursor","preventClicks","preventClicksPropagation","_slideToClickedSlide","_loop","loopAdditionalSlides","loopAddBlankSlides","loopPreventsSliding","_rewind","_allowSlidePrev","_allowSlideNext","_swipeHandler","_noSwiping","noSwipingClass","noSwipingSelector","passiveListeners","containerModifierClass","slideClass","slideActiveClass","slideVisibleClass","slideFullyVisibleClass","slideNextClass","slidePrevClass","slideBlankClass","wrapperClass","lazyPreloaderClass","lazyPreloadPrevNext","runCallbacksOnInit","observer","observeParents","observeSlideChildren","a11y","_autoplay","_controller","coverflowEffect","cubeEffect","fadeEffect","flipEffect","creativeEffect","cardsEffect","hashNavigation","history","keyboard","mousewheel","_navigation","_pagination","parallax","_scrollbar","_thumbs","virtual","zoom","control"];function W(t){return typeof t=="object"&&t!==null&&t.constructor&&Object.prototype.toString.call(t).slice(8,-1)==="Object"&&!t.__swiper__}function Y(t,e){const i=["__proto__","constructor","prototype"];Object.keys(e).filter(s=>i.indexOf(s)<0).forEach(s=>{typeof t[s]>"u"?t[s]=e[s]:W(e[s])&&W(t[s])&&Object.keys(e[s]).length>0?e[s].__swiper__?t[s]=e[s]:Y(t[s],e[s]):t[s]=e[s]})}function _e(t){return t===void 0&&(t={}),t.navigation&&typeof t.navigation.nextEl>"u"&&typeof t.navigation.prevEl>"u"}function Ne(t){return t===void 0&&(t={}),t.pagination&&typeof t.pagination.el>"u"}function Ge(t){return t===void 0&&(t={}),t.scrollbar&&typeof t.scrollbar.el>"u"}function De(t){t===void 0&&(t="");const e=t.split(" ").map(s=>s.trim()).filter(s=>!!s),i=[];return e.forEach(s=>{i.indexOf(s)<0&&i.push(s)}),i.join(" ")}function mi(t){return t===void 0&&(t=""),t?t.includes("swiper-wrapper")?t:`swiper-wrapper ${t}`:"swiper-wrapper"}function hi(t){let{swiper:e,slides:i,passedParams:s,changedParams:r,nextEl:n,prevEl:a,scrollbarEl:o,paginationEl:l}=t;const f=r.filter(T=>T!=="children"&&T!=="direction"&&T!=="wrapperClass"),{params:d,pagination:c,navigation:p,scrollbar:u,virtual:h,thumbs:v}=e;let E,m,S,g,y,b,L,O;r.includes("thumbs")&&s.thumbs&&s.thumbs.swiper&&!s.thumbs.swiper.destroyed&&d.thumbs&&(!d.thumbs.swiper||d.thumbs.swiper.destroyed)&&(E=!0),r.includes("controller")&&s.controller&&s.controller.control&&d.controller&&!d.controller.control&&(m=!0),r.includes("pagination")&&s.pagination&&(s.pagination.el||l)&&(d.pagination||d.pagination===!1)&&c&&!c.el&&(S=!0),r.includes("scrollbar")&&s.scrollbar&&(s.scrollbar.el||o)&&(d.scrollbar||d.scrollbar===!1)&&u&&!u.el&&(g=!0),r.includes("navigation")&&s.navigation&&(s.navigation.prevEl||a)&&(s.navigation.nextEl||n)&&(d.navigation||d.navigation===!1)&&p&&!p.prevEl&&!p.nextEl&&(y=!0);const x=T=>{e[T]&&(e[T].destroy(),T==="navigation"?(e.isElement&&(e[T].prevEl.remove(),e[T].nextEl.remove()),d[T].prevEl=void 0,d[T].nextEl=void 0,e[T].prevEl=void 0,e[T].nextEl=void 0):(e.isElement&&e[T].el.remove(),d[T].el=void 0,e[T].el=void 0))};r.includes("loop")&&e.isElement&&(d.loop&&!s.loop?b=!0:!d.loop&&s.loop?L=!0:O=!0),f.forEach(T=>{if(W(d[T])&&W(s[T]))Object.assign(d[T],s[T]),(T==="navigation"||T==="pagination"||T==="scrollbar")&&"enabled"in s[T]&&!s[T].enabled&&x(T);else{const w=s[T];(w===!0||w===!1)&&(T==="navigation"||T==="pagination"||T==="scrollbar")?w===!1&&x(T):d[T]=s[T]}}),f.includes("controller")&&!m&&e.controller&&e.controller.control&&d.controller&&d.controller.control&&(e.controller.control=d.controller.control),r.includes("children")&&i&&h&&d.virtual.enabled?(h.slides=i,h.update(!0)):r.includes("virtual")&&h&&d.virtual.enabled&&(i&&(h.slides=i),h.update(!0)),r.includes("children")&&i&&d.loop&&(O=!0),E&&v.init()&&v.update(!0),m&&(e.controller.control=d.controller.control),S&&(e.isElement&&(!l||typeof l=="string")&&(l=document.createElement("div"),l.classList.add("swiper-pagination"),l.part.add("pagination"),e.el.appendChild(l)),l&&(d.pagination.el=l),c.init(),c.render(),c.update()),g&&(e.isElement&&(!o||typeof o=="string")&&(o=document.createElement("div"),o.classList.add("swiper-scrollbar"),o.part.add("scrollbar"),e.el.appendChild(o)),o&&(d.scrollbar.el=o),u.init(),u.updateSize(),u.setTranslate()),y&&(e.isElement&&((!n||typeof n=="string")&&(n=document.createElement("div"),n.classList.add("swiper-button-next"),n.innerHTML=e.hostEl.constructor.nextButtonSvg,n.part.add("button-next"),e.el.appendChild(n)),(!a||typeof a=="string")&&(a=document.createElement("div"),a.classList.add("swiper-button-prev"),a.innerHTML=e.hostEl.constructor.prevButtonSvg,a.part.add("button-prev"),e.el.appendChild(a))),n&&(d.navigation.nextEl=n),a&&(d.navigation.prevEl=a),p.init(),p.update()),r.includes("allowSlideNext")&&(e.allowSlideNext=s.allowSlideNext),r.includes("allowSlidePrev")&&(e.allowSlidePrev=s.allowSlidePrev),r.includes("direction")&&e.changeDirection(s.direction,!1),(b||O)&&e.loopDestroy(),(L||O)&&e.loopCreate(),e.update()}function gi(t,e){t===void 0&&(t={}),e===void 0&&(e=!0);const i={on:{}},s={},r={};Y(i,me),i._emitClasses=!0,i.init=!1;const n={},a=Ae.map(l=>l.replace(/_/,"")),o=Object.assign({},t);return Object.keys(o).forEach(l=>{typeof t[l]>"u"||(a.indexOf(l)>=0?W(t[l])?(i[l]={},r[l]={},Y(i[l],t[l]),Y(r[l],t[l])):(i[l]=t[l],r[l]=t[l]):l.search(/on[A-Z]/)===0&&typeof t[l]=="function"?e?s[`${l[2].toLowerCase()}${l.substr(3)}`]=t[l]:i.on[`${l[2].toLowerCase()}${l.substr(3)}`]=t[l]:n[l]=t[l])}),["navigation","pagination","scrollbar"].forEach(l=>{i[l]===!0&&(i[l]={}),i[l]===!1&&delete i[l]}),{params:i,passedParams:r,rest:n,events:s}}function vi(t,e){let{el:i,nextEl:s,prevEl:r,paginationEl:n,scrollbarEl:a,swiper:o}=t;_e(e)&&s&&r&&(o.params.navigation.nextEl=s,o.originalParams.navigation.nextEl=s,o.params.navigation.prevEl=r,o.originalParams.navigation.prevEl=r),Ne(e)&&n&&(o.params.pagination.el=n,o.originalParams.pagination.el=n),Ge(e)&&a&&(o.params.scrollbar.el=a,o.originalParams.scrollbar.el=a),o.init(i)}function wi(t,e,i,s,r){const n=[];if(!e)return n;const a=l=>{n.indexOf(l)<0&&n.push(l)};if(i&&s){const l=s.map(r),f=i.map(r);l.join("")!==f.join("")&&a("children"),s.length!==i.length&&a("children")}return Ae.filter(l=>l[0]==="_").map(l=>l.replace(/_/,"")).forEach(l=>{if(l in t&&l in e)if(W(t[l])&&W(e[l])){const f=Object.keys(t[l]),d=Object.keys(e[l]);f.length!==d.length?a(l):(f.forEach(c=>{t[l][c]!==e[l][c]&&a(l)}),d.forEach(c=>{t[l][c]!==e[l][c]&&a(l)}))}else t[l]!==e[l]&&a(l)}),n}const Si=t=>{!t||t.destroyed||!t.params.virtual||t.params.virtual&&!t.params.virtual.enabled||(t.updateSlides(),t.updateProgress(),t.updateSlidesClasses(),t.parallax&&t.params.parallax&&t.params.parallax.enabled&&t.parallax.setTranslate())};function te(){return te=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var i=arguments[e];for(var s in i)Object.prototype.hasOwnProperty.call(i,s)&&(t[s]=i[s])}return t},te.apply(this,arguments)}function Ve(t){return t.type&&t.type.displayName&&t.type.displayName.includes("SwiperSlide")}function je(t){const e=[];return _.Children.toArray(t).forEach(i=>{Ve(i)?e.push(i):i.props&&i.props.children&&je(i.props.children).forEach(s=>e.push(s))}),e}function xi(t){const e=[],i={"container-start":[],"container-end":[],"wrapper-start":[],"wrapper-end":[]};return _.Children.toArray(t).forEach(s=>{if(Ve(s))e.push(s);else if(s.props&&s.props.slot&&i[s.props.slot])i[s.props.slot].push(s);else if(s.props&&s.props.children){const r=je(s.props.children);r.length>0?r.forEach(n=>e.push(n)):i["container-end"].push(s)}else i["container-end"].push(s)}),{slides:e,slots:i}}function Ti(t,e,i){if(!i)return null;const s=d=>{let c=d;return d<0?c=e.length+d:c>=e.length&&(c=c-e.length),c},r=t.isHorizontal()?{[t.rtlTranslate?"right":"left"]:`${i.offset}px`}:{top:`${i.offset}px`},{from:n,to:a}=i,o=t.params.loop?-e.length:0,l=t.params.loop?e.length*2:e.length,f=[];for(let d=o;d<l;d+=1)d>=n&&d<=a&&f.push(e[s(d)]);return f.map((d,c)=>_.cloneElement(d,{swiper:t,style:r,key:d.props.virtualIndex||d.key||`slide-${c}`}))}function K(t,e){return typeof window>"u"?z.useEffect(t,e):z.useLayoutEffect(t,e)}const ye=z.createContext(null),bi=z.createContext(null),Be=z.forwardRef(function(t,e){let{className:i,tag:s="div",wrapperTag:r="div",children:n,onSwiper:a,...o}=t===void 0?{}:t,l=!1;const[f,d]=z.useState("swiper"),[c,p]=z.useState(null),[u,h]=z.useState(!1),v=z.useRef(!1),E=z.useRef(null),m=z.useRef(null),S=z.useRef(null),g=z.useRef(null),y=z.useRef(null),b=z.useRef(null),L=z.useRef(null),O=z.useRef(null),{params:x,passedParams:T,rest:w,events:M}=gi(o),{slides:I,slots:P}=xi(n),A=()=>{h(!u)};Object.assign(x.on,{_containerClasses(G,B){d(B)}});const N=()=>{Object.assign(x.on,M),l=!0;const G={...x};if(delete G.wrapperClass,m.current=new ge(G),m.current.virtual&&m.current.params.virtual.enabled){m.current.virtual.slides=I;const B={cache:!1,slides:I,renderExternal:p,renderExternalUpdate:!1};Y(m.current.params.virtual,B),Y(m.current.originalParams.virtual,B)}};E.current||N(),m.current&&m.current.on("_beforeBreakpoint",A);const R=()=>{l||!M||!m.current||Object.keys(M).forEach(G=>{m.current.on(G,M[G])})},ie=()=>{!M||!m.current||Object.keys(M).forEach(G=>{m.current.off(G,M[G])})};z.useEffect(()=>()=>{m.current&&m.current.off("_beforeBreakpoint",A)}),z.useEffect(()=>{!v.current&&m.current&&(m.current.emitSlidesClasses(),v.current=!0)}),K(()=>{if(e&&(e.current=E.current),!!E.current)return m.current.destroyed&&N(),vi({el:E.current,nextEl:y.current,prevEl:b.current,paginationEl:L.current,scrollbarEl:O.current,swiper:m.current},x),a&&!m.current.destroyed&&a(m.current),()=>{m.current&&!m.current.destroyed&&m.current.destroy(!0,!1)}},[]),K(()=>{R();const G=wi(T,S.current,I,g.current,B=>B.key);return S.current=T,g.current=I,G.length&&m.current&&!m.current.destroyed&&hi({swiper:m.current,slides:I,passedParams:T,changedParams:G,nextEl:y.current,prevEl:b.current,scrollbarEl:O.current,paginationEl:L.current}),()=>{ie()}}),K(()=>{Si(m.current)},[c]);function U(){return x.virtual?Ti(m.current,I,c):I.map((G,B)=>_.cloneElement(G,{swiper:m.current,swiperSlideIndex:B}))}return _.createElement(s,te({ref:E,className:De(`${f}${i?` ${i}`:""}`)},w),_.createElement(bi.Provider,{value:m.current},P["container-start"],_.createElement(r,{className:mi(x.wrapperClass)},P["wrapper-start"],U(),P["wrapper-end"]),_e(x)&&_.createElement(_.Fragment,null,_.createElement("div",{ref:b,className:"swiper-button-prev"}),_.createElement("div",{ref:y,className:"swiper-button-next"})),Ge(x)&&_.createElement("div",{ref:O,className:"swiper-scrollbar"}),Ne(x)&&_.createElement("div",{ref:L,className:"swiper-pagination"}),P["container-end"]))});Be.displayName="Swiper";const Re=z.forwardRef(function(t,e){let{tag:i="div",children:s,className:r="",swiper:n,zoom:a,lazy:o,virtualIndex:l,swiperSlideIndex:f,...d}=t===void 0?{}:t;const c=z.useRef(null),[p,u]=z.useState("swiper-slide"),[h,v]=z.useState(!1);function E(y,b,L){b===c.current&&u(L)}K(()=>{if(typeof f<"u"&&(c.current.swiperSlideIndex=f),e&&(e.current=c.current),!(!c.current||!n)){if(n.destroyed){p!=="swiper-slide"&&u("swiper-slide");return}return n.on("_slideClass",E),()=>{n&&n.off("_slideClass",E)}}}),K(()=>{n&&c.current&&!n.destroyed&&u(n.getSlideClasses(c.current))},[n]);const m={isActive:p.indexOf("swiper-slide-active")>=0,isVisible:p.indexOf("swiper-slide-visible")>=0,isPrev:p.indexOf("swiper-slide-prev")>=0,isNext:p.indexOf("swiper-slide-next")>=0},S=()=>typeof s=="function"?s(m):s,g=()=>{v(!0)};return _.createElement(i,te({ref:c,className:De(`${p}${r?` ${r}`:""}`),"data-swiper-slide-index":l,onLoad:g},d),a&&_.createElement(ye.Provider,{value:m},_.createElement("div",{className:"swiper-zoom-container","data-swiper-zoom":typeof a=="number"?a:void 0},S(),o&&!h&&_.createElement("div",{className:"swiper-lazy-preloader"}))),!a&&_.createElement(ye.Provider,{value:m},S(),o&&!h&&_.createElement("div",{className:"swiper-lazy-preloader"})))});Re.displayName="SwiperSlide";function Ei({imgSrcs:t=[]}){return C.jsx("div",{className:"w-full h-full",children:C.jsx(Be,{slidesPerView:"auto",freeMode:!0,modules:[rt],children:t.map((e,i)=>C.jsx(Re,{className:"w-[303px] h-[379px]",children:C.jsx("img",{className:"w-full h-full object-cover",src:e,alt:i})},i))})})}function Ii(){let{id:t}=$e();const e=z.useRef(null),[i,s]=z.useState(D.findIndex(d=>d.id===t)),[r,n]=z.useState(D[i]),[a,o]=z.useState("100%"),l=()=>{s(d=>d+1===D.length?0:d+1)},f=()=>{s(d=>d===0?D.length-1:d-1)};return z.useEffect(()=>{typeof i=="number"&&n(D[i])},[i]),z.useEffect(()=>{if(window.innerWidth>=1024){const d=e.current.offsetHeight;o(`${d}px`)}},[]),C.jsxs("div",{className:"flex flex-col flex-1 justify-between lg:bg-[url('/img/background/circle.jpg')] bg-contain bg-center h-full",children:[C.jsx("div",{className:"w-full flex-1",children:C.jsxs("div",{className:"w-full lg:grid lg:grid-cols-2 sm:flex sm:flex-col lg:gap-[30px] sm:gap-0 gap-[30px] sm:pt-[60px] pt-[30px] xl:px-[60px] sm:px-[40px]",children:[C.jsx("div",{ref:e,className:"w-full h-fit sm:flex hidden sm:items-center sm:justify-center lg:mb-0 sm:mb-[70px]",children:C.jsx("div",{className:"lg:max-w-3/4 sm:max-w-2/3 sm:w-2/3 max-h-full",children:C.jsx(qe,{autoSlide:!1,modalSrcs:r.full,sliderSrcs:r.square})})}),C.jsxs("div",{className:"lg:w-full sm:w-2/3 self-center lg:pl-[30px] sm:p-0 px-10 flex flex-col sm:justify-between gap-[30px]",style:{height:a},children:[C.jsxs("div",{className:"overflow-y-auto w-full flex flex-col gap-[30px]",children:[C.jsx("h2",{className:"lg:w-2/3 sm:w-full sm:font-bold font-semibold sm:text-[32px] text-[21px] leading-tight w-full",children:r.title}),C.jsx("pre",{className:"lg:w-2/3 sm:w-full sm:text-[17px] text-[16px] sm:leading-[21px] leading-tight whitespace-pre-wrap font-sans",children:r.description})]}),C.jsxs("div",{className:"sm:flex hidden justify-start gap-5",children:[C.jsx("button",{children:C.jsx(X,{to:`/magicDetail/${i===0?D[D.length-1].id:D[i-1].id}`,className:"text-[17px] font-medium underline hover:text-[#FF5900] transform duration-500",onClick:f,children:"Previous"})}),C.jsx("span",{className:"font-inter text-[17px] font-semibold flex items-center",children:"|"}),C.jsx("button",{children:C.jsx(X,{to:`/magicDetail/${i+1===D.length?D[0].id:D[i+1].id}`,className:"text-[17px] font-medium underline hover:text-[#FF5900] transform duration-500",onClick:l,children:"Next"})})]})]}),C.jsx("div",{className:"lg:block hidden"}),C.jsx("div",{className:"sm:w-2/3 lg:mt-0 sm:mt-[30px] self-center",children:C.jsx(X,{to:"/magic",className:"sm:flex hidden items-center justify-center w-fit p-5 lg:ml-[30px] box-border border-black border-[1px] bg-black text-white hover:bg-white hover:text-black transform duration-500 text-[17px] font-normal",children:C.jsx("button",{className:"",children:"Back to Magic world"})})}),C.jsx("div",{className:"sm:hidden block w-full h-full mt-[30px]",children:C.jsx(Ei,{imgSrcs:r.square})}),C.jsxs("div",{className:"sm:hidden flex justify-center gap-5 pt-[21px]",children:[C.jsx("button",{children:C.jsx(X,{to:`/magicDetail/${i===0?D[D.length-1].id:D[i-1].id}`,className:"text-[17px] font-medium underline hover:text-[#FF5900] transform duration-500",onClick:f,children:"Previous"})}),C.jsx("span",{className:"font-inter text-[17px] font-semibold flex items-center",children:"|"}),C.jsx("button",{children:C.jsx(X,{to:`/magicDetail/${i+1===D.length?D[0].id:D[i+1].id}`,className:"text-[17px] font-medium underline hover:text-[#FF5900] transform duration-500",onClick:l,children:"Next"})})]})]})}),C.jsx("div",{className:"lg:mt-[60px] sm:mt-10 mt-[30px] w-full",children:C.jsx(We,{})})]})}export{Ii as default};
