import{r,j as t}from"./index-mQoYb2KJ.js";function x({images:s=[],index:l=0,onClose:c=()=>{}}){const[i,n]=r.useState(l),a=()=>{n(e=>e>0?e-1:s.length-1)},h=()=>{n(e=>e<s.length-1?e+1:0)};return t.jsxs("div",{className:"fixed inset-0 bg-black bg-opacity-80 flex items-center justify-around z-50",children:[s.length>1&&t.jsx("button",{className:"lg:m-[50px] m-[20px]",onClick:a,children:t.jsx("img",{className:"min-h-[50px] h-[50px]",src:"/img/icons/thin_white_left.png",alt:"thin_white_left"})}),t.jsx("div",{className:"relative",children:t.jsx("img",{src:s[i],alt:`Slide ${i}`,className:"max-h-[75vh] object-contain"})}),s.length>1&&t.jsx("button",{className:"lg:m-[50px] m-[20px]",onClick:h,children:t.jsx("img",{className:"min-h-[50px] h-[50px]",src:"/img/icons/thin_white_right.png",alt:"thin_white_right"})}),t.jsx("button",{className:"absolute top-0 right-0 m-10",onClick:c,children:t.jsx("img",{className:"w-[50px] aspect-square",src:"/img/icons/thin_white_close.png",alt:"thin_white_close"})})]})}export{x as M};
