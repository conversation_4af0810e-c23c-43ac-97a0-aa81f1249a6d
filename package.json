{"name": "illustration-portfolio", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "devhost": "vite --host", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"framer-motion": "^12.4.3", "node-vibrant": "^4.0.3", "react": "^19.0.0", "react-dom": "^19.0.0", "react-icons": "^5.5.0", "react-router-dom": "^7.1.5", "react-router-hash-link": "^2.4.3", "swiper": "^11.2.5"}, "devDependencies": {"@eslint/js": "^9.19.0", "@types/react": "^19.0.8", "@types/react-dom": "^19.0.3", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.20", "eslint": "^9.19.0", "eslint-plugin-react": "^7.37.4", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.18", "globals": "^15.14.0", "postcss": "^8.5.2", "tailwindcss": "^3.4.17", "vite": "^6.1.0"}}